# SmartBiz Accounting - Static Assets

هذا المجلد يحتوي على الأصول الثابتة (Static Assets) المستخدمة في نظام المحاسبة الذكي SmartBiz Accounting.

## هيكل المجلدات

```
static/
├── css/         # ملفات أنماط CSS
│   └── main.css # ملف CSS الرئيسي للتطبيق
├── js/          # ملفات JavaScript
│   └── main.js  # ملف JavaScript الرئيسي للتطبيق
└── img/         # الصور والأيقونات
    ├── logo.svg             # شعار التطبيق الرئيسي
    ├── favicon.svg          # أيقونة المفضلة للمتصفح
    ├── background.svg       # خلفية التطبيق
    ├── user-icon.svg        # أيقونة المستخدم
    ├── invoice-icon.svg     # أيقونة الفاتورة
    ├── project-icon.svg     # أيقونة المشروع
    ├── client-icon.svg      # أيقونة العميل
    ├── report-icon.svg      # أيقونة التقرير
    ├── dashboard-icon.svg   # أيقونة لوحة التحكم
    └── ai-assistant-icon.svg # أيقونة المساعد الذكي
```

## ملف CSS الرئيسي

ملف `main.css` يحتوي على جميع أنماط التطبيق، بما في ذلك:

- متغيرات CSS للألوان والخطوط والحدود
- الأنماط العامة للتطبيق
- أنماط العناصر الأساسية (الأزرار، النماذج، الجداول، البطاقات)
- أنماط مخصصة للوحة التحكم والفواتير والعملاء والمشاريع
- أنماط المساعد الذكي
- تعديلات للغة العربية (RTL)
- تعديلات للطباعة

## ملف JavaScript الرئيسي

ملف `main.js` يحتوي على جميع وظائف التطبيق، بما في ذلك:

- تهيئة مكونات Bootstrap (tooltips, popovers, dropdowns, modals)
- تهيئة DataTables للجداول
- تهيئة الرسوم البيانية باستخدام Chart.js
- التحقق من صحة النماذج
- مبدل اللغة (العربية/الإنجليزية)
- مبدل السمة (الوضع الفاتح/الداكن)
- إدارة الإشعارات
- وظائف المساعد الذكي
- حسابات الفواتير
- معالجة تحميل الملفات
- إدارة الحقول الديناميكية في النماذج
- وظائف الطباعة والتصدير

## الأيقونات والصور

جميع الأيقونات والصور مخزنة بتنسيق SVG للحصول على جودة عالية وحجم ملف صغير. تشمل الأيقونات الرئيسية:

- شعار التطبيق وأيقونة المفضلة
- أيقونات لمختلف أقسام التطبيق (لوحة التحكم، الفواتير، العملاء، المشاريع، التقارير)
- أيقونة المساعد الذكي

## استخدام الأصول

يتم تحميل ملفات CSS و JavaScript تلقائيًا في قالب القاعدة للتطبيق. يمكن استخدام الأيقونات والصور في القوالب باستخدام مسار مثل:

```html
<img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting Logo">
```

## تخصيص الأصول

لتخصيص مظهر التطبيق، يمكن تعديل ملف `main.css` لتغيير الألوان والخطوط والأنماط الأخرى. يمكن أيضًا إضافة ملفات CSS و JavaScript إضافية حسب الحاجة.