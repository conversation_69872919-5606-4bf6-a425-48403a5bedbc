#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة لوحة التحكم
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import logging
import json

# استيراد النماذج
from models.user import User
from models.project import Project
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction

# استيراد الوكيل الذكي
from ai_agent.agent import SmartBizAgent
from ai_agent.analytics import SmartBizAnalytics

# إنشاء مخطط Blueprint
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

# إعداد السجل
logger = logging.getLogger(__name__)

@dashboard_bp.route('/')
@login_required
def home():
    """
لوحة التحكم الرئيسية
    """
    try:
        # الحصول على إحصائيات عامة
        today = datetime.now().date()
        start_of_month = datetime(today.year, today.month, 1).date()
        end_of_month = (datetime(today.year, today.month + 1, 1) - timedelta(days=1)).date() if today.month < 12 else datetime(today.year, 12, 31).date()
        
        # عدد العملاء والمشاريع والفواتير
        clients_count = Client.query.filter_by(user_id=current_user.id).count()
        projects_count = Project.query.filter_by(user_id=current_user.id).count()
        invoices_count = Invoice.query.filter_by(user_id=current_user.id).count()
        
        # الفواتير غير المدفوعة
        unpaid_invoices = Invoice.query.filter_by(
            user_id=current_user.id, 
            status='unpaid'
        ).order_by(Invoice.due_date.asc()).limit(5).all()
        
        # إجمالي المبيعات والمدفوعات للشهر الحالي
        monthly_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_of_month,
            Invoice.issue_date <= end_of_month
        ).all()
        
        monthly_sales = sum(invoice.total_amount for invoice in monthly_invoices)
        
        # المعاملات للشهر الحالي
        monthly_transactions = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_of_month,
            Transaction.date <= end_of_month
        ).all()
        
        monthly_income = sum(transaction.amount for transaction in monthly_transactions if transaction.type == 'income')
        monthly_expenses = sum(transaction.amount for transaction in monthly_transactions if transaction.type == 'expense')
        monthly_profit = monthly_income - monthly_expenses
        
        # أحدث المعاملات
        recent_transactions = Transaction.query.filter_by(
            user_id=current_user.id
        ).order_by(Transaction.date.desc()).limit(5).all()
        
        # أحدث العملاء
        recent_clients = Client.query.filter_by(
            user_id=current_user.id
        ).order_by(Client.created_at.desc()).limit(5).all()
        
        # المشاريع النشطة
        active_projects = Project.query.filter_by(
            user_id=current_user.id,
            status='active'
        ).order_by(Project.end_date.asc()).limit(5).all()
        
        # إنشاء رسوم بيانية
        try:
            analytics = SmartBizAnalytics()
            charts = analytics.generate_dashboard_charts(current_user.id)
        except Exception as chart_error:
            logger.error(f"Error generating charts: {str(chart_error)}")
            charts = None
        
        # الحصول على توصيات الذكاء الاصطناعي
        try:
            agent = SmartBizAgent()
            insights = agent.get_business_insights(current_user.id)
        except Exception as ai_error:
            logger.error(f"Error getting AI insights: {str(ai_error)}")
            insights = None
        
        # إعداد بيانات الإحصائيات لقالب لوحة التحكم
        stats = {
            'total_revenue': monthly_income,
            'revenue_change': 5.2,  # قيمة افتراضية للعرض
            'total_invoices': invoices_count,
            'paid_invoices': Invoice.query.filter_by(user_id=current_user.id, status='paid').count(),
            'unpaid_invoices': Invoice.query.filter_by(user_id=current_user.id, status='unpaid').count(),
            'total_clients': clients_count,
            'new_clients': Client.query.filter(Client.user_id == current_user.id, Client.created_at >= start_of_month).count(),
            'total_projects': projects_count,
            'completed_projects': Project.query.filter_by(user_id=current_user.id, status='completed').count(),
            'in_progress_projects': Project.query.filter_by(user_id=current_user.id, status='active').count()
        }
        
        # إعداد بيانات النشاط الأخير
        recent_activities = [
            {
                'title': 'تم إنشاء فاتورة جديدة',
                'description': 'تم إنشاء الفاتورة #INV-2023-001 للعميل شركة الأفق',
                'time_ago': 'منذ ساعتين',
                'user': 'أنت'
            },
            {
                'title': 'تم استلام دفعة',
                'description': 'تم استلام دفعة بقيمة 5,000 ريال من العميل مؤسسة النور',
                'time_ago': 'منذ 5 ساعات',
                'user': 'أنت'
            },
            {
                'title': 'تم إضافة عميل جديد',
                'description': 'تم إضافة عميل جديد: شركة المستقبل للتقنية',
                'time_ago': 'منذ يوم واحد',
                'user': 'أنت'
            }
        ]
        
        # إعداد بيانات المهام القادمة
        upcoming_tasks = [
            {
                'title': 'متابعة الفاتورة المتأخرة',
                'description': 'متابعة الفاتورة #INV-2023-002 المتأخرة مع العميل',
                'due_date': 'غداً',
                'priority': 'عالية',
                'priority_color': 'danger'
            },
            {
                'title': 'إعداد تقرير المبيعات الشهري',
                'description': 'إعداد وإرسال تقرير المبيعات الشهري للإدارة',
                'due_date': 'بعد 3 أيام',
                'priority': 'متوسطة',
                'priority_color': 'warning'
            },
            {
                'title': 'اجتماع مع العميل الجديد',
                'description': 'مناقشة متطلبات المشروع الجديد مع شركة المستقبل',
                'due_date': 'الأسبوع القادم',
                'priority': 'منخفضة',
                'priority_color': 'success'
            }
        ]
        
        # إعداد بيانات أحدث الفواتير
        recent_invoices = [
            {
                'id': 1,
                'number': 'INV-2023-005',
                'client_name': 'شركة الأفق',
                'date': '15 نوفمبر 2023',
                'amount': 12500,
                'status': 'مدفوعة',
                'status_color': 'success'
            },
            {
                'id': 2,
                'number': 'INV-2023-004',
                'client_name': 'مؤسسة النور',
                'date': '10 نوفمبر 2023',
                'amount': 8750,
                'status': 'غير مدفوعة',
                'status_color': 'danger'
            },
            {
                'id': 3,
                'number': 'INV-2023-003',
                'client_name': 'شركة المستقبل',
                'date': '5 نوفمبر 2023',
                'amount': 15000,
                'status': 'مدفوعة جزئياً',
                'status_color': 'warning'
            }
        ]
        
        # إعداد بيانات أحدث العملاء
        recent_clients = [
            {
                'id': 1,
                'name': 'شركة المستقبل للتقنية',
                'email': '<EMAIL>',
                'avatar': None
            },
            {
                'id': 2,
                'name': 'مؤسسة النور للتجارة',
                'email': '<EMAIL>',
                'avatar': None
            },
            {
                'id': 3,
                'name': 'شركة الأفق للاستشارات',
                'email': '<EMAIL>',
                'avatar': None
            }
        ]
        
        return render_template(
            'dashboard/index.html',
            stats=stats,
            recent_activities=recent_activities,
            upcoming_tasks=upcoming_tasks,
            recent_invoices=recent_invoices,
            recent_clients=recent_clients
        )
        
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('dashboard/index.html')

@dashboard_bp.route('/stats')
@login_required
def stats():
    """
صفحة الإحصائيات التفصيلية
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
        
        # إنشاء رسوم بيانية
        try:
            analytics = SmartBizAnalytics()
            sales_chart = analytics.generate_sales_chart(current_user.id, period=period)
            expense_chart = analytics.generate_expense_chart(current_user.id, period=period)
            cash_flow_chart = analytics.generate_cash_flow_chart(current_user.id, period=period)
            client_chart = analytics.generate_client_segmentation_chart(current_user.id)
            project_chart = analytics.generate_project_profitability_chart(current_user.id)
            
            charts = {
                'sales_chart': sales_chart.get('chart_html') if sales_chart.get('status') == 'success' else None,
                'expense_chart': expense_chart.get('chart_html') if expense_chart.get('status') == 'success' else None,
                'cash_flow_chart': cash_flow_chart.get('chart_html') if cash_flow_chart.get('status') == 'success' else None,
                'client_chart': client_chart.get('chart_html') if client_chart.get('status') == 'success' else None,
                'project_chart': project_chart.get('chart_html') if project_chart.get('status') == 'success' else None
            }
        except Exception as chart_error:
            logger.error(f"Error generating charts: {str(chart_error)}")
            charts = {}
        
        # الحصول على إحصائيات عامة
        # المعاملات في الفترة المحددة
        transactions = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_date,
            Transaction.date <= today
        ).all()
        
        total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
        total_expenses = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
        net_profit = total_income - total_expenses
        profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
        
        # الفواتير في الفترة المحددة
        invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_date,
            Invoice.issue_date <= today
        ).all()
        
        total_invoiced = sum(invoice.total_amount for invoice in invoices)
        paid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
        unpaid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status in ['unpaid', 'partially_paid'])
        
        # العملاء النشطين في الفترة المحددة
        active_clients = Client.query.filter(
            Client.user_id == current_user.id,
            Client.last_contact >= start_date
        ).count()
        
        # المشاريع النشطة في الفترة المحددة
        active_projects = Project.query.filter(
            Project.user_id == current_user.id,
            Project.status == 'active',
            Project.start_date <= today,
            (Project.end_date >= start_date) | (Project.end_date == None)
        ).count()
        
        return render_template(
            'dashboard/stats.html',
            period=period,
            start_date=start_date,
            end_date=today,
            total_income=total_income,
            total_expenses=total_expenses,
            net_profit=net_profit,
            profit_margin=profit_margin,
            total_invoiced=total_invoiced,
            paid_invoices=paid_invoices,
            unpaid_invoices=unpaid_invoices,
            active_clients=active_clients,
            active_projects=active_projects,
            charts=charts
        )
        
    except Exception as e:
        logger.error(f"Error loading stats page: {str(e)}")
        flash('حدث خطأ أثناء تحميل صفحة الإحصائيات. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('dashboard/stats.html')

@dashboard_bp.route('/api/stats')
@login_required
def api_stats():
    """
واجهة برمجة التطبيقات API للإحصائيات
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
        
        # المعاملات في الفترة المحددة
        transactions = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_date,
            Transaction.date <= today
        ).all()
        
        total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
        total_expenses = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
        net_profit = total_income - total_expenses
        profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
        
        # الفواتير في الفترة المحددة
        invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_date,
            Invoice.issue_date <= today
        ).all()
        
        total_invoiced = sum(invoice.total_amount for invoice in invoices)
        paid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
        unpaid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status in ['unpaid', 'partially_paid'])
        
        # تجميع البيانات حسب التاريخ
        daily_data = {}
        for transaction in transactions:
            date_str = transaction.date.strftime('%Y-%m-%d')
            if date_str not in daily_data:
                daily_data[date_str] = {'income': 0, 'expense': 0}
            
            if transaction.type == 'income':
                daily_data[date_str]['income'] += transaction.amount
            elif transaction.type == 'expense':
                daily_data[date_str]['expense'] += transaction.amount
        
        # تحويل البيانات إلى قائمة
        daily_summary = []
        for date_str, data in sorted(daily_data.items()):
            daily_summary.append({
                'date': date_str,
                'income': data['income'],
                'expense': data['expense'],
                'net': data['income'] - data['expense']
            })
        
        return jsonify({
            'status': 'success',
            'data': {
                'period': period,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': today.strftime('%Y-%m-%d'),
                'total_income': total_income,
                'total_expenses': total_expenses,
                'net_profit': net_profit,
                'profit_margin': profit_margin,
                'total_invoiced': total_invoiced,
                'paid_invoices': paid_invoices,
                'unpaid_invoices': unpaid_invoices,
                'daily_summary': daily_summary
            }
        })
        
    except Exception as e:
        logger.error(f"API Error - stats: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الإحصائيات'
        }), 500