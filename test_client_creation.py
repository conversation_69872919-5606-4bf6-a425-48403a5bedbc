#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار إنشاء عميل جديد
Test client creation functionality
"""

from app import create_app
from extensions import db
from models.client import Client
from models.user import User

def test_client_creation():
    """اختبار إنشاء عميل جديد"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 اختبار إنشاء عميل جديد...")
            
            # التحقق من وجود مستخدم للاختبار
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين في النظام")
                return False
            
            print(f"✅ تم العثور على المستخدم: {user.email}")
            
            # بيانات العميل التجريبي
            client_data = {
                'user_id': user.id,
                'name': 'أحمد محمد علي',
                'email': '<EMAIL>',
                'phone': '+************',
                'address': 'الرياض، المملكة العربية السعودية',
                'company_name': 'شركة الأمل للتجارة',
                'payment_method': 'bank_transfer',
                'notes': 'عميل مهم - يفضل التواصل عبر الهاتف',
                'is_active': True
            }
            
            # إنشاء العميل
            new_client = Client(**client_data)
            
            print("🔄 إضافة العميل إلى قاعدة البيانات...")
            db.session.add(new_client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل بنجاح! ID: {new_client.id}")
            
            # التحقق من البيانات
            saved_client = Client.query.get(new_client.id)
            if saved_client:
                print("✅ تم التحقق من حفظ البيانات:")
                print(f"   - الاسم: {saved_client.name}")
                print(f"   - البريد الإلكتروني: {saved_client.email}")
                print(f"   - الهاتف: {saved_client.phone}")
                print(f"   - الشركة: {saved_client.company_name}")
                print(f"   - طريقة الدفع: {saved_client.payment_method}")
                print(f"   - نشط: {saved_client.is_active}")
                print(f"   - تاريخ الإنشاء: {saved_client.created_at}")
                print(f"   - آخر اتصال: {saved_client.last_contact}")
                
                # اختبار الطرق
                print("\n🔄 اختبار طرق العميل...")
                print(f"   - إجمالي المبيعات: {saved_client.calculate_total_sales()}")
                print(f"   - إجمالي المدفوع: {saved_client.calculate_total_paid()}")
                print(f"   - إجمالي غير المدفوع: {saved_client.calculate_total_unpaid()}")
                
                # اختبار تحويل إلى قاموس
                client_dict = saved_client.to_dict()
                print(f"   - تحويل إلى قاموس: نجح ({len(client_dict)} حقل)")
                
                return True
            else:
                print("❌ فشل في حفظ البيانات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء العميل: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

def test_client_validation():
    """اختبار التحقق من صحة بيانات العميل"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔄 اختبار التحقق من صحة البيانات...")
            
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين في النظام")
                return False
            
            # اختبار عميل بدون اسم (يجب أن يفشل)
            try:
                invalid_client = Client(
                    user_id=user.id,
                    name=None,  # اسم فارغ
                    email='<EMAIL>'
                )
                db.session.add(invalid_client)
                db.session.commit()
                print("❌ تم قبول عميل بدون اسم (خطأ)")
                return False
            except Exception:
                print("✅ تم رفض عميل بدون اسم (صحيح)")
                db.session.rollback()
            
            # اختبار عميل بدون user_id (يجب أن يفشل)
            try:
                invalid_client = Client(
                    user_id=None,  # مستخدم فارغ
                    name='اختبار'
                )
                db.session.add(invalid_client)
                db.session.commit()
                print("❌ تم قبول عميل بدون مستخدم (خطأ)")
                return False
            except Exception:
                print("✅ تم رفض عميل بدون مستخدم (صحيح)")
                db.session.rollback()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التحقق: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🚀 بدء اختبار وظائف العملاء...")
    
    # اختبار إنشاء عميل
    success1 = test_client_creation()
    
    # اختبار التحقق من صحة البيانات
    success2 = test_client_validation()
    
    if success1 and success2:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ وظيفة إضافة العملاء تعمل بشكل صحيح")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
