{% extends "base.html" %}

{% block title %}إضافة فاتورة جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة فاتورة جديدة</h1>
            <p class="text-muted">إنشاء فاتورة جديدة للعميل</p>
        </div>
        <div>
            <a href="{{ url_for('invoices.list_invoices') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <form method="POST" id="invoiceForm">
        <div class="row">
            <!-- Invoice Details -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                       value="{{ default_invoice_number }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="client_id" name="client_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="project_id" class="form-label">المشروع</label>
                                <select class="form-select" id="project_id" name="project_id">
                                    <option value="">اختر المشروع (اختياري)</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}">{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="unpaid" selected>غير مدفوعة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="partially_paid">مدفوعة جزئياً</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="issue_date" class="form-label">تاريخ الإصدار <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="{{ today }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="{{ due_date }}" required>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>عناصر الفاتورة
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addItem()">
                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="itemsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40%">الوصف</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="10%">الضريبة %</th>
                                        <th width="15%">الإجمالي</th>
                                        <th width="5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <!-- Items will be added here dynamically -->
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <td colspan="4" class="text-end"><strong>الإجمالي الكلي:</strong></td>
                                        <td><strong id="grandTotal">0.00</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Notes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>الملاحظات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="client_notes" class="form-label">ملاحظات للعميل</label>
                            <textarea class="form-control" id="client_notes" name="client_notes" rows="3" 
                                      placeholder="ملاحظات ستظهر في الفاتورة للعميل"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="private_notes" class="form-label">ملاحظات خاصة</label>
                            <textarea class="form-control" id="private_notes" name="private_notes" rows="3" 
                                      placeholder="ملاحظات خاصة لن تظهر للعميل"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الفاتورة
                            </button>
                            <a href="{{ url_for('invoices.list_invoices') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hidden field for items data -->
        <input type="hidden" id="items_data" name="items_data" value="[]">
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
let itemCounter = 0;
let items = [];

function addItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.id = `item-${itemCounter}`;
    
    row.innerHTML = `
        <td>
            <input type="text" class="form-control" placeholder="وصف العنصر" 
                   onchange="updateItem(${itemCounter}, 'description', this.value)">
        </td>
        <td>
            <input type="number" class="form-control" min="0" step="0.01" value="1" 
                   onchange="updateItem(${itemCounter}, 'quantity', this.value)">
        </td>
        <td>
            <input type="number" class="form-control" min="0" step="0.01" value="0" 
                   onchange="updateItem(${itemCounter}, 'unit_price', this.value)">
        </td>
        <td>
            <input type="number" class="form-control" min="0" max="100" step="0.01" value="0" 
                   onchange="updateItem(${itemCounter}, 'tax_rate', this.value)">
        </td>
        <td>
            <span id="total-${itemCounter}">0.00</span>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeItem(${itemCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tbody.appendChild(row);
    
    // Initialize item data
    items[itemCounter] = {
        description: '',
        quantity: 1,
        unit_price: 0,
        tax_rate: 0
    };
    
    updateItemsData();
}

function removeItem(itemId) {
    const row = document.getElementById(`item-${itemId}`);
    if (row) {
        row.remove();
        delete items[itemId];
        updateGrandTotal();
        updateItemsData();
    }
}

function updateItem(itemId, field, value) {
    if (!items[itemId]) {
        items[itemId] = {};
    }
    
    items[itemId][field] = value;
    
    // Calculate item total
    const quantity = parseFloat(items[itemId].quantity || 0);
    const unitPrice = parseFloat(items[itemId].unit_price || 0);
    const taxRate = parseFloat(items[itemId].tax_rate || 0);
    
    const subtotal = quantity * unitPrice;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;
    
    // Update display
    const totalElement = document.getElementById(`total-${itemId}`);
    if (totalElement) {
        totalElement.textContent = total.toFixed(2);
    }
    
    updateGrandTotal();
    updateItemsData();
}

function updateGrandTotal() {
    let grandTotal = 0;
    
    for (let itemId in items) {
        if (items[itemId]) {
            const quantity = parseFloat(items[itemId].quantity || 0);
            const unitPrice = parseFloat(items[itemId].unit_price || 0);
            const taxRate = parseFloat(items[itemId].tax_rate || 0);
            
            const subtotal = quantity * unitPrice;
            const taxAmount = subtotal * (taxRate / 100);
            const total = subtotal + taxAmount;
            
            grandTotal += total;
        }
    }
    
    document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);
}

function updateItemsData() {
    const itemsArray = [];
    
    for (let itemId in items) {
        if (items[itemId] && items[itemId].description) {
            itemsArray.push(items[itemId]);
        }
    }
    
    document.getElementById('items_data').value = JSON.stringify(itemsArray);
}

// Add first item on page load
document.addEventListener('DOMContentLoaded', function() {
    addItem();
});

// Form validation
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    const itemsData = JSON.parse(document.getElementById('items_data').value);
    
    if (itemsData.length === 0) {
        e.preventDefault();
        alert('يرجى إضافة عنصر واحد على الأقل للفاتورة.');
        return false;
    }
    
    // Check if all items have descriptions
    for (let item of itemsData) {
        if (!item.description || item.description.trim() === '') {
            e.preventDefault();
            alert('يرجى إدخال وصف لجميع عناصر الفاتورة.');
            return false;
        }
    }
});
</script>
{% endblock %}
