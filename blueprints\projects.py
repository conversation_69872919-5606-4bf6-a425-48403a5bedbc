#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة إدارة المشاريع
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import logging
import json

# استيراد النماذج وقاعدة البيانات
from models.project import Project, project_clients
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction
from extensions import db

# إنشاء مخطط Blueprint
projects_bp = Blueprint('projects', __name__, url_prefix='/projects')

# إعداد السجل
logger = logging.getLogger(__name__)

@projects_bp.route('/')
@login_required
def list_projects():
    """
قائمة المشاريع
    """
    try:
        # الحصول على جميع المشاريع للمستخدم الحالي
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.start_date.desc()).all()
        
        # الحصول على العملاء للفلترة
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        status_filter = request.args.get('status')
        client_filter = request.args.get('client_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if status_filter and status_filter != 'all':
            projects = [project for project in projects if project.status == status_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            projects = [project for project in projects if any(client.id == client_id for client in project.clients)]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                projects = [project for project in projects if project.start_date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                projects = [project for project in projects if project.start_date.date() <= date_to]
            except ValueError:
                pass
        
        # حساب الإجماليات والإحصائيات
        total_value = 0
        active_projects_count = 0
        completed_projects_count = 0

        for project in projects:
            # إضافة دوال آمنة للحساب
            if hasattr(project, 'calculate_income'):
                project.income = project.calculate_income()
            else:
                project.income = project.budget or 0

            if hasattr(project, 'calculate_expenses'):
                project.expenses = project.calculate_expenses()
            else:
                project.expenses = 0

            if hasattr(project, 'calculate_profit'):
                project.profit = project.calculate_profit()
            else:
                project.profit = project.income - project.expenses

            total_value += project.budget or 0

            if project.status == 'active':
                active_projects_count += 1
            elif project.status == 'completed':
                completed_projects_count += 1

        return render_template(
            'projects/list.html',
            projects=projects,
            clients=clients,
            total_value=total_value,
            active_projects_count=active_projects_count,
            completed_projects_count=completed_projects_count,
            search_query=request.args.get('search', ''),
            status_filter=status_filter or 'all',
            client_filter=client_filter,
            sort_by=request.args.get('sort_by', 'name'),
            date_from=date_from,
            date_to=date_to
        )
        
    except Exception as e:
        logger.error(f"Error loading projects list: {str(e)}")
        flash('حدث خطأ أثناء تحميل قائمة المشاريع. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template(
            'projects/list.html',
            projects=[],
            clients=[],
            total_value=0,
            active_projects_count=0,
            completed_projects_count=0,
            search_query='',
            status_filter='all',
            client_filter='',
            sort_by='name'
        )

@projects_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_project():
    """
إضافة مشروع جديد
    """
    # الحصول على العملاء
    clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
    
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        status = request.form.get('status', 'active')
        budget = request.form.get('budget')
        client_ids = request.form.getlist('client_ids')
        
        # التحقق من البيانات
        if not name or not start_date:
            flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
            return render_template('projects/add.html', clients=clients)
        
        try:
            # التحقق من تنسيق التواريخ
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
                end_date = datetime.strptime(end_date, '%Y-%m-%d') if end_date else None
            except ValueError:
                flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                return render_template('projects/add.html', clients=clients)
            
            # التحقق من الميزانية
            budget = float(budget) if budget else 0
            
            # إنشاء مشروع جديد
            new_project = Project(
                user_id=current_user.id,
                name=name,
                description=description,
                start_date=start_date,
                end_date=end_date,
                status=status,
                budget=budget,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(new_project)
            db.session.flush()  # للحصول على معرف المشروع
            
            # إضافة العملاء للمشروع
            if client_ids:
                for client_id in client_ids:
                    client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                    if client:
                        new_project.clients.append(client)
                        # تحديث تاريخ آخر اتصال للعميل
                        client.update_last_contact()
            
            db.session.commit()
            
            flash(f'تمت إضافة المشروع {name} بنجاح.', 'success')
            return redirect(url_for('projects.view_project', project_id=new_project.id))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error adding project: {str(e)}")
            flash('حدث خطأ أثناء إضافة المشروع. يرجى المحاولة مرة أخرى.', 'danger')
    
    return render_template(
        'projects/add.html',
        clients=clients,
        today=datetime.now().strftime('%Y-%m-%d')
    )

@projects_bp.route('/<int:project_id>')
@login_required
def view_project(project_id):
    """
عرض تفاصيل المشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
        
        # الحصول على جميع العملاء للفلترة
        all_clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()

        # الحصول على الفواتير المرتبطة
        invoices = Invoice.query.filter_by(project_id=project.id).order_by(Invoice.issue_date.desc()).all()

        # الحصول على المعاملات المرتبطة
        transactions = Transaction.query.filter_by(project_id=project.id).order_by(Transaction.date.desc()).all()

        # حساب الإحصائيات بطريقة آمنة
        if hasattr(project, 'calculate_income'):
            income = project.calculate_income()
        else:
            income = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')

        if hasattr(project, 'calculate_expenses'):
            expenses = project.calculate_expenses()
        else:
            expenses = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')

        if hasattr(project, 'calculate_profit'):
            profit = project.calculate_profit()
        else:
            profit = income - expenses

        if hasattr(project, 'calculate_profit_margin'):
            profit_margin = project.calculate_profit_margin()
        else:
            profit_margin = (profit / income * 100) if income > 0 else 0

        # الفواتير غير المدفوعة
        if hasattr(project, 'get_unpaid_invoices'):
            unpaid_invoices = project.get_unpaid_invoices()
        else:
            unpaid_invoices = [invoice for invoice in invoices if invoice.status in ['unpaid', 'partially_paid', 'overdue']]

        # إحصائيات المشروع
        project_stats = {
            'total_invoices': len(invoices),
            'total_amount': sum(invoice.total_amount for invoice in invoices),
            'paid_amount': sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid'),
            'unpaid_amount': sum(invoice.total_amount for invoice in unpaid_invoices)
        }

        return render_template(
            'projects/view.html',
            project=project,
            clients=all_clients,
            invoices=invoices,
            transactions=transactions,
            income=income,
            expenses=expenses,
            profit=profit,
            profit_margin=profit_margin,
            unpaid_invoices=unpaid_invoices,
            project_stats=project_stats
        )
        
    except Exception as e:
        logger.error(f"Error viewing project: {str(e)}")
        flash('حدث خطأ أثناء عرض تفاصيل المشروع. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('projects.list_projects'))

@projects_bp.route('/<int:project_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_project(project_id):
    """
تعديل المشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العملاء
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        
        # الحصول على معرفات العملاء المرتبطين بالمشروع
        project_client_ids = [client.id for client in project.clients]
        
        if request.method == 'POST':
            name = request.form.get('name')
            description = request.form.get('description')
            start_date = request.form.get('start_date')
            end_date = request.form.get('end_date')
            status = request.form.get('status')
            budget = request.form.get('budget')
            client_ids = request.form.getlist('client_ids')
            
            # التحقق من البيانات
            if not name or not start_date:
                flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
                return render_template(
                    'projects/edit.html',
                    project=project,
                    clients=clients,
                    project_client_ids=project_client_ids
                )
            
            try:
                # التحقق من تنسيق التواريخ
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    end_date = datetime.strptime(end_date, '%Y-%m-%d') if end_date else None
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return render_template(
                        'projects/edit.html',
                        project=project,
                        clients=clients,
                        project_client_ids=project_client_ids
                    )
                
                # التحقق من الميزانية
                budget = float(budget) if budget else 0
                
                # تحديث بيانات المشروع
                project.name = name
                project.description = description
                project.start_date = start_date
                project.end_date = end_date
                project.status = status
                project.budget = budget
                project.updated_at = datetime.now()
                
                # تحديث العملاء المرتبطين
                # حذف جميع العلاقات الحالية
                project.clients = []
                
                # إضافة العملاء المحددين
                if client_ids:
                    for client_id in client_ids:
                        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                        if client:
                            project.clients.append(client)
                            # تحديث تاريخ آخر اتصال للعميل
                            client.update_last_contact()
                
                db.session.commit()
                
                flash(f'تم تحديث المشروع {name} بنجاح.', 'success')
                return redirect(url_for('projects.view_project', project_id=project.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating project: {str(e)}")
                flash('حدث خطأ أثناء تحديث المشروع. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template(
            'projects/edit.html',
            project=project,
            clients=clients,
            project_client_ids=project_client_ids
        )
        
    except Exception as e:
        logger.error(f"Error loading project for edit: {str(e)}")
        flash('حدث خطأ أثناء تحميل بيانات المشروع. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('projects.list_projects'))

@projects_bp.route('/<int:project_id>/delete', methods=['POST'])
@login_required
def delete_project(project_id):
    """
حذف المشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
        
        # التحقق من وجود فواتير أو معاملات مرتبطة
        invoices_count = Invoice.query.filter_by(project_id=project.id).count()
        transactions_count = Transaction.query.filter_by(project_id=project.id).count()
        
        if invoices_count > 0 or transactions_count > 0:
            flash('لا يمكن حذف المشروع لأنه مرتبط بفواتير أو معاملات.', 'danger')
            return redirect(url_for('projects.view_project', project_id=project.id))
        
        # حذف المشروع
        db.session.delete(project)
        db.session.commit()
        
        flash(f'تم حذف المشروع {project.name} بنجاح.', 'success')
        return redirect(url_for('projects.list_projects'))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting project: {str(e)}")
        flash('حدث خطأ أثناء حذف المشروع. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('projects.list_projects'))

@projects_bp.route('/<int:project_id>/add-transaction', methods=['GET', 'POST'])
@login_required
def add_transaction(project_id):
    """
إضافة معاملة للمشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العملاء المرتبطين بالمشروع
        clients = project.clients
        
        if request.method == 'POST':
            client_id = request.form.get('client_id')
            amount = request.form.get('amount')
            date = request.form.get('date')
            transaction_type = request.form.get('type')
            payment_method = request.form.get('payment_method')
            description = request.form.get('description')
            
            # التحقق من البيانات
            if not amount or not date or not transaction_type:
                flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
                return render_template(
                    'projects/add_transaction.html',
                    project=project,
                    clients=clients
                )
            
            try:
                # التحقق من المبلغ
                amount = float(amount)
                if amount <= 0:
                    flash('يجب أن يكون المبلغ أكبر من صفر.', 'danger')
                    return render_template(
                        'projects/add_transaction.html',
                        project=project,
                        clients=clients
                    )
                
                # التحقق من تنسيق التاريخ
                try:
                    transaction_date = datetime.strptime(date, '%Y-%m-%d')
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return render_template(
                        'projects/add_transaction.html',
                        project=project,
                        clients=clients
                    )
                
                # التحقق من وجود العميل إذا تم تحديده
                client = None
                if client_id:
                    client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                    if not client:
                        flash('العميل غير موجود.', 'danger')
                        return render_template(
                            'projects/add_transaction.html',
                            project=project,
                            clients=clients
                        )
                
                # إنشاء معاملة جديدة
                new_transaction = Transaction(
                    user_id=current_user.id,
                    client_id=client_id if client_id else None,
                    project_id=project.id,
                    invoice_id=None,
                    amount=amount,
                    date=transaction_date,
                    type=transaction_type,
                    payment_method=payment_method,
                    description=description or f'معاملة للمشروع {project.name}',
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                db.session.add(new_transaction)
                
                # تحديث تاريخ آخر اتصال للعميل إذا تم تحديده
                if client:
                    client.update_last_contact()
                
                db.session.commit()
                
                flash(f'تمت إضافة معاملة بقيمة {amount} بنجاح.', 'success')
                return redirect(url_for('projects.view_project', project_id=project.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error adding transaction: {str(e)}")
                flash('حدث خطأ أثناء إضافة المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template(
            'projects/add_transaction.html',
            project=project,
            clients=clients,
            today=datetime.now().strftime('%Y-%m-%d')
        )
        
    except Exception as e:
        logger.error(f"Error loading transaction form: {str(e)}")
        flash('حدث خطأ أثناء تحميل نموذج المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('projects.projects_list'))

@projects_bp.route('/api/list')
@login_required
def api_projects_list():
    """
واجهة برمجة التطبيقات API لقائمة المشاريع
    """
    try:
        # الحصول على جميع المشاريع للمستخدم الحالي
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.start_date.desc()).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        status_filter = request.args.get('status')
        client_filter = request.args.get('client_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if status_filter and status_filter != 'all':
            projects = [project for project in projects if project.status == status_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            projects = [project for project in projects if any(client.id == client_id for client in project.clients)]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                projects = [project for project in projects if project.start_date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                projects = [project for project in projects if project.start_date.date() <= date_to]
            except ValueError:
                pass
        
        # تحويل البيانات إلى تنسيق JSON
        projects_data = []
        for project in projects:
            project_data = project.to_dict()
            project_data['clients'] = [client.to_dict() for client in project.clients]
            project_data['income'] = project.calculate_income()
            project_data['expenses'] = project.calculate_expenses()
            project_data['profit'] = project.calculate_profit()
            project_data['profit_margin'] = project.calculate_profit_margin()
            projects_data.append(project_data)
        
        return jsonify({
            'status': 'success',
            'data': projects_data
        })
        
    except Exception as e:
        logger.error(f"API Error - projects list: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب قائمة المشاريع'
        }), 500

@projects_bp.route('/api/<int:project_id>')
@login_required
def api_project_details(project_id):
    """
واجهة برمجة التطبيقات API لتفاصيل المشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
        
        if not project:
            return jsonify({
                'status': 'error',
                'message': 'المشروع غير موجود'
            }), 404
        
        # الحصول على العملاء المرتبطين
        clients = project.clients
        
        # الحصول على الفواتير المرتبطة
        invoices = Invoice.query.filter_by(project_id=project.id).order_by(Invoice.issue_date.desc()).all()
        
        # الحصول على المعاملات المرتبطة
        transactions = Transaction.query.filter_by(project_id=project.id).order_by(Transaction.date.desc()).all()
        
        # تحويل البيانات إلى تنسيق JSON
        project_data = project.to_dict()
        project_data['clients'] = [client.to_dict() for client in clients]
        project_data['invoices'] = [invoice.to_dict() for invoice in invoices]
        project_data['transactions'] = [transaction.to_dict() for transaction in transactions]
        project_data['income'] = project.calculate_income()
        project_data['expenses'] = project.calculate_expenses()
        project_data['profit'] = project.calculate_profit()
        project_data['profit_margin'] = project.calculate_profit_margin()
        project_data['unpaid_invoices'] = [invoice.to_dict() for invoice in project.get_unpaid_invoices()]
        
        return jsonify({
            'status': 'success',
            'data': project_data
        })
        
    except Exception as e:
        logger.error(f"API Error - project details: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب تفاصيل المشروع'
        }), 500

@projects_bp.route('/api/add', methods=['POST'])
@login_required
def api_add_project():
    """
واجهة برمجة التطبيقات API لإضافة مشروع
    """
    try:
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'name' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # إنشاء مشروع جديد
        new_project = Project(
            user_id=current_user.id,
            name=data['name'],
            description=data.get('description', ''),
            start_date=datetime.strptime(data['start_date'], '%Y-%m-%d') if 'start_date' in data else datetime.now(),
            end_date=datetime.strptime(data['end_date'], '%Y-%m-%d') if 'end_date' in data else None,
            status=data.get('status', 'active'),
            budget=float(data.get('budget', 0)),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.session.add(new_project)
        db.session.flush()  # للحصول على معرف المشروع
        
        # إضافة العملاء للمشروع
        client_ids = data.get('client_ids', [])
        if client_ids:
            for client_id in client_ids:
                client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                if client:
                    new_project.clients.append(client)
                    # تحديث تاريخ آخر اتصال للعميل
                    client.update_last_contact()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تمت إضافة المشروع {new_project.name} بنجاح.',
            'data': new_project.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - add project: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إضافة المشروع'
        }), 500

@projects_bp.route('/api/<int:project_id>/update-status', methods=['PUT'])
@login_required
def api_update_project_status(project_id):
    """
واجهة برمجة التطبيقات API لتحديث حالة المشروع
    """
    try:
        # الحصول على المشروع
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
        
        if not project:
            return jsonify({
                'status': 'error',
                'message': 'المشروع غير موجود'
            }), 404
        
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'status' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # التحقق من صحة الحالة
        valid_statuses = ['active', 'completed', 'on_hold', 'cancelled']
        if data['status'] not in valid_statuses:
            return jsonify({
                'status': 'error',
                'message': 'حالة المشروع غير صالحة'
            }), 400
        
        # تحديث حالة المشروع
        project.status = data['status']
        project.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تم تحديث حالة المشروع إلى {data["status"]} بنجاح.',
            'data': project.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - update project status: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء تحديث حالة المشروع'
        }), 500