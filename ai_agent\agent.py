# -*- coding: utf-8 -*-

"""
SmartBiz AI Agent - الوكيل الذكي المحسن
Enhanced AI Agent for SmartBiz Accounting System
"""

import os
import re
import logging
import random
from datetime import datetime, timedelta
from sqlalchemy import func
from typing import List
import warnings
warnings.filterwarnings('ignore')

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# استيراد المكتبات الاختيارية
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    np = None

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    pd = None

try:
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import KMeans
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    LinearRegression = None
    StandardScaler = None
    KMeans = None

try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False

# استيراد النماذج
try:
    from models.invoice import Invoice
    from models.client import Client
    from models.user import User
    from models.project import Project
    from models.transaction import Transaction
except ImportError:
    # في حالة عدم توفر النماذج، سيتم استيرادها محلياً عند الحاجة
    Invoice = None
    Client = None
    User = None
    Project = None
    Transaction = None

# تكوين Google Gemini AI (استخدام المكتبة الرسمية)
GEMINI_AVAILABLE = False
genai = None
GEMINI_API_KEY = None
GEMINI_MODEL = 'gemini-pro'
GEMINI_MAX_TOKENS = 4000
GEMINI_TEMPERATURE = 0.7

if HAS_DOTENV:
    # إعداد Google Gemini API
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
    if GEMINI_API_KEY:
        # إعداد المعاملات الافتراضية
        GEMINI_MODEL = os.environ.get('GEMINI_MODEL', 'gemini-pro')
        GEMINI_MAX_TOKENS = int(os.environ.get('GEMINI_MAX_TOKENS', 4000))
        GEMINI_TEMPERATURE = float(os.environ.get('GEMINI_TEMPERATURE', 0.7))
        GEMINI_AVAILABLE = True
        
        # استخدام المكتبة الرسمية لـ Google Gemini AI
        try:
            import google.generativeai as genai
            # محاولة تكوين المكتبة مع معالجة الأخطاء
            try:
                # استخدام طريقة بديلة للتكوين إذا لم تكن configure متاحة
                if hasattr(genai, 'configure'):
                    # الطريقة الحديثة
                    genai.configure(api_key=GEMINI_API_KEY)  # type: ignore
                    logger.info("✅ Google Gemini AI تم تكوينه بنجاح (الطريقة الحديثة)")
                elif hasattr(genai, 'client'):
                    # طريقة بديلة للإصدارات الأقدم
                    genai.client.api_key = GEMINI_API_KEY
                    logger.info("✅ Google Gemini AI تم تكوينه بنجاح (الطريقة البديلة)")
                else:
                    logger.warning("⚠️ لا يمكن تكوين Google Gemini AI، سيتم استخدام requests")
                    genai = None
            except Exception as config_error:
                logger.warning(f"⚠️ فشل في تكوين Google Gemini AI: {config_error}")
                genai = None
        except ImportError:
            logger.warning("⚠️ مكتبة google-generativeai غير مثبتة، سيتم استخدام requests مباشرة")
            genai = None  # سنستخدم requests مباشرة كبديل

# OpenAI تم حذفه نهائياً - نستخدم Google Gemini AI فقط

# استيراد معالج اللغة العربية المتقدم
try:
    from ai_agent.advanced_nlp import EnhancedArabicNLP
    nlp_ar = EnhancedArabicNLP()
    HAS_ADVANCED_NLP = True
except ImportError:
    from ai_agent.utils import SimpleArabicNLP
    nlp_ar = SimpleArabicNLP()
    HAS_ADVANCED_NLP = False

# استيراد التحليلات المحسنة
try:
    from ai_agent.analytics import SmartBizAnalytics
    HAS_ANALYTICS = True
except ImportError:
    HAS_ANALYTICS = False
    SmartBizAnalytics = None

class SmartBizAgent:
    """الوكيل الذكي لنظام SmartBiz"""
    
    def __init__(self, db=None):
        """تهيئة الوكيل الذكي"""
        self.db = db
        self.nlp = nlp_ar
        
        # إعداد نماذج التعلم الآلي
        if HAS_SKLEARN and StandardScaler is not None:
            self.scaler = StandardScaler()
            self.models = {
                'sales_predictor': LinearRegression() if LinearRegression is not None else None,
                'client_segmentation': KMeans(n_clusters=4, random_state=42) if KMeans is not None else None,
                'anomaly_detector': None
            }
        else:
            self.scaler = None
            self.models = {
                'sales_predictor': None,
                'client_segmentation': None,
                'anomaly_detector': None
            }
        
        # ذاكرة المحادثة وتفضيلات المستخدم
        self.conversation_memory = []
        self.user_preferences = {}
        
        # قاعدة المعرفة المحاسبية المحسنة
        from ai_agent.config import AIAgentConfig
        self.accounting_knowledge = AIAgentConfig.KNOWLEDGE_BASE

        # إعدادات التعلم المتقدم
        self.learning_config = AIAgentConfig.get_learning_config()
        self.performance_config = AIAgentConfig.get_performance_config()

        # نظام الذاكرة المتقدم
        self.memory_system = {
            'short_term': [],
            'long_term': [],
            'episodic': [],
            'semantic': {}
        }

        # نظام التعلم التدريجي
        self.incremental_learning = {
            'enabled': self.learning_config['learning_algorithms']['reinforcement_learning']['enabled'],
            'learning_rate': self.learning_config['learning_algorithms']['reinforcement_learning']['learning_rate'],
            'knowledge_base': {},
            'user_patterns': {},
            'feedback_scores': []
        }
    
    def process_message(self, message: str, user_id: int, context=None) -> dict:
        """معالجة رسالة المستخدم المحسنة مع التعلم التدريجي"""
        _ = context  # متغير غير مستخدم

        try:
            # تنظيف وتحليل الرسالة باستخدام المعالج المتقدم
            cleaned_message = self._clean_message(message)

            # استخدام المعالج المتقدم إذا كان متاحاً
            if HAS_ADVANCED_NLP:
                analysis_result = self.nlp.detect_intent_advanced(cleaned_message, user_id)
                keywords = analysis_result['keywords']
                intent = analysis_result['intent']
                confidence = analysis_result['confidence']
                entities = analysis_result['entities']
            else:
                keywords = self.nlp.extract_keywords(cleaned_message)
                intent = self._detect_intent(cleaned_message, keywords)
                confidence = 0.8  # قيمة افتراضية
                entities = {}

            # إضافة الرسالة إلى نظام الذاكرة المتقدم
            interaction_data = {
                'user_message': message,
                'cleaned_message': cleaned_message,
                'keywords': keywords,
                'intent': intent,
                'confidence': confidence,
                'entities': entities,
                'timestamp': datetime.now(),
                'user_id': user_id
            }

            # إضافة إلى الذاكرة قصيرة المدى
            self.memory_system['short_term'].append(interaction_data)
            self.conversation_memory.append(interaction_data)

            # معالجة الرسالة حسب النوع مع التحسينات
            if intent == 'greeting':
                response = self._handle_greeting_enhanced(user_id)
            elif intent == 'accounting_question':
                response = self._handle_accounting_question_enhanced(cleaned_message, keywords, user_id)
            elif intent == 'data_query':
                response = self._handle_data_query(cleaned_message, keywords, user_id)
            elif intent == 'calculation':
                response = self._handle_calculation_enhanced(cleaned_message, keywords, user_id)
            elif intent == 'analysis_request':
                response = self._handle_analysis_request_enhanced(cleaned_message, keywords, user_id)
            elif intent == 'help':
                response = self._handle_help_request(cleaned_message, user_id)
            elif intent == 'recommendation_request':
                response = self._handle_recommendation_request(cleaned_message, keywords, user_id)
            elif intent == 'prediction_request':
                response = self._handle_prediction_request(cleaned_message, keywords, user_id)
            else:
                response = self._handle_general_query_enhanced(cleaned_message, user_id)

            # إضافة الرد إلى ذاكرة المحادثة
            self.conversation_memory[-1]['ai_response'] = response
            self.memory_system['short_term'][-1]['ai_response'] = response

            # تحديث تفضيلات المستخدم مع التعلم
            self._update_user_preferences_enhanced(user_id, intent, keywords, confidence)

            # تحديث نظام التعلم التدريجي
            if self.incremental_learning['enabled']:
                self._update_incremental_learning(user_id, intent, keywords, confidence)

            # إدارة الذاكرة
            self._manage_memory_system()

            return {
                'status': 'success',
                'response': response,
                'intent': intent,
                'confidence': confidence,
                'keywords': keywords,
                'entities': entities,
                'suggestions': self._generate_suggestions_enhanced(intent, keywords, user_id),
                'learning_applied': HAS_ADVANCED_NLP and self.incremental_learning['enabled']
            }

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'status': 'error',
                'response': 'عذراً، حدث خطأ أثناء معالجة رسالتك. يرجى المحاولة مرة أخرى.',
                'error': str(e)
            }
    
    def _clean_message(self, message: str) -> str:
        """تنظيف الرسالة من الأحرف غير المرغوبة"""
        cleaned = re.sub(r'[^\u0600-\u06FF\s\d\.\,\?\!]', ' ', message)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned
    
    def _detect_intent(self, message: str, keywords: List[str]) -> str:
        """تحديد نية المستخدم من الرسالة"""
        _ = keywords  # متغير غير مستخدم
        message_lower = message.lower()
        
        # تحيات
        greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء', 'كيف حالك']
        if any(greeting in message_lower for greeting in greetings):
            return 'greeting'
        
        # أسئلة محاسبية
        accounting_terms = ['محاسبة', 'مبدأ', 'قائمة', 'ميزانية', 'نسبة', 'مصطلح']
        if any(term in message_lower for term in accounting_terms):
            return 'accounting_question'
        
        # طلبات حسابية
        calculation_terms = ['احسب', 'حساب', 'كم', 'مجموع', 'متوسط', 'نسبة']
        if any(term in message_lower for term in calculation_terms):
            return 'calculation'
        
        # طلبات تحليل
        analysis_terms = ['تحليل', 'تقرير', 'إحصائيات', 'اتجاه', 'توقع']
        if any(term in message_lower for term in analysis_terms):
            return 'analysis_request'
        
        # استعلامات البيانات
        data_terms = ['اظهر', 'عرض', 'قائمة', 'فواتير', 'عملاء', 'مشاريع']
        if any(term in message_lower for term in data_terms):
            return 'data_query'
        
        # طلبات المساعدة
        help_terms = ['مساعدة', 'كيف', 'ماذا', 'أين', 'متى']
        if any(term in message_lower for term in help_terms):
            return 'help'
        
        return 'general'
    
    def _handle_greeting(self, user_id: int) -> str:
        """التعامل مع التحيات"""
        _ = user_id  # متغير غير مستخدم
        greetings = [
            "مرحباً بك في SmartBiz! كيف يمكنني مساعدتك اليوم؟",
            "أهلاً وسهلاً! أنا مساعدك الذكي للمحاسبة. ما الذي تحتاج إليه؟",
            "السلام عليكم! أنا هنا لمساعدتك في جميع أمورك المحاسبية.",
            "صباح الخير! كيف يمكنني أن أساعدك في إدارة أعمالك اليوم؟"
        ]
        if HAS_NUMPY and np is not None:
            return np.random.choice(greetings)
        else:
            import random
            return random.choice(greetings)
    
    def _handle_accounting_question(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع الأسئلة المحاسبية"""
        _ = user_id  # متغير غير مستخدم
        
        # البحث في قاعدة المعرفة
        for category, items in self.accounting_knowledge.items():
            _ = category  # متغير غير مستخدم
            for term, definition in items.items():
                if any(keyword in term for keyword in keywords) or any(keyword in message for keyword in keywords):
                    return f"**{term.replace('_', ' ')}:**\n\n{definition}\n\nهل تحتاج إلى مزيد من التوضيح حول هذا الموضوع؟"
        
        # إذا لم يتم العثور على إجابة مباشرة
        return self._generate_educational_response(message, keywords)
    
    def _handle_data_query(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع استعلامات البيانات"""
        _ = keywords  # متغير غير مستخدم
        
        try:
            if not self.db:
                return "عذراً، لا يمكنني الوصول إلى البيانات حالياً."
            
            if 'فواتير' in message or 'فاتورة' in message:
                return self._get_invoice_summary(user_id)
            elif 'عملاء' in message or 'عميل' in message:
                return self._get_client_summary(user_id)
            elif 'مشاريع' in message or 'مشروع' in message:
                return self._get_project_summary(user_id)
            else:
                return self._get_general_summary(user_id)
        except Exception as e:
            return f"عذراً، حدث خطأ أثناء جلب البيانات: {str(e)}"

    def _handle_calculation(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع طلبات الحسابات"""
        _ = keywords  # متغير غير مستخدم

        try:
            if 'ربح' in message or 'أرباح' in message:
                return self._calculate_profit(user_id)
            elif 'مبيعات' in message:
                return self._calculate_sales(user_id)
            elif 'نسبة' in message:
                return self._calculate_ratios(user_id)
            else:
                return "يرجى تحديد نوع الحساب المطلوب (أرباح، مبيعات، نسب مالية، إلخ)"
        except Exception as e:
            return f"عذراً، حدث خطأ أثناء الحساب: {str(e)}"

    def _handle_analysis_request(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع طلبات التحليل"""
        _ = keywords  # متغير غير مستخدم

        try:
            if 'اتجاه' in message or 'توقع' in message:
                result = self.analyze_sales_trend(user_id)
                if result['status'] == 'success':
                    trend_text = 'صاعد' if result['trend'] == 'upward' else 'هابط' if result['trend'] == 'downward' else 'مستقر'
                    return f"تحليل اتجاه المبيعات:\n\n📈 الاتجاه العام: {trend_text}\n📊 معدل النمو: {result['growth_rate']:.2f}%\n\nهل تريد تفاصيل أكثر؟"
                else:
                    return result['message']
            elif 'عملاء' in message:
                result = self.analyze_client_segments(user_id)
                if result['status'] == 'success':
                    return f"تحليل شرائح العملاء:\n\n👥 تم تقسيم العملاء إلى {result['optimal_clusters']} مجموعات\n📊 يمكنك مراجعة التقارير للحصول على تفاصيل أكثر"
                else:
                    return result['message']
            else:
                return self._get_business_analysis(user_id)
        except Exception as e:
            return f"عذراً، حدث خطأ أثناء التحليل: {str(e)}"

    def _handle_help_request(self, message: str, user_id: int) -> str:
        """التعامل مع طلبات المساعدة"""
        _ = user_id  # متغير غير مستخدم
        _ = message  # متغير غير مستخدم

        help_text = """
🤖 **مرحباً بك في مساعد SmartBiz الذكي!**

يمكنني مساعدتك في:

📊 **التحليلات والتقارير:**
- تحليل اتجاه المبيعات
- تحليل شرائح العملاء
- تحليل التدفق النقدي

💰 **الحسابات المالية:**
- حساب الأرباح والخسائر
- حساب النسب المالية
- تقييم الأداء المالي

📋 **استعلام البيانات:**
- عرض الفواتير والعملاء
- معلومات المشاريع
- ملخص الأعمال

📚 **التعليم المحاسبي:**
- شرح المبادئ المحاسبية
- تعريف المصطلحات
- أمثلة عملية

**أمثلة على الأسئلة:**
- "احسب إجمالي الأرباح هذا الشهر"
- "اظهر لي قائمة العملاء"
- "ما هو مبدأ الاستحقاق؟"
- "حلل اتجاه المبيعات"

كيف يمكنني مساعدتك اليوم؟ 😊
        """
        return help_text

    def _handle_general_query(self, message: str, user_id: int) -> str:
        """التعامل مع الاستعلامات العامة"""
        if GEMINI_AVAILABLE:
            result = self._process_with_gemini(message, user_id)
            if isinstance(result, dict):
                return result.get('response', str(result))
            return str(result)
        else:
            return self._generate_smart_fallback_response(message, user_id)

    def _generate_educational_response(self, message: str, keywords: List[str]) -> str:
        """توليد رد تعليمي"""
        _ = message  # متغير غير مستخدم

        educational_responses = {
            'محاسبة': "المحاسبة هي علم تسجيل وتصنيف وتلخيص المعاملات المالية لتوفير معلومات مفيدة لاتخاذ القرارات.",
            'ميزانية': "الميزانية العمومية تظهر الوضع المالي للشركة في تاريخ معين، وتتكون من الأصول والخصوم وحقوق الملكية.",
            'دخل': "قائمة الدخل تظهر إيرادات ومصروفات الشركة خلال فترة معينة لحساب صافي الربح أو الخسارة.",
            'تدفق': "قائمة التدفقات النقدية تتبع حركة النقد الداخل والخارج من الأنشطة التشغيلية والاستثمارية والتمويلية."
        }

        for keyword in keywords:
            for term, explanation in educational_responses.items():
                if term in keyword or keyword in term:
                    return f"📚 **{term}:**\n\n{explanation}\n\nهل تحتاج إلى مزيد من التفاصيل؟"

        return "أعتذر، لم أتمكن من فهم سؤالك بوضوح. يمكنك إعادة صياغته أو طلب المساعدة لمعرفة ما يمكنني فعله."

    def _update_user_preferences(self, user_id: int, intent: str, keywords: List[str]):
        """تحديث تفضيلات المستخدم"""
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {
                'frequent_intents': {},
                'favorite_topics': {},
                'interaction_count': 0
            }

        prefs = self.user_preferences[user_id]
        prefs['interaction_count'] += 1

        # تحديث الاهتمامات المتكررة
        if intent in prefs['frequent_intents']:
            prefs['frequent_intents'][intent] += 1
        else:
            prefs['frequent_intents'][intent] = 1

        # تحديث المواضيع المفضلة
        for keyword in keywords:
            if keyword in prefs['favorite_topics']:
                prefs['favorite_topics'][keyword] += 1
            else:
                prefs['favorite_topics'][keyword] = 1

    def _generate_suggestions(self, intent: str, keywords: List[str], user_id: int) -> List[str]:
        """توليد اقتراحات للمستخدم"""
        _ = user_id  # متغير غير مستخدم
        _ = keywords  # متغير غير مستخدم

        suggestions = []

        if intent == 'greeting':
            suggestions = [
                "اظهر لي ملخص الأعمال",
                "احسب إجمالي الأرباح",
                "حلل اتجاه المبيعات",
                "ما هي الفواتير المتأخرة؟"
            ]
        elif intent == 'accounting_question':
            suggestions = [
                "اشرح لي المزيد عن هذا المفهوم",
                "أعطني مثال عملي",
                "ما هي المبادئ المحاسبية الأساسية؟",
                "كيف أطبق هذا في عملي؟"
            ]
        elif intent == 'data_query':
            suggestions = [
                "اظهر تفاصيل أكثر",
                "قارن مع الشهر الماضي",
                "صدر هذه البيانات",
                "حلل هذه البيانات"
            ]
        elif intent == 'calculation':
            suggestions = [
                "احسب النسب المالية",
                "قارن مع الفترة السابقة",
                "اظهر التفاصيل",
                "احسب معدل النمو"
            ]
        elif intent == 'analysis_request':
            suggestions = [
                "اظهر توقعات المستقبل",
                "قدم توصيات للتحسين",
                "قارن مع المعايير الصناعية",
                "حلل المخاطر"
            ]
        else:
            suggestions = [
                "كيف يمكنني مساعدتك؟",
                "اظهر لي الخيارات المتاحة",
                "ما هي أهم التقارير؟",
                "علمني شيئاً جديداً"
            ]

        return suggestions[:4]  # إرجاع أول 4 اقتراحات

    # ===== طرق معالجة البيانات =====

    def _get_invoice_summary(self, user_id: int) -> str:
        """الحصول على ملخص الفواتير"""
        try:
            if not Invoice:
                return "عذراً، لا يمكنني الوصول إلى بيانات الفواتير حالياً."

            invoices = Invoice.query.filter_by(user_id=user_id).all()

            if not invoices:
                return "لا توجد فواتير مسجلة حالياً."

            total_invoices = len(invoices)
            paid_count = len([inv for inv in invoices if inv.status == 'paid'])
            unpaid_count = len([inv for inv in invoices if inv.status == 'unpaid'])
            total_amount = sum(inv.total_amount for inv in invoices)
            paid_amount = sum(inv.total_amount for inv in invoices if inv.status == 'paid')

            return f"""📋 **ملخص الفواتير:**

📊 إجمالي الفواتير: {total_invoices}
✅ مدفوعة: {paid_count}
❌ غير مدفوعة: {unpaid_count}
💰 إجمالي المبلغ: {total_amount:,.2f}
💵 المبلغ المحصل: {paid_amount:,.2f}
📈 نسبة التحصيل: {(paid_amount/total_amount*100):.1f}%"""

        except Exception as e:
            return f"عذراً، حدث خطأ: {str(e)}"

    def _get_client_summary(self, user_id: int) -> str:
        """الحصول على ملخص العملاء"""
        try:
            if not Client:
                return "عذراً، لا يمكنني الوصول إلى بيانات العملاء حالياً."

            clients = Client.query.filter_by(user_id=user_id).all()

            if not clients:
                return "لا يوجد عملاء مسجلون حالياً."

            total_clients = len(clients)
            active_clients = len([c for c in clients if getattr(c, 'is_active', True)])

            return f"""👥 **ملخص العملاء:**

📊 إجمالي العملاء: {total_clients}
✅ عملاء نشطون: {active_clients}
📈 معدل النشاط: {(active_clients/total_clients*100):.1f}%"""

        except Exception as e:
            return f"عذراً، حدث خطأ: {str(e)}"

    def _get_project_summary(self, user_id: int) -> str:
        """الحصول على ملخص المشاريع"""
        try:
            if not Project:
                return "عذراً، لا يمكنني الوصول إلى بيانات المشاريع حالياً."

            projects = Project.query.filter_by(user_id=user_id).all()

            if not projects:
                return "لا توجد مشاريع مسجلة حالياً."

            total_projects = len(projects)
            active_projects = len([p for p in projects if p.status == 'active'])
            completed_projects = len([p for p in projects if p.status == 'completed'])

            return f"""🚀 **ملخص المشاريع:**

📊 إجمالي المشاريع: {total_projects}
🔄 مشاريع نشطة: {active_projects}
✅ مشاريع مكتملة: {completed_projects}
📈 معدل الإنجاز: {(completed_projects/total_projects*100):.1f}%"""

        except Exception as e:
            return f"عذراً، حدث خطأ: {str(e)}"

    def _get_general_summary(self, user_id: int) -> str:
        """الحصول على ملخص عام للأعمال"""
        try:
            invoice_summary = self._get_invoice_summary(user_id)
            client_summary = self._get_client_summary(user_id)
            project_summary = self._get_project_summary(user_id)

            return f"""📊 **ملخص الأعمال العام:**

{invoice_summary}

{client_summary}

{project_summary}

💡 **نصيحة:** يمكنك طلب تحليل أكثر تفصيلاً لأي من هذه الجوانب."""

        except Exception as e:
            return f"عذراً، حدث خطأ: {str(e)}"

    # ===== طرق الحسابات المالية =====

    def _calculate_profit(self, user_id: int) -> str:
        """حساب الأرباح"""
        try:
            if not Invoice or not Transaction:
                return "عذراً، لا يمكنني الوصول إلى البيانات المالية حالياً."

            # حساب الإيرادات من الفواتير المدفوعة
            paid_invoices = Invoice.query.filter_by(user_id=user_id, status='paid').all()
            total_revenue = sum(inv.total_amount for inv in paid_invoices)

            # حساب المصروفات
            expenses = Transaction.query.filter_by(user_id=user_id, transaction_type='expense').all()
            total_expenses = sum(exp.amount for exp in expenses)

            # حساب صافي الربح
            net_profit = total_revenue - total_expenses
            profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0

            return f"""💰 **تحليل الأرباح:**

📈 إجمالي الإيرادات: {total_revenue:,.2f}
📉 إجمالي المصروفات: {total_expenses:,.2f}
💵 صافي الربح: {net_profit:,.2f}
📊 هامش الربح: {profit_margin:.1f}%

{'🎉 أداء ممتاز!' if profit_margin > 20 else '⚠️ يحتاج تحسين' if profit_margin < 10 else '👍 أداء جيد'}"""

        except Exception as e:
            return f"عذراً، حدث خطأ في حساب الأرباح: {str(e)}"

    def _calculate_sales(self, user_id: int) -> str:
        """حساب المبيعات"""
        try:
            if not Invoice:
                return "عذراً، لا يمكنني الوصول إلى بيانات المبيعات حالياً."

            today = datetime.now().date()
            this_month_start = today.replace(day=1)
            last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)

            # مبيعات هذا الشهر
            this_month_invoices = Invoice.query.filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= this_month_start
            ).all()
            this_month_sales = sum(inv.total_amount for inv in this_month_invoices)

            # مبيعات الشهر الماضي
            last_month_invoices = Invoice.query.filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= last_month_start,
                Invoice.issue_date < this_month_start
            ).all()
            last_month_sales = sum(inv.total_amount for inv in last_month_invoices)

            # حساب النمو
            growth_rate = ((this_month_sales - last_month_sales) / last_month_sales * 100) if last_month_sales > 0 else 0

            return f"""📊 **تحليل المبيعات:**

🗓️ مبيعات هذا الشهر: {this_month_sales:,.2f}
📅 مبيعات الشهر الماضي: {last_month_sales:,.2f}
📈 معدل النمو: {growth_rate:+.1f}%
📋 عدد الفواتير: {len(this_month_invoices)}

{'🚀 نمو ممتاز!' if growth_rate > 10 else '📉 انخفاض' if growth_rate < -5 else '📊 نمو مستقر'}"""

        except Exception as e:
            return f"عذراً، حدث خطأ في حساب المبيعات: {str(e)}"

    def _calculate_ratios(self, user_id: int) -> str:
        """حساب النسب المالية"""
        try:
            if not Invoice or not Transaction:
                return "عذراً، لا يمكنني الوصول إلى البيانات المالية حالياً."

            # جمع البيانات
            invoices = Invoice.query.filter_by(user_id=user_id).all()
            paid_invoices = [inv for inv in invoices if inv.status == 'paid']
            unpaid_invoices = [inv for inv in invoices if inv.status in ['unpaid', 'partially_paid']]

            total_revenue = sum(inv.total_amount for inv in paid_invoices)
            total_receivables = sum(getattr(inv, 'amount_due', inv.total_amount) for inv in unpaid_invoices)

            expenses = Transaction.query.filter_by(user_id=user_id, transaction_type='expense').all()
            total_expenses = sum(exp.amount for exp in expenses)

            # حساب النسب
            collection_ratio = (len(paid_invoices) / len(invoices) * 100) if invoices else 0
            profit_margin = ((total_revenue - total_expenses) / total_revenue * 100) if total_revenue > 0 else 0
            receivables_ratio = (total_receivables / total_revenue * 100) if total_revenue > 0 else 0

            return f"""📊 **النسب المالية:**

💰 نسبة التحصيل: {collection_ratio:.1f}%
📈 هامش الربح: {profit_margin:.1f}%
📋 نسبة المستحقات: {receivables_ratio:.1f}%

**التقييم:**
{'✅ ممتاز' if collection_ratio > 80 else '⚠️ يحتاج تحسين' if collection_ratio < 60 else '👍 جيد'} - التحصيل
{'✅ ممتاز' if profit_margin > 20 else '⚠️ يحتاج تحسين' if profit_margin < 10 else '👍 جيد'} - الربحية"""

        except Exception as e:
            return f"عذراً، حدث خطأ في حساب النسب: {str(e)}"

    # ===== طرق التحليل المتقدم =====

    def analyze_sales_trend(self, user_id: int, period='month', months=6) -> dict:
        """تحليل اتجاه المبيعات"""
        _ = period  # متغير غير مستخدم

        try:
            if not Invoice:
                return {
                    'status': 'error',
                    'message': 'لا يمكنني الوصول إلى بيانات المبيعات'
                }

            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30 * months)

            # الحصول على بيانات المبيعات
            invoices = Invoice.query.filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).all()

            if len(invoices) < 2:
                return {
                    'status': 'error',
                    'message': 'لا توجد بيانات كافية لتحليل الاتجاه'
                }

            # تحليل بسيط للاتجاه
            total_sales = sum(inv.total_amount for inv in invoices)
            avg_monthly_sales = total_sales / months

            # حساب النمو (مقارنة النصف الأول بالنصف الثاني)
            mid_point = len(invoices) // 2
            first_half = invoices[:mid_point]
            second_half = invoices[mid_point:]

            first_half_sales = sum(inv.total_amount for inv in first_half)
            second_half_sales = sum(inv.total_amount for inv in second_half)

            growth_rate = ((second_half_sales - first_half_sales) / first_half_sales * 100) if first_half_sales > 0 else 0

            trend = 'upward' if growth_rate > 5 else 'downward' if growth_rate < -5 else 'stable'

            return {
                'status': 'success',
                'trend': trend,
                'growth_rate': growth_rate,
                'total_sales': total_sales,
                'avg_monthly_sales': avg_monthly_sales,
                'invoice_count': len(invoices)
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في تحليل اتجاه المبيعات: {str(e)}'
            }

    def analyze_client_segments(self, user_id: int) -> dict:
        """تحليل شرائح العملاء"""
        try:
            if not Client or not Invoice:
                return {
                    'status': 'error',
                    'message': 'لا يمكنني الوصول إلى بيانات العملاء'
                }

            clients = Client.query.filter_by(user_id=user_id).all()

            if len(clients) < 2:
                return {
                    'status': 'error',
                    'message': 'لا توجد بيانات كافية لتحليل شرائح العملاء'
                }

            # تحليل بسيط للعملاء
            client_analysis = []
            for client in clients:
                client_invoices = Invoice.query.filter_by(user_id=user_id, client_id=client.id).all()
                total_sales = sum(inv.total_amount for inv in client_invoices)
                invoice_count = len(client_invoices)
                avg_invoice = total_sales / invoice_count if invoice_count > 0 else 0

                # تصنيف العميل
                if total_sales > 10000 and invoice_count > 5:
                    segment = 'VIP'
                elif total_sales > 5000:
                    segment = 'عالي القيمة'
                elif invoice_count > 3:
                    segment = 'نشط'
                else:
                    segment = 'عادي'

                client_analysis.append({
                    'client_id': client.id,
                    'client_name': client.name,
                    'total_sales': total_sales,
                    'invoice_count': invoice_count,
                    'avg_invoice': avg_invoice,
                    'segment': segment
                })

            # حساب توزيع الشرائح
            segments = {}
            for analysis in client_analysis:
                segment = analysis['segment']
                if segment in segments:
                    segments[segment] += 1
                else:
                    segments[segment] = 1

            return {
                'status': 'success',
                'client_segments': client_analysis,
                'segment_distribution': segments,
                'optimal_clusters': len(segments),
                'total_clients': len(clients)
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في تحليل شرائح العملاء: {str(e)}'
            }

    def _get_business_analysis(self, user_id: int) -> str:
        """تحليل الأعمال العام"""
        try:
            # تحليل المبيعات
            sales_result = self.analyze_sales_trend(user_id)

            # تحليل العملاء
            clients_result = self.analyze_client_segments(user_id)

            analysis = "📊 **تحليل الأعمال الشامل:**\n\n"

            # إضافة تحليل المبيعات
            if sales_result['status'] == 'success':
                trend_text = 'صاعد' if sales_result['trend'] == 'upward' else 'هابط' if sales_result['trend'] == 'downward' else 'مستقر'
                analysis += f"📈 **اتجاه المبيعات:** {trend_text}\n"
                analysis += f"💰 **إجمالي المبيعات:** {sales_result['total_sales']:,.2f}\n"
                analysis += f"📊 **معدل النمو:** {sales_result['growth_rate']:+.1f}%\n\n"

            # إضافة تحليل العملاء
            if clients_result['status'] == 'success':
                analysis += f"👥 **تحليل العملاء:**\n"
                analysis += f"📊 **إجمالي العملاء:** {clients_result['total_clients']}\n"
                analysis += f"🎯 **شرائح العملاء:** {clients_result['optimal_clusters']}\n\n"

                # إضافة توزيع الشرائح
                for segment, count in clients_result['segment_distribution'].items():
                    analysis += f"• {segment}: {count} عميل\n"

            analysis += "\n💡 **نصيحة:** يمكنك طلب تحليل أكثر تفصيلاً لأي جانب من هذه الجوانب."

            return analysis

        except Exception as e:
            return f"عذراً، حدث خطأ في التحليل: {str(e)}"

    # ===== طرق المساعدة =====

    # OpenAI functions removed - using Google Gemini AI only

    def _generate_fallback_response(self, message: str) -> str:
        """توليد رد احتياطي"""
        _ = message  # متغير غير مستخدم

        fallback_responses = [
            "أعتذر، لم أتمكن من فهم طلبك بوضوح. يمكنك إعادة صياغة السؤال أو طلب المساعدة.",
            "يبدو أن سؤالك معقد. يمكنني مساعدتك في الأمور المحاسبية الأساسية مثل الفواتير والعملاء والتقارير.",
            "لم أتمكن من معالجة طلبك حالياً. جرب أسئلة مثل: 'اظهر الفواتير' أو 'احسب الأرباح' أو 'حلل المبيعات'.",
            "أحتاج إلى مزيد من التوضيح. يمكنك سؤالي عن الفواتير، العملاء، المشاريع، أو المبادئ المحاسبية."
        ]

        if HAS_NUMPY and np is not None:
            return np.random.choice(fallback_responses)
        else:
            import random
            return random.choice(fallback_responses)

    # ===== الطرق المحسنة الجديدة =====

    def _handle_greeting_enhanced(self, user_id: int) -> str:
        """التعامل مع التحيات المحسن مع التخصيص"""
        try:
            # تحديد نوع المستخدم بناءً على التفاعلات السابقة
            user_type = self._determine_user_type(user_id)

            # الحصول على الردود المناسبة من التكوين
            from ai_agent.config import AIAgentConfig
            responses = AIAgentConfig.RESPONSE_CONFIG['contextual_responses'].get(user_type,
                       AIAgentConfig.RESPONSE_CONFIG['greeting_responses'])

            # اختيار رد مناسب
            if HAS_NUMPY and np is not None:
                base_response = np.random.choice(responses)
            else:
                import random
                base_response = random.choice(responses)

            # تخصيص الرد بناءً على تفضيلات المستخدم
            if user_id in self.user_preferences:
                prefs = self.user_preferences[user_id]
                if prefs.get('favorite_topics'):
                    top_topic = max(prefs['favorite_topics'], key=prefs['favorite_topics'].get)
                    if '{favorite_topic}' in base_response:
                        base_response = base_response.format(favorite_topic=top_topic)
                    if '{suggestion}' in base_response:
                        suggestion = self._get_personalized_suggestion(user_id)
                        base_response = base_response.format(suggestion=suggestion)

            return base_response

        except Exception as e:
            logger.error(f"Error in enhanced greeting: {str(e)}")
            return self._handle_greeting(user_id)

    def _handle_accounting_question_enhanced(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع الأسئلة المحاسبية المحسن مع OpenAI"""
        try:
            # محاولة الإجابة من قاعدة المعرفة أولاً
            knowledge_response = self._handle_accounting_question(message, keywords, user_id)

            # استخدام Google Gemini AI للحصول على معلومات إضافية
            if GEMINI_AVAILABLE and ("لم أتمكن" in knowledge_response or "أعتذر" in knowledge_response):
                gemini_response = self._process_with_gemini(message, user_id)
                if gemini_response and len(gemini_response) > 50:  # إجابة مفيدة
                    # دمج المعرفة المحلية مع Gemini AI
                    combined_response = f"{gemini_response}\n\n💡 **معلومة إضافية من قاعدة معرفتي:**\n{knowledge_response}"
                    return combined_response

            return knowledge_response

        except Exception as e:
            logger.error(f"Error in enhanced accounting question: {str(e)}")
            return self._handle_accounting_question(message, keywords, user_id)

    def _handle_calculation_enhanced(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع الحسابات المحسن مع تحليل ذكي"""
        try:
            # الحساب الأساسي
            basic_result = self._handle_calculation(message, keywords, user_id)

            # إضافة تحليل ذكي للنتائج
            if "ربح" in basic_result or "مبيعات" in basic_result:
                # إضافة مقارنات وتوصيات
                enhanced_analysis = self._add_intelligent_analysis(basic_result, user_id)
                return f"{basic_result}\n\n{enhanced_analysis}"

            return basic_result

        except Exception as e:
            logger.error(f"Error in enhanced calculation: {str(e)}")
            return self._handle_calculation(message, keywords, user_id)

    def _handle_analysis_request_enhanced(self, message: str, keywords: List[str], user_id: int) -> str:
        """التعامل مع طلبات التحليل المحسن مع AI"""
        try:
            # التحليل الأساسي
            basic_analysis = self._handle_analysis_request(message, keywords, user_id)

            # إضافة تحليل متقدم باستخدام Google Gemini AI
            if GEMINI_AVAILABLE:
                ai_insights = self._generate_gemini_insights(basic_analysis, message, user_id)
                if ai_insights:
                    return f"{basic_analysis}\n\n🤖 **رؤى ذكية من Gemini AI:**\n{ai_insights}"

            return basic_analysis

        except Exception as e:
            logger.error(f"Error in enhanced analysis: {str(e)}")
            return self._handle_analysis_request(message, keywords, user_id)

    def _handle_general_query_enhanced(self, message: str, user_id: int) -> str:
        """التعامل مع الاستعلامات العامة المحسن"""
        try:
            if GEMINI_AVAILABLE:
                # استخدام Google Gemini AI مع سياق محاسبي محسن
                result = self._process_with_gemini(message, user_id)
                if isinstance(result, dict):
                    return result.get('response', str(result))
                return str(result)
            else:
                return self._generate_smart_fallback_response(message, user_id)

        except Exception as e:
            logger.error(f"Error in enhanced general query: {str(e)}")
            return self._handle_general_query(message, user_id)

    def _update_user_preferences_enhanced(self, user_id: int, intent: str, keywords: List[str], confidence: float):
        """تحديث تفضيلات المستخدم المحسن مع التعلم"""
        try:
            # التحديث الأساسي
            self._update_user_preferences(user_id, intent, keywords)

            # إضافة معلومات متقدمة
            if user_id not in self.user_preferences:
                self.user_preferences[user_id] = {
                    'frequent_intents': {},
                    'favorite_topics': {},
                    'interaction_count': 0,
                    'confidence_scores': [],
                    'expertise_level': 'beginner',
                    'last_interaction': datetime.now(),
                    'learning_progress': {}
                }

            prefs = self.user_preferences[user_id]

            # التأكد من وجود confidence_scores
            if 'confidence_scores' not in prefs:
                prefs['confidence_scores'] = []

            prefs['confidence_scores'].append(confidence)
            prefs['last_interaction'] = datetime.now()

            # تحديد مستوى الخبرة
            avg_confidence = sum(prefs['confidence_scores'][-10:]) / min(len(prefs['confidence_scores']), 10)
            if avg_confidence > 0.8 and prefs['interaction_count'] > 20:
                prefs['expertise_level'] = 'expert'
            elif avg_confidence > 0.6 and prefs['interaction_count'] > 10:
                prefs['expertise_level'] = 'intermediate'

            # تحديث تقدم التعلم
            if 'learning_progress' not in prefs:
                prefs['learning_progress'] = {}
            if intent not in prefs['learning_progress']:
                prefs['learning_progress'][intent] = {'count': 0, 'avg_confidence': 0}

            progress = prefs['learning_progress'][intent]
            progress['count'] += 1
            progress['avg_confidence'] = (progress['avg_confidence'] * (progress['count'] - 1) + confidence) / progress['count']

        except Exception as e:
            logger.error(f"Error updating enhanced user preferences: {str(e)}")

    def _generate_suggestions_enhanced(self, intent: str, keywords: List[str], user_id: int) -> List[str]:
        """توليد اقتراحات محسنة مخصصة للمستخدم"""
        try:
            # الاقتراحات الأساسية
            basic_suggestions = self._generate_suggestions(intent, keywords, user_id)

            # تخصيص الاقتراحات بناءً على تفضيلات المستخدم
            if user_id in self.user_preferences:
                prefs = self.user_preferences[user_id]
                expertise_level = prefs.get('expertise_level', 'beginner')

                # اقتراحات مخصصة حسب مستوى الخبرة
                if expertise_level == 'expert':
                    expert_suggestions = [
                        "قدم تحليل متقدم للنسب المالية",
                        "اظهر توقعات التدفق النقدي",
                        "حلل المخاطر المالية",
                        "قارن مع المعايير الصناعية"
                    ]
                    basic_suggestions.extend(expert_suggestions[:2])
                elif expertise_level == 'intermediate':
                    intermediate_suggestions = [
                        "اشرح العلاقة بين هذه البيانات",
                        "قدم نصائح للتحسين",
                        "اظهر الاتجاهات الزمنية"
                    ]
                    basic_suggestions.extend(intermediate_suggestions[:1])

                # إضافة اقتراحات بناءً على المواضيع المفضلة
                if prefs.get('favorite_topics'):
                    top_topics = sorted(prefs['favorite_topics'].items(), key=lambda x: x[1], reverse=True)[:2]
                    for topic, _ in top_topics:
                        if topic not in ' '.join(basic_suggestions):
                            basic_suggestions.append(f"اظهر معلومات عن {topic}")

            return basic_suggestions[:6]  # إرجاع أفضل 6 اقتراحات

        except Exception as e:
            logger.error(f"Error generating enhanced suggestions: {str(e)}")
            return self._generate_suggestions(intent, keywords, user_id)

    def _determine_user_type(self, user_id: int) -> str:
        """تحديد نوع المستخدم بناءً على التفاعلات السابقة"""
        try:
            if user_id not in self.user_preferences:
                return 'first_time_user'

            prefs = self.user_preferences[user_id]
            interaction_count = prefs.get('interaction_count', 0)
            expertise_level = prefs.get('expertise_level', 'beginner')

            if interaction_count == 0:
                return 'first_time_user'
            elif expertise_level == 'expert':
                return 'expert_user'
            else:
                return 'returning_user'

        except Exception as e:
            logger.error(f"Error determining user type: {str(e)}")
            return 'returning_user'

    def _get_personalized_suggestion(self, user_id: int) -> str:
        """الحصول على اقتراح مخصص للمستخدم"""
        try:
            if user_id not in self.user_preferences:
                return "تحليل الأعمال"

            prefs = self.user_preferences[user_id]

            # اقتراح بناءً على أكثر المواضيع استخداماً
            if prefs.get('frequent_intents'):
                top_intent = max(prefs['frequent_intents'], key=prefs['frequent_intents'].get)
                suggestions_map = {
                    'calculation': 'حساب النسب المالية',
                    'analysis_request': 'تحليل اتجاه المبيعات',
                    'data_query': 'مراجعة تقارير الأداء',
                    'accounting_question': 'تعلم مبادئ محاسبية جديدة'
                }
                return suggestions_map.get(top_intent, 'تحليل الأعمال')

            return 'تحليل الأعمال'

        except Exception as e:
            logger.error(f"Error getting personalized suggestion: {str(e)}")
            return 'تحليل الأعمال'

    # OpenAI accounting function removed - using Google Gemini AI only

    # OpenAI enhanced function removed - using Google Gemini AI only

    def _generate_smart_fallback_response(self, message: str, user_id: int) -> str:
        """نظام بديل ذكي عندما لا تتوفر OpenAI"""
        try:
            # تحليل الرسالة لتحديد النوع
            message_lower = message.lower()

            # أسئلة محاسبية
            if any(term in message_lower for term in ['محاسبة', 'مبدأ', 'قائمة', 'ميزانية']):
                return self._handle_accounting_question_enhanced(message, [], user_id)

            # طلبات حسابية
            elif any(term in message_lower for term in ['احسب', 'حساب', 'كم', 'مجموع']):
                return self._handle_calculation_enhanced(message, [], user_id)

            # طلبات تحليل
            elif any(term in message_lower for term in ['تحليل', 'تقرير', 'اتجاه']):
                return self._handle_analysis_request_enhanced(message, [], user_id)

            # استعلامات البيانات
            elif any(term in message_lower for term in ['اظهر', 'عرض', 'قائمة']):
                return self._handle_data_query(message, [], user_id)

            # رد عام ذكي
            else:
                return f"""🤖 **مساعد SmartBiz الذكي**

أعتذر، خدمة الذكاء الاصطناعي المتقدمة غير متاحة حالياً، لكن يمكنني مساعدتك في:

📊 **التحليلات:**
- تحليل المبيعات والأرباح
- تقارير الأداء المالي
- اتجاهات الأعمال

💰 **الحسابات:**
- حساب الأرباح والخسائر
- النسب المالية
- التدفق النقدي

📋 **البيانات:**
- عرض الفواتير والعملاء
- ملخص المشاريع
- الإحصائيات العامة

📚 **التعليم:**
- شرح المبادئ المحاسبية
- تعريف المصطلحات
- أمثلة عملية

**جرب أن تسأل:**
- "احسب إجمالي الأرباح"
- "اظهر قائمة العملاء"
- "ما هو مبدأ الاستحقاق؟"

كيف يمكنني مساعدتك؟ 😊"""

        except Exception as e:
            logger.error(f"Error in smart fallback: {str(e)}")
            return "أعتذر، حدث خطأ مؤقت. يرجى المحاولة مرة أخرى."

    def _process_with_gemini(self, message: str, user_id: int) -> str:
        """معالجة الرسالة باستخدام Google Gemini AI مع تحسينات متقدمة وخوارزميات تعلم محسنة"""
        try:
            if not GEMINI_AVAILABLE or not GEMINI_API_KEY:
                # محاولة استخدام OpenAI كبديل إذا كان متاحاً
                openai_key = os.environ.get('OPENAI_API_KEY')
                if openai_key and openai_key != 'disabled':
                    return self._process_with_openai_fallback(message, user_id)
                return self._generate_smart_fallback_response(message, user_id)

            # إعداد السياق المحسن للمستخدم مع تحليل متقدم
            context = self._build_enhanced_context(user_id)
            
            # تحليل الرسالة باستخدام خوارزميات التعلم المتقدمة
            message_analysis = self._analyze_message_with_learning(message, user_id)
            
            # الحصول على إعدادات Gemini المحسنة من متغيرات البيئة
            gemini_model = os.environ.get('GEMINI_MODEL', 'gemini-pro')
            gemini_max_tokens = int(os.environ.get('GEMINI_MAX_TOKENS', 4000))
            gemini_temperature = float(os.environ.get('GEMINI_TEMPERATURE', 0.7))
            gemini_top_k = int(os.environ.get('GEMINI_TOP_K', 40))
            gemini_top_p = float(os.environ.get('GEMINI_TOP_P', 0.95))

            # إعداد الرسالة مع السياق المحسن والتحليل المتقدم
            enhanced_prompt = f"""أنت مساعد ذكي متخصص في المحاسبة والأعمال لنظام SmartBiz.  

السياق الحالي للمستخدم:
{context}

تحليل الرسالة:
{message_analysis}

تعليمات مهمة:
1. أجب باللغة العربية بطريقة مهنية ومفيدة
2. قدم نصائح عملية وقابلة للتطبيق في المحاسبة
3. استخدم أمثلة من الواقع العملي
4. اربط إجابتك بنظام SmartBiz عند الإمكان
5. قدم اقتراحات للخطوات التالية
6. كن مختصراً ومفيداً
7. استخدم تنسيق Markdown لتحسين قراءة الإجابة
8. قدم إجابات دقيقة ومفصلة
9. استخدم الرسوم البيانية والجداول عند الحاجة
10. قدم تحليلات مالية متقدمة عندما يكون ذلك مناسباً

سؤال المستخدم: {message}

الرجاء تقديم إجابة شاملة ومفيدة:"""

            # محاولة استخدام Gemini AI مع معالجة الأخطاء
            try:
                # استخدام المكتبة الرسمية إذا كانت متاحة
                if genai and hasattr(genai, 'GenerativeModel'):
                    # استخدام المكتبة الرسمية
                    logger.info("🤖 استخدام مكتبة Google Gemini AI الرسمية")
                    
                    # إعداد نموذج Gemini (سيتم استخدام الإعدادات الافتراضية)
                    
                    try:
                        # إنشاء النموذج
                        model = genai.GenerativeModel(gemini_model)  # type: ignore

                        # إرسال الطلب
                        response = model.generate_content(enhanced_prompt)

                        # معالجة الاستجابة
                        if response and hasattr(response, 'text'):
                            ai_text = response.text
                            # تحسين الرد وإضافة رؤى إضافية
                            enhanced_response = self._enhance_gemini_response(ai_text, message, user_id)
                            # تسجيل التفاعل الناجح للتعلم
                            self._log_successful_interaction(message, enhanced_response, user_id)
                            # تحديث نظام التعلم التدريجي
                            if hasattr(self, '_update_learning_system'):
                                self._update_learning_system(message, enhanced_response, user_id)
                            return enhanced_response
                    except Exception as model_error:
                        logger.warning(f"فشل في استخدام نموذج Gemini: {model_error}")
                        # سنستخدم requests كبديل
                else:
                    # استخدام requests للاتصال المباشر بـ Gemini API
                    logger.info("🤖 استخدام requests للاتصال المباشر بـ Gemini API")
                    import requests

                    api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{gemini_model}:generateContent?key={GEMINI_API_KEY}"

                    # إعداد الطلب مع المعلمات المحسنة
                    payload = {
                        "contents": [{
                            "parts": [{
                                "text": enhanced_prompt
                            }]
                        }],
                        "generationConfig": {
                            "temperature": gemini_temperature,
                            "maxOutputTokens": gemini_max_tokens,
                            "topK": gemini_top_k,
                            "topP": gemini_top_p
                        },
                        "safetySettings": [
                            {
                                "category": "HARM_CATEGORY_HARASSMENT",
                                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                            },
                            {
                                "category": "HARM_CATEGORY_HATE_SPEECH",
                                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                            },
                            {
                                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                            },
                            {
                                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                            }
                        ]
                    }

                    headers = {
                        'Content-Type': 'application/json'
                    }

                    # إرسال الطلب مع زيادة مهلة الانتظار
                    response = requests.post(api_url, json=payload, headers=headers, timeout=60)

                    # معالجة الاستجابة
                    if response.status_code == 200:
                        result = response.json()
                        if 'candidates' in result and len(result['candidates']) > 0:
                            if 'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content']:
                                ai_text = result['candidates'][0]['content']['parts'][0]['text']
                                # تحسين الرد وإضافة رؤى إضافية
                                enhanced_response = self._enhance_gemini_response(ai_text, message, user_id)
                                # تسجيل التفاعل الناجح للتعلم
                                self._log_successful_interaction(message, enhanced_response, user_id)
                                # تحديث نظام التعلم التدريجي
                                self._update_learning_system(message, enhanced_response, user_id)
                                return enhanced_response
                    else:
                        # تسجيل خطأ API
                        logger.error(f"Gemini API error: Status {response.status_code}, Response: {response.text}")
                        # محاولة استخدام OpenAI كبديل
                        openai_key = os.environ.get('OPENAI_API_KEY')
                        if openai_key and openai_key != 'disabled':
                            return self._process_with_openai_fallback(message, user_id)

                return self._generate_smart_fallback_response(message, user_id)

            except Exception as gemini_error:
                logger.error(f"Gemini API error: {str(gemini_error)}")
                # محاولة استخدام OpenAI كبديل
                openai_key = os.environ.get('OPENAI_API_KEY')
                if openai_key and openai_key != 'disabled':
                    return self._process_with_openai_fallback(message, user_id)
                return self._generate_smart_fallback_response(message, user_id)

        except Exception as e:
            logger.error(f"Error in Gemini AI processing: {str(e)}")
            # في حالة الخطأ، استخدم النظام البديل الذكي
            return self._generate_smart_fallback_response(message, user_id)

    def _enhance_gemini_response(self, ai_response: str, original_message: str, user_id: int) -> str:
        """تحسين رد Gemini AI بإضافة رؤى إضافية وتعزيز قدرات التعلم"""
        try:
            # تنظيف الرد
            cleaned_response = ai_response.strip()
            
            # الحصول على مستوى خبرة المستخدم
            expertise_level = self._get_user_expertise_level(user_id)
            
            # تحليل الرسالة باستخدام خوارزميات التعلم المتقدمة
            message_analysis = self._analyze_message_with_learning(original_message, user_id) if hasattr(self, '_analyze_message_with_learning') else {}
            
            # استخراج الكلمات المفتاحية والنية من التحليل
            keywords = message_analysis.get('keywords', [])
            if not keywords and HAS_ADVANCED_NLP:
                keywords = self.nlp.extract_keywords(original_message)
            
            # إضافة اقتراحات ذكية بناءً على المحتوى
            suggestions = self._generate_contextual_suggestions(original_message, user_id)
            
            # إضافة اقتراحات مخصصة بناءً على مستوى الخبرة
            expertise_suggestions = []
            
            if expertise_level == 'مبتدئ':
                expertise_suggestions.append("💡 **نصيحة للمبتدئين:** تعرف على المصطلحات المحاسبية الأساسية لفهم أفضل.")
            elif expertise_level == 'متوسط':
                expertise_suggestions.append("💡 **نصيحة للمستوى المتوسط:** جرب تطبيق هذه المفاهيم على بيانات شركتك الفعلية.")
            elif expertise_level == 'خبير':
                expertise_suggestions.append("💡 **نصيحة للخبراء:** فكر في كيفية دمج هذه المعلومات مع استراتيجيات الأعمال المتقدمة.")
            
            # إضافة روابط للمعرفة ذات الصلة
            related_knowledge = []
            
            for keyword in keywords[:3]:  # استخدام أهم 3 كلمات مفتاحية
                for principle, data in self.accounting_knowledge.get('accounting_principles', {}).items():
                    if keyword.lower() in [k.lower() for k in data.get('keywords', [])]:
                        related_knowledge.append(f"📚 **مبدأ ذو صلة:** {principle} - {data.get('definition', '')}")
                        break
            
            # إضافة تحليلات ذكية إذا كانت متاحة
            smart_insights = []
            if HAS_ANALYTICS and 'SmartBizAnalytics' in globals() and SmartBizAnalytics is not None:
                # هذا مجرد مثال، يمكن تنفيذ تحليلات حقيقية هنا
                smart_insights.append("📊 **تحليل ذكي:** بناءً على أنماط الاستخدام، قد تستفيد من مراجعة تقارير التدفق النقدي بشكل أكثر انتظاماً.")
            
            # دمج كل الإضافات مع الرد الأصلي
            enhanced_parts = [cleaned_response]
            
            # إضافة الاقتراحات الأصلية
            if suggestions:
                enhanced_parts.append("\n\n💡 **اقتراحات إضافية:**\n" + chr(10).join(f"• {suggestion}" for suggestion in suggestions))
            
            # إضافة اقتراحات مستوى الخبرة
            if expertise_suggestions and not suggestions:  # إضافة فقط إذا لم تكن هناك اقتراحات أخرى
                enhanced_parts.append("\n\n" + expertise_suggestions[0])
                
            # إضافة المعرفة ذات الصلة
            if related_knowledge:
                enhanced_parts.append("\n\n" + "\n".join(related_knowledge[:2]))  # إضافة أهم معرفتين ذات صلة
                
            # إضافة التحليلات الذكية بنسبة 70%
            if smart_insights and random.random() < 0.7:  
                enhanced_parts.append("\n\n" + smart_insights[0])
            
            # إضافة دعوة للعمل في النهاية
            enhanced_parts.append("\n\n💬 **هل تحتاج إلى مزيد من المعلومات حول هذا الموضوع؟**")
            
            # إضافة توقيع Gemini AI
            enhanced_parts.append("\n\n🤖 **مدعوم بـ Google Gemini AI**")
            
            return "\n".join(enhanced_parts)
            
        except Exception as e:
            logger.error(f"Error enhancing Gemini response: {str(e)}")
            return ai_response
            
    # OpenAI fallback functions removed - using Google Gemini AI only

    # OpenAI enhancement function removed
            
    def _process_with_openai_fallback(self, message: str, user_id: int) -> str:
        """معالجة الرسالة باستخدام OpenAI كبديل عندما يفشل Gemini"""
        try:
            # الحصول على مفتاح OpenAI API
            openai_api_key = os.environ.get('OPENAI_API_KEY')
            if not openai_api_key or openai_api_key == 'disabled':
                return self._generate_smart_fallback_response(message, user_id)
                
            # إعداد السياق المحسن للمستخدم
            context = self._build_enhanced_context(user_id)
            
            # الحصول على إعدادات OpenAI من متغيرات البيئة
            openai_model = os.environ.get('OPENAI_MODEL', 'gpt-3.5-turbo')
            openai_max_tokens = int(os.environ.get('OPENAI_MAX_TOKENS', 1000))
            openai_temperature = float(os.environ.get('OPENAI_TEMPERATURE', 0.7))
            
            # إعداد الرسالة مع السياق المحسن
            enhanced_prompt = f"""أنت مساعد ذكي متخصص في المحاسبة والأعمال لنظام SmartBiz.

السياق الحالي للمستخدم:
{context}

تعليمات مهمة:
1. أجب باللغة العربية بطريقة مهنية ومفيدة
2. قدم نصائح عملية وقابلة للتطبيق في المحاسبة
3. استخدم أمثلة من الواقع العملي
4. اربط إجابتك بنظام SmartBiz عند الإمكان
5. قدم اقتراحات للخطوات التالية
6. كن مختصراً ومفيداً
7. استخدم تنسيق Markdown لتحسين قراءة الإجابة

سؤال المستخدم: {message}

الرجاء تقديم إجابة شاملة ومفيدة:"""
            
            # استخدام requests للاتصال المباشر بـ OpenAI API
            import requests
            
            api_url = "https://api.openai.com/v1/chat/completions"
            
            payload = {
                "model": openai_model,
                "messages": [
                    {"role": "system", "content": "أنت مساعد ذكي متخصص في المحاسبة والأعمال لنظام SmartBiz."},
                    {"role": "user", "content": enhanced_prompt}
                ],
                "max_tokens": openai_max_tokens,
                "temperature": openai_temperature
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {openai_api_key}"
            }
            
            response = requests.post(api_url, json=payload, headers=headers, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    ai_text = result['choices'][0]['message']['content']
                    # تحسين الرد وإضافة رؤى إضافية
                    enhanced_response = self._enhance_openai_response(ai_text, message, user_id)
                    # تسجيل التفاعل الناجح للتعلم
                    self._log_successful_interaction(message, enhanced_response, user_id)
                    return enhanced_response
            
            return self._generate_smart_fallback_response(message, user_id)
            
        except Exception as e:
            logger.error(f"Error in OpenAI fallback processing: {str(e)}")
            return self._generate_smart_fallback_response(message, user_id)
    
    def _enhance_openai_response(self, ai_response: str, original_message: str, user_id: int) -> str:
        """تحسين رد OpenAI بإضافة رؤى إضافية"""
        try:
            # تنظيف الرد
            cleaned_response = ai_response.strip()
            
            # إضافة اقتراحات ذكية بناءً على المحتوى
            suggestions = self._generate_contextual_suggestions(original_message, user_id)
            
            if suggestions:
                enhanced_response = f"""{cleaned_response}

💡 **اقتراحات إضافية:**
{chr(10).join(f"• {suggestion}" for suggestion in suggestions)}

🤖 **مدعوم بـ OpenAI**"""
            else:
                enhanced_response = f"""{cleaned_response}

🤖 **مدعوم بـ OpenAI**"""
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing OpenAI response: {str(e)}")
            return ai_response
    
    def _log_successful_interaction(self, message: str, response: str, user_id: int) -> None:
        """تسجيل التفاعل الناجح للتعلم"""
        try:
            # تحليل الرسالة لاستخراج الكلمات المفتاحية والنية
            keywords = self.nlp.extract_keywords(message) if hasattr(self.nlp, 'extract_keywords') else []
            intent = self._detect_intent(message, keywords)
            confidence = 0.9  # قيمة افتراضية عالية للتفاعلات الناجحة

            # إضافة التفاعل إلى الذاكرة قصيرة المدى
            interaction = {
                'user_id': user_id,
                'message': message,
                'response': response,
                'keywords': keywords,
                'intent': intent,
                'confidence': confidence,
                'timestamp': datetime.now(),
                'feedback': None  # سيتم تحديثه لاحقاً إذا قدم المستخدم تغذية راجعة
            }

            self.memory_system['short_term'].append(interaction)

            # تحديث نظام التعلم التدريجي
            if self.incremental_learning['enabled']:
                self._update_incremental_learning(user_id, intent, keywords, confidence)

            # تحديث تفضيلات المستخدم
            self._update_user_preferences_enhanced(user_id, intent, keywords, confidence)

            # إدارة نظام الذاكرة
            if len(self.memory_system['short_term']) > 100:  # إذا كان هناك أكثر من 100 تفاعل
                self._manage_memory_system()

        except Exception as e:
            logger.error(f"Error logging successful interaction: {str(e)}")

    def _generate_contextual_suggestions(self, message: str, user_id: int) -> List[str]:
        """توليد اقتراحات سياقية ذكية"""
        try:
            suggestions = []
            message_lower = message.lower()

            # اقتراحات بناءً على نوع السؤال
            if any(term in message_lower for term in ['فاتورة', 'فواتير']):
                suggestions.extend([
                    "إنشاء فاتورة جديدة",
                    "عرض الفواتير المعلقة",
                    "تحليل إيرادات الفواتير"
                ])

            elif any(term in message_lower for term in ['عميل', 'عملاء']):
                suggestions.extend([
                    "إضافة عميل جديد",
                    "عرض تقرير العملاء",
                    "تحليل سلوك العملاء"
                ])

            elif any(term in message_lower for term in ['ربح', 'أرباح']):
                suggestions.extend([
                    "عرض تقرير الأرباح والخسائر",
                    "تحليل هوامش الربح",
                    "مقارنة الأرباح الشهرية"
                ])

            elif any(term in message_lower for term in ['تقرير', 'تحليل']):
                suggestions.extend([
                    "إنشاء تقرير مالي شامل",
                    "تحليل الاتجاهات المالية",
                    "عرض المؤشرات الرئيسية"
                ])

            # اقتراحات عامة مفيدة
            if len(suggestions) < 3:
                general_suggestions = [
                    "عرض لوحة التحكم",
                    "إنشاء تقرير سريع",
                    "مراجعة الإعدادات",
                    "تصدير البيانات",
                    "عرض الإحصائيات"
                ]
                suggestions.extend(general_suggestions[:3-len(suggestions)])

            return suggestions[:3]  # أقصى 3 اقتراحات

        except Exception as e:
            logger.error(f"Error generating contextual suggestions: {str(e)}")
            return []

    def _generate_gemini_insights(self, basic_analysis: str, original_message=None, user_id=None) -> str:
        """توليد رؤى ذكية إضافية باستخدام Google Gemini AI"""
        try:
            if not GEMINI_AVAILABLE or not GEMINI_API_KEY:
                return ""

            # إعداد السياق المحسن للتحليل
            context = f"""بناءً على التحليل التالي، قدم رؤى إضافية وتوصيات عملية:

            التحليل الأساسي: {basic_analysis}

            قدم:
            1. تفسير للنتائج
            2. توصيات للتحسين
            3. تحذيرات من المخاطر المحتملة
            4. خطوات عملية للتنفيذ

            اجعل الرد مختصراً ومفيداً (أقل من 200 كلمة) باللغة العربية."""

            # استخدام Google Gemini AI
            try:
                # استخدام المكتبة الرسمية إذا كانت متاحة
                if genai and hasattr(genai, 'GenerativeModel'):
                    logger.info("🤖 استخدام مكتبة Google Gemini AI الرسمية لتوليد الرؤى")

                    try:
                        # إنشاء النموذج
                        model = genai.GenerativeModel('gemini-pro')  # type: ignore

                        # إرسال الطلب
                        response = model.generate_content(context)

                        # معالجة الاستجابة
                        if response and hasattr(response, 'text'):
                            return response.text.strip()
                    except Exception as model_error:
                        logger.warning(f"فشل في استخدام نموذج Gemini: {model_error}")
                        # سنستخدم requests كبديل
                else:
                    # استخدام requests للاتصال المباشر
                    logger.info("🤖 استخدام requests للاتصال المباشر بـ Gemini API لتوليد الرؤى")
                    import requests

                    api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={GEMINI_API_KEY}"

                    payload = {
                        "contents": [{
                            "parts": [{
                                "text": context
                            }]
                        }],
                        "generationConfig": {
                            "temperature": 0.4,
                            "maxOutputTokens": 500,
                            "topK": 40,
                            "topP": 0.95
                        }
                    }

                    headers = {
                        'Content-Type': 'application/json'
                    }

                    response = requests.post(api_url, json=payload, headers=headers, timeout=30)

                    if response.status_code == 200:
                        result = response.json()
                        if 'candidates' in result and len(result['candidates']) > 0:
                            if 'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content']:
                                return result['candidates'][0]['content']['parts'][0]['text'].strip()

                return ""

            except Exception as e:
                logger.error(f"Error generating Gemini insights: {str(e)}")
                return ""

        except Exception as e:
            logger.error(f"Error generating Gemini insights: {str(e)}")
            return ""

    def _add_intelligent_analysis(self, basic_result: str, user_id: int) -> str:
        """إضافة تحليل ذكي للنتائج المالية"""
        try:
            # استخراج الأرقام من النتيجة الأساسية
            import re
            numbers = re.findall(r'[\d,]+\.?\d*', basic_result)

            if not numbers:
                return "💡 **نصيحة:** تأكد من دقة البيانات المدخلة للحصول على تحليل أفضل."

            # تحليل بسيط للاتجاهات
            analysis_parts = []

            # إضافة مقارنات تاريخية إذا كانت متاحة
            if "نمو" in basic_result or "%" in basic_result:
                analysis_parts.append("📈 **تحليل الاتجاه:** النتائج تظهر تطوراً في الأداء المالي.")

            # إضافة توصيات عامة
            if "ربح" in basic_result:
                analysis_parts.append("💰 **توصية:** راقب هامش الربح وابحث عن فرص لتحسين الكفاءة.")

            if "مبيعات" in basic_result:
                analysis_parts.append("🎯 **نصيحة:** ركز على العملاء الأكثر ربحية وطور استراتيجيات الاحتفاظ بهم.")

            # إضافة تحليل ذكي باستخدام Google Gemini AI إذا كان متاحاً
            if GEMINI_AVAILABLE:
                ai_analysis = self._generate_gemini_insights(basic_result, "", user_id)
                if ai_analysis:
                    analysis_parts.append(f"🤖 **تحليل ذكي من Gemini AI:** {ai_analysis}")

            return "\n".join(analysis_parts) if analysis_parts else "💡 **نصيحة:** استمر في مراقبة هذه المؤشرات بانتظام."

        except Exception as e:
            logger.error(f"Error adding intelligent analysis: {str(e)}")
            return "💡 **نصيحة:** راجع النتائج مع خبير مالي للحصول على رؤى أعمق."

    def _get_user_expertise_level(self, user_id: int) -> str:
        """الحصول على مستوى خبرة المستخدم"""
        try:
            if user_id in self.user_preferences:
                return self.user_preferences[user_id].get('expertise_level', 'مبتدئ')
            return 'مبتدئ'
        except Exception:
            return 'مبتدئ'
            
    def _get_user_context(self, user_id: int) -> str:
        """الحصول على سياق المستخدم الحالي"""
        try:
            # البحث عن معلومات المستخدم في قاعدة البيانات إذا كانت متاحة
            user_info = ""
            if self.db and User is not None:
                user = self.db.query(User).filter(User.id == user_id).first()
                if user:
                    user_info = f"اسم المستخدم: {user.name}, البريد الإلكتروني: {user.email}"
            
            # الحصول على تفضيلات المستخدم
            user_prefs = ""
            if user_id in self.user_preferences:
                prefs = self.user_preferences[user_id]
                expertise = prefs.get('expertise_level', 'مبتدئ')
                interaction_count = prefs.get('interaction_count', 0)
                user_prefs = f"مستوى الخبرة: {expertise}, عدد التفاعلات: {interaction_count}"
            
            # الحصول على آخر المحادثات
            recent_interactions = ""
            user_interactions = [i for i in self.conversation_memory if i.get('user_id') == user_id]
            if user_interactions:
                last_interactions = user_interactions[-3:] if len(user_interactions) > 3 else user_interactions
                intents = [i.get('intent', 'غير محدد') for i in last_interactions]
                recent_interactions = f"آخر النوايا: {', '.join(intents)}"
            
            # بناء السياق الكامل
            context_parts = []
            if user_info:
                context_parts.append(user_info)
            if user_prefs:
                context_parts.append(user_prefs)
            if recent_interactions:
                context_parts.append(recent_interactions)
            
            # إضافة معلومات من نظام التعلم التدريجي
            if user_id in self.incremental_learning['user_patterns']:
                patterns = self.incremental_learning['user_patterns'][user_id]
                top_intents = sorted(patterns.items(), key=lambda x: x[1]['frequency'], reverse=True)[:2]
                if top_intents:
                    top_intents_str = ", ".join([intent for intent, _ in top_intents])
                    context_parts.append(f"الاهتمامات الرئيسية: {top_intents_str}")
            
            return "; ".join(context_parts) if context_parts else "لا يوجد سياق متاح"
            
        except Exception as e:
            logger.error(f"Error getting user context: {str(e)}")
            return "لا يوجد سياق متاح"

    def _build_conversation_context(self, user_id: int) -> str:
        """بناء سياق المحادثة من التفاعلات السابقة"""
        try:
            if not self.conversation_memory:
                return "لا يوجد سياق سابق"

            # أخذ آخر 3 تفاعلات للسياق
            recent_interactions = self.conversation_memory[-3:]
            context_parts = []

            for interaction in recent_interactions:
                if interaction.get('user_id') == user_id:
                    intent = interaction.get('intent', 'غير محدد')
                    keywords = ', '.join(interaction.get('keywords', []))
                    context_parts.append(f"النية: {intent}, الكلمات المفتاحية: {keywords}")

            return '; '.join(context_parts) if context_parts else "لا يوجد سياق سابق"

        except Exception as e:
            logger.error(f"Error building conversation context: {str(e)}")
            return "لا يوجد سياق سابق"
            
    def _build_enhanced_context(self, user_id: int) -> str:
        """بناء سياق محسن للمستخدم يشمل معلومات متقدمة"""
        try:
            context_parts = []
            
            # إضافة معلومات المستخدم الأساسية
            user_context = self._get_user_context(user_id)
            if user_context and user_context != "لا يوجد سياق متاح":
                context_parts.append(f"معلومات المستخدم: {user_context}")
            
            # إضافة سياق المحادثة
            conversation_context = self._build_conversation_context(user_id)
            if conversation_context and conversation_context != "لا يوجد سياق سابق":
                context_parts.append(f"سياق المحادثة: {conversation_context}")
            
            # إضافة معلومات من نظام التعلم التدريجي
            if user_id in self.incremental_learning['user_patterns']:
                patterns = self.incremental_learning['user_patterns'][user_id]
                # الحصول على أكثر 3 نوايا تكراراً
                top_intents = sorted(patterns.items(), key=lambda x: x[1]['frequency'], reverse=True)[:3]
                if top_intents:
                    intents_info = []
                    for intent, data in top_intents:
                        avg_confidence = data.get('avg_confidence', 0)
                        confidence_level = "عالية" if avg_confidence > 0.8 else "متوسطة" if avg_confidence > 0.6 else "منخفضة"
                        intents_info.append(f"{intent} (ثقة: {confidence_level})")
                    context_parts.append(f"الاهتمامات الرئيسية: {', '.join(intents_info)}")
            
            # إضافة معلومات من الذاكرة الدلالية
            semantic_insights = []
            if self.memory_system['semantic']:
                # الحصول على أكثر 5 كلمات مفتاحية استخداماً
                top_keywords = sorted(self.memory_system['semantic'].items(), 
                                     key=lambda x: len(x[1]), 
                                     reverse=True)[:5]
                if top_keywords:
                    keywords_info = [f"{keyword} ({len(data)})" for keyword, data in top_keywords]
                    semantic_insights.append(f"الكلمات المفتاحية الشائعة: {', '.join(keywords_info)}")
            
            if semantic_insights:
                context_parts.append('; '.join(semantic_insights))
            
            # إضافة معلومات من نماذج التعلم الآلي إذا كانت متاحة
            if HAS_SKLEARN and self.models['sales_predictor'] is not None:
                if user_id in self.user_preferences and 'learning_progress' in self.user_preferences[user_id]:
                    learning_progress = self.user_preferences[user_id]['learning_progress']
                    if learning_progress:
                        progress_info = []
                        for topic, data in learning_progress.items():
                            if isinstance(data, dict) and 'level' in data:
                                progress_info.append(f"{topic}: {data['level']}")
                        if progress_info:
                            context_parts.append(f"تقدم التعلم: {', '.join(progress_info)}")
            
            # بناء السياق النهائي
            enhanced_context = "\n".join(context_parts) if context_parts else "لا يوجد سياق متاح"
            
            return enhanced_context
            
        except Exception as e:
            logger.error(f"Error building enhanced context: {str(e)}")
            return "لا يوجد سياق متاح"

    def _update_incremental_learning(self, user_id: int, intent: str, keywords: List[str], confidence: float):
        """تحديث نظام التعلم التدريجي"""
        try:
            if not self.incremental_learning['enabled']:
                return

            # تحديث قاعدة المعرفة
            knowledge_key = f"{intent}_{user_id}"
            if knowledge_key not in self.incremental_learning['knowledge_base']:
                self.incremental_learning['knowledge_base'][knowledge_key] = {
                    'patterns': [],
                    'success_rate': 0.0,
                    'usage_count': 0
                }

            knowledge = self.incremental_learning['knowledge_base'][knowledge_key]
            knowledge['patterns'].extend(keywords)
            knowledge['usage_count'] += 1

            # تحديث معدل النجاح بناءً على الثقة
            current_rate = knowledge['success_rate']
            learning_rate = self.incremental_learning['learning_rate']
            knowledge['success_rate'] = current_rate + learning_rate * (confidence - current_rate)

            # تحديث أنماط المستخدم
            if user_id not in self.incremental_learning['user_patterns']:
                self.incremental_learning['user_patterns'][user_id] = {}

            user_patterns = self.incremental_learning['user_patterns'][user_id]
            if intent not in user_patterns:
                user_patterns[intent] = {'frequency': 0, 'avg_confidence': 0.0}

            pattern = user_patterns[intent]
            pattern['frequency'] += 1
            pattern['avg_confidence'] = (pattern['avg_confidence'] * (pattern['frequency'] - 1) + confidence) / pattern['frequency']

            # إضافة نقاط التغذية الراجعة
            self.incremental_learning['feedback_scores'].append({
                'user_id': user_id,
                'intent': intent,
                'confidence': confidence,
                'timestamp': datetime.now()
            })

            # الاحتفاظ بآخر 1000 نقطة فقط
            if len(self.incremental_learning['feedback_scores']) > 1000:
                self.incremental_learning['feedback_scores'] = self.incremental_learning['feedback_scores'][-1000:]

        except Exception as e:
            logger.error(f"Error updating incremental learning: {str(e)}")
            
    def _analyze_message_with_learning(self, message: str, user_id: int) -> str:
        """تحليل الرسالة باستخدام خوارزميات التعلم المتقدمة"""
        try:
            # استخراج الكلمات المفتاحية والنية
            cleaned_message = self._clean_message(message)
            keywords = self.nlp.extract_keywords(cleaned_message)
            intent = self._detect_intent(cleaned_message, keywords)
            
            # تحليل النص باستخدام المعالج المتقدم
            if HAS_ADVANCED_NLP:
                analysis_result = self.nlp.detect_intent_advanced(cleaned_message, user_id)
                entities = analysis_result.get('entities', {})
                sentiment = analysis_result.get('sentiment', 'محايد')
                topics = analysis_result.get('topics', [])
            else:
                entities = {}
                sentiment = 'محايد'
                topics = keywords[:3] if len(keywords) >= 3 else keywords
            
            # تحليل سياق المستخدم
            user_context = self._get_user_context(user_id)
            expertise_level = self._get_user_expertise_level(user_id)
            
            # استخدام نظام التعلم التدريجي لتحسين التحليل
            if user_id in self.incremental_learning['user_patterns']:
                user_patterns = self.incremental_learning['user_patterns'][user_id]
                frequent_intents = sorted(user_patterns.items(), key=lambda x: x[1]['frequency'], reverse=True)[:3]
                frequent_intents_str = ", ".join([f"{intent} (تكرار: {data['frequency']})" for intent, data in frequent_intents])
            else:
                frequent_intents_str = "لا توجد أنماط سابقة"
            
            # بناء تحليل متكامل
            analysis = f"""الكلمات المفتاحية: {', '.join(keywords)}
النية المكتشفة: {intent}
المشاعر: {sentiment}
المواضيع الرئيسية: {', '.join(topics)}
مستوى الخبرة: {expertise_level}
الأنماط السابقة: {frequent_intents_str}"""
            
            # إضافة تحليل الكيانات إذا وجدت
            if entities:
                entities_str = ", ".join([f"{k}: {v}" for k, v in entities.items()])
                analysis += f"\nالكيانات المكتشفة: {entities_str}"
            
            # إضافة سياق المستخدم
            if user_context:
                analysis += f"\nسياق المستخدم: {user_context}"
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in message analysis with learning: {str(e)}")
            return f"الكلمات المفتاحية: {', '.join(self.nlp.extract_keywords(message))}"
    
    def _update_learning_system(self, message: str, response: str, user_id: int):
        """تحديث نظام التعلم بناءً على التفاعل الحالي"""
        try:
            # استخراج المعلومات الأساسية
            cleaned_message = self._clean_message(message)
            keywords = self.nlp.extract_keywords(cleaned_message)
            intent = self._detect_intent(cleaned_message, keywords)
            
            # تقدير مستوى الثقة
            if HAS_ADVANCED_NLP:
                analysis_result = self.nlp.detect_intent_advanced(cleaned_message, user_id)
                confidence = analysis_result.get('confidence', 0.8)
            else:
                confidence = 0.8  # قيمة افتراضية
            
            # تحديث نظام التعلم التدريجي
            self._update_incremental_learning(user_id, intent, keywords, confidence)
            
            # تحديث نظام الذاكرة
            interaction_data = {
                'user_message': message,
                'system_response': response,
                'keywords': keywords,
                'intent': intent,
                'confidence': confidence,
                'timestamp': datetime.now(),
                'user_id': user_id
            }
            
            # إضافة إلى الذاكرة قصيرة المدى
            self.memory_system['short_term'].append(interaction_data)
            
            # تحديث الذاكرة طويلة المدى إذا كانت الثقة عالية
            if confidence > 0.7:
                self.memory_system['long_term'].append(interaction_data)
                
                # تحديث الذاكرة الدلالية
                for keyword in keywords:
                    if keyword not in self.memory_system['semantic']:
                        self.memory_system['semantic'][keyword] = []
                    self.memory_system['semantic'][keyword].append({
                        'intent': intent,
                        'confidence': confidence,
                        'timestamp': datetime.now()
                    })
            
            # إدارة حجم الذاكرة
            self._manage_memory_system()
            
            # تحديث تفضيلات المستخدم
            self._update_user_preferences_enhanced(user_id, intent, keywords, confidence)
            
            # تحديث نماذج التعلم الآلي إذا كانت متاحة
            if HAS_SKLEARN and self.models['sales_predictor'] is not None:
                # تحديث نموذج التنبؤ بالمبيعات (مثال)
                if intent == 'sales_query' and user_id in self.user_preferences:
                    self.user_preferences[user_id]['learning_progress']['sales_understanding'] = {
                        'level': 'advanced' if confidence > 0.8 else 'intermediate',
                        'last_updated': datetime.now()
                    }
            
        except Exception as e:
            logger.error(f"Error updating learning system: {str(e)}")

    def _manage_memory_system(self):
        """إدارة نظام الذاكرة المتقدم"""
        try:
            current_time = datetime.now()

            # إدارة الذاكرة قصيرة المدى
            short_term = self.memory_system['short_term']
            # إزالة التفاعلات القديمة (أكثر من ساعة)
            self.memory_system['short_term'] = [
                interaction for interaction in short_term
                if (current_time - interaction['timestamp']).seconds < 3600
            ]

            # نقل التفاعلات المهمة إلى الذاكرة طويلة المدى
            for interaction in short_term:
                if interaction.get('confidence', 0) > 0.8:  # تفاعلات عالية الثقة
                    if len(self.memory_system['long_term']) < 10000:  # حد أقصى للذاكرة طويلة المدى
                        self.memory_system['long_term'].append(interaction)

            # إدارة الذاكرة الحلقية (Episodic Memory)
            if len(self.conversation_memory) > 100:  # الاحتفاظ بآخر 100 محادثة
                old_conversations = self.conversation_memory[:-100]
                # نقل المحادثات المهمة إلى الذاكرة الحلقية
                for conv in old_conversations:
                    if conv.get('confidence', 0) > 0.7:
                        self.memory_system['episodic'].append(conv)

                self.conversation_memory = self.conversation_memory[-100:]

            # تحديث الذاكرة الدلالية (Semantic Memory)
            self._update_semantic_memory()

        except Exception as e:
            logger.error(f"Error managing memory system: {str(e)}")

    def _update_semantic_memory(self):
        """تحديث الذاكرة الدلالية بالمفاهيم والعلاقات"""
        try:
            # استخراج المفاهيم الشائعة من التفاعلات
            all_keywords = []
            for interaction in self.conversation_memory[-50:]:  # آخر 50 تفاعل
                all_keywords.extend(interaction.get('keywords', []))

            # حساب تكرار الكلمات المفتاحية
            keyword_frequency = {}
            for keyword in all_keywords:
                keyword_frequency[keyword] = keyword_frequency.get(keyword, 0) + 1

            # تحديث الذاكرة الدلالية بالمفاهيم الأكثر استخداماً
            top_concepts = sorted(keyword_frequency.items(), key=lambda x: x[1], reverse=True)[:20]

            for concept, frequency in top_concepts:
                if concept not in self.memory_system['semantic']:
                    self.memory_system['semantic'][concept] = {
                        'frequency': frequency,
                        'related_concepts': [],
                        'user_associations': {}
                    }
                else:
                    self.memory_system['semantic'][concept]['frequency'] = frequency

        except Exception as e:
            logger.error(f"Error updating semantic memory: {str(e)}")

    # ===== طرق إضافية للتوصيات والتوقعات =====

    def _handle_recommendation_request(self, message: str, keywords=None, user_id=None) -> str:
        """التعامل مع طلبات التوصيات"""
        try:
            # تحليل نوع التوصية المطلوبة
            if 'تحسين' in message or 'تطوير' in message:
                return self._generate_improvement_recommendations(user_id)
            elif 'استثمار' in message or 'نمو' in message:
                return self._generate_growth_recommendations(user_id)
            elif 'توفير' in message or 'تكلفة' in message:
                return self._generate_cost_saving_recommendations(user_id)
            else:
                return self._generate_general_recommendations(user_id)

        except Exception as e:
            logger.error(f"Error handling recommendation request: {str(e)}")
            return "عذراً، لا يمكنني تقديم توصيات في الوقت الحالي. يرجى المحاولة لاحقاً."

    def _handle_prediction_request(self, message: str, keywords=None, user_id=None) -> str:
        """التعامل مع طلبات التوقعات"""
        try:
            if 'مبيعات' in message:
                return self._predict_sales_trend(user_id)
            elif 'ربح' in message or 'أرباح' in message:
                return self._predict_profit_trend(user_id)
            elif 'تدفق' in message or 'نقدي' in message:
                return self._predict_cash_flow(user_id)
            else:
                return self._generate_general_predictions(user_id)

        except Exception as e:
            logger.error(f"Error handling prediction request: {str(e)}")
            return "عذراً، لا يمكنني تقديم توقعات في الوقت الحالي. يرجى المحاولة لاحقاً."

    def _generate_improvement_recommendations(self, user_id=None) -> str:
        """توليد توصيات التحسين"""
        return """🚀 **توصيات التحسين:**

📊 **تحسين العمليات:**
• أتمتة العمليات المحاسبية المتكررة
• تحسين دورة الفواتير والتحصيل
• تطوير نظام إدارة المخزون

💰 **تحسين الربحية:**
• تحليل هوامش الربح لكل منتج/خدمة
• تحسين استراتيجيات التسعير
• تقليل التكاليف غير الضرورية

👥 **تحسين إدارة العملاء:**
• تطوير برامج ولاء العملاء
• تحسين خدمة العملاء
• تحليل سلوك العملاء لزيادة المبيعات"""

    def _generate_growth_recommendations(self, user_id=None) -> str:
        """توليد توصيات النمو"""
        return """📈 **توصيات النمو:**

🎯 **استراتيجيات التوسع:**
• دراسة أسواق جديدة
• تطوير منتجات/خدمات جديدة
• الاستثمار في التسويق الرقمي

💼 **الاستثمار الذكي:**
• تحسين رأس المال العامل
• الاستثمار في التكنولوجيا
• تطوير الموارد البشرية

🤝 **الشراكات الاستراتيجية:**
• البحث عن شركاء تجاريين
• تطوير قنوات توزيع جديدة
• الاستفادة من الشبكات المهنية"""

    def _generate_cost_saving_recommendations(self, user_id=None) -> str:
        """توليد توصيات توفير التكاليف"""
        return """💰 **توصيات توفير التكاليف:**

⚡ **تحسين الكفاءة:**
• مراجعة العمليات وإزالة الهدر
• أتمتة المهام اليدوية
• تحسين استخدام الموارد

📋 **إدارة المصروفات:**
• مراجعة الاشتراكات والخدمات
• التفاوض مع الموردين
• تحسين إدارة المخزون

🔧 **التحسين التقني:**
• استخدام الحلول السحابية
• تحسين استهلاك الطاقة
• الاستثمار في التقنيات الموفرة"""

    def _generate_general_recommendations(self, user_id=None) -> str:
        """توليد توصيات عامة"""
        return """💡 **توصيات عامة للأعمال:**

📊 **التحليل والمراقبة:**
• مراقبة المؤشرات المالية الرئيسية
• إعداد تقارير دورية
• تحليل الاتجاهات والأنماط

🎯 **التخطيط الاستراتيجي:**
• وضع أهداف واضحة وقابلة للقياس
• تطوير خطط عمل مفصلة
• مراجعة الاستراتيجية بانتظام

🛡️ **إدارة المخاطر:**
• تحديد المخاطر المحتملة
• وضع خطط للطوارئ
• تنويع مصادر الدخل"""

    def _predict_sales_trend(self, user_id=None) -> str:
        """توقع اتجاه المبيعات"""
        try:
            # تحليل بسيط للاتجاه
            if user_id:
                sales_result = self.analyze_sales_trend(user_id)
            else:
                sales_result = {'status': 'error'}
            if sales_result['status'] == 'success':
                trend = sales_result['trend']
                growth_rate = sales_result['growth_rate']

                if trend == 'upward':
                    prediction = f"📈 **توقعات إيجابية:** بناءً على الاتجاه الحالي ({growth_rate:+.1f}%), من المتوقع استمرار نمو المبيعات."
                elif trend == 'downward':
                    prediction = f"📉 **تحذير:** الاتجاه الحالي ({growth_rate:+.1f}%) يشير إلى انخفاض محتمل في المبيعات."
                else:
                    prediction = f"📊 **استقرار:** المبيعات مستقرة حالياً ({growth_rate:+.1f}%)."

                return f"""🔮 **توقعات المبيعات:**

{prediction}

💡 **توصيات:**
• راقب العوامل المؤثرة على المبيعات
• طور استراتيجيات تسويقية مناسبة
• حافظ على جودة المنتجات/الخدمات"""

            return "عذراً، لا توجد بيانات كافية لتوقع اتجاه المبيعات."

        except Exception as e:
            logger.error(f"Error predicting sales trend: {str(e)}")
            return "عذراً، حدث خطأ في توقع اتجاه المبيعات."

    def _predict_profit_trend(self, user_id=None) -> str:
        """توقع اتجاه الأرباح"""
        return """🔮 **توقعات الأرباح:**

📊 **التحليل المتوقع:**
• بناءً على الأداء الحالي، من المتوقع استقرار الأرباح
• هوامش الربح تحتاج إلى مراقبة مستمرة
• فرص تحسين الربحية متاحة

💡 **توصيات:**
• راقب التكاليف بعناية
• حسن استراتيجيات التسعير
• ركز على المنتجات/الخدمات الأكثر ربحية"""

    def _predict_cash_flow(self, user_id=None) -> str:
        """توقع التدفق النقدي"""
        return """🔮 **توقعات التدفق النقدي:**

💰 **التحليل المتوقع:**
• التدفق النقدي يحتاج إلى مراقبة دقيقة
• دورة التحصيل تؤثر على السيولة
• التخطيط المسبق ضروري

💡 **توصيات:**
• حسن دورة التحصيل
• خطط للمصروفات الكبيرة
• احتفظ بمخزون نقدي للطوارئ"""

    def _generate_general_predictions(self, user_id=None) -> str:
        """توليد توقعات عامة"""
        return """🔮 **توقعات عامة للأعمال:**

📊 **التوقعات المالية:**
• الأداء المالي يحتاج إلى مراقبة مستمرة
• الاتجاهات الحالية تشير إلى استقرار نسبي
• فرص التحسين متاحة في عدة مجالات

🎯 **التوصيات الاستراتيجية:**
• ركز على التحليل المستمر للبيانات
• طور خطط للسيناريوهات المختلفة
• استثمر في تحسين العمليات"""

    def get_business_insights(self, user_id: int) -> dict:
        """الحصول على رؤى الأعمال للمستخدم"""
        try:
            if not self.db:
                return {
                    'status': 'error',
                    'message': 'قاعدة البيانات غير متاحة'
                }

            # جمع البيانات الأساسية
            insights = {
                'total_revenue': 0,
                'total_clients': 0,
                'total_projects': 0,
                'pending_invoices': 0,
                'growth_rate': 0,
                'recommendations': []
            }

            # حساب الإيرادات الإجمالية
            if Invoice:
                total_revenue = self.db.session.query(func.sum(Invoice.total_amount)).filter_by(user_id=user_id).scalar()
                insights['total_revenue'] = total_revenue or 0

            # عدد العملاء
            if Client:
                total_clients = self.db.session.query(Client).filter_by(user_id=user_id).count()
                insights['total_clients'] = total_clients

            # عدد المشاريع
            if Project:
                total_projects = self.db.session.query(Project).filter_by(user_id=user_id).count()
                insights['total_projects'] = total_projects

            # الفواتير المعلقة
            if Invoice:
                pending_invoices = self.db.session.query(Invoice).filter_by(
                    user_id=user_id,
                    status='pending'
                ).count()
                insights['pending_invoices'] = pending_invoices

            # توليد التوصيات
            recommendations = []
            if insights['pending_invoices'] > 0:
                recommendations.append(f"لديك {insights['pending_invoices']} فاتورة معلقة تحتاج للمتابعة")

            if insights['total_clients'] < 5:
                recommendations.append("يمكنك زيادة قاعدة عملائك لتحسين الإيرادات")

            if insights['total_revenue'] == 0:
                recommendations.append("ابدأ بإنشاء فواتيرك الأولى لتتبع إيراداتك")

            insights['recommendations'] = recommendations

            return {
                'status': 'success',
                'insights': insights
            }

        except Exception as e:
            logger.error(f"Error getting business insights: {str(e)}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في جلب رؤى الأعمال: {str(e)}'
            }
