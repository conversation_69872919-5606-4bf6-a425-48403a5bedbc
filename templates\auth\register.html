{% extends "base.html" %}

{% block title %}إنشاء حساب جديد - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">إنشاء حساب جديد</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting" height="80">
                        <p class="text-muted mt-2">أنشئ حسابك للوصول إلى نظام المحاسبة الذكي</p>
                    </div>
                    
                    <form method="POST" action="{{ url_for('auth.register') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    {{ form.first_name(class="form-control", placeholder="الاسم الأول") }}
                                </div>
                                {% if form.first_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    {{ form.last_name(class="form-control", placeholder="الاسم الأخير") }}
                                </div>
                                {% if form.last_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-building"></i></span>
                                    {{ form.company_name(class="form-control", placeholder="اسم شركتك أو مؤسستك") }}
                                </div>
                                {% if form.company_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.company_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    {{ form.phone(class="form-control", placeholder="رقم هاتفك") }}
                                </div>
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    {{ form.confirm_password(class="form-control", placeholder="أعد إدخال كلمة المرور") }}
                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.confirm_password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.confirm_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.accept_terms(class="form-check-input") }}
                                <label class="form-check-label" for="accept_terms">
                                    أوافق على <a href="#" onclick="alert('شروط الاستخدام ستكون متاحة قريباً')">شروط الاستخدام</a> و <a href="#" onclick="alert('سياسة الخصوصية ستكون متاحة قريباً')">سياسة الخصوصية</a>
                                </label>
                            </div>
                            {% if form.accept_terms.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.accept_terms.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i> إنشاء الحساب
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">لديك حساب بالفعل؟ <a href="{{ url_for('auth.login') }}" class="text-primary">تسجيل الدخول</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const input = this.closest('.input-group').querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Password strength meter
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                
                if (password.length >= 8) strength += 1;
                if (password.match(/[a-z]+/)) strength += 1;
                if (password.match(/[A-Z]+/)) strength += 1;
                if (password.match(/[0-9]+/)) strength += 1;
                if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
                
                const strengthMeter = document.createElement('div');
                strengthMeter.className = 'progress mt-2';
                strengthMeter.style.height = '5px';
                
                let strengthClass = '';
                let strengthText = '';
                
                switch(strength) {
                    case 0:
                    case 1:
                        strengthClass = 'bg-danger';
                        strengthText = 'ضعيفة جداً';
                        break;
                    case 2:
                        strengthClass = 'bg-warning';
                        strengthText = 'ضعيفة';
                        break;
                    case 3:
                        strengthClass = 'bg-info';
                        strengthText = 'متوسطة';
                        break;
                    case 4:
                        strengthClass = 'bg-primary';
                        strengthText = 'قوية';
                        break;
                    case 5:
                        strengthClass = 'bg-success';
                        strengthText = 'قوية جداً';
                        break;
                }
                
                const progressWidth = (strength / 5) * 100;
                
                strengthMeter.innerHTML = `
                    <div class="progress-bar ${strengthClass}" role="progressbar" 
                         style="width: ${progressWidth}%;" 
                         aria-valuenow="${progressWidth}" aria-valuemin="0" aria-valuemax="100">
                    </div>
                `;
                
                const existingMeter = this.parentNode.parentNode.querySelector('.progress');
                const existingText = this.parentNode.parentNode.querySelector('.strength-text');
                
                if (existingMeter) {
                    existingMeter.remove();
                }
                
                if (existingText) {
                    existingText.remove();
                }
                
                if (password.length > 0) {
                    this.parentNode.parentNode.appendChild(strengthMeter);
                    
                    const strengthTextEl = document.createElement('small');
                    strengthTextEl.className = 'strength-text text-muted d-block mt-1';
                    strengthTextEl.textContent = `قوة كلمة المرور: ${strengthText}`;
                    this.parentNode.parentNode.appendChild(strengthTextEl);
                }
            });
        }
    });
</script>
{% endblock %}