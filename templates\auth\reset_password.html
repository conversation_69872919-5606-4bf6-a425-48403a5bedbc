{% extends "base.html" %}

{% block title %}إعادة تعيين كلمة المرور - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">إعادة تعيين كلمة المرور</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting" height="80">
                        <p class="text-muted mt-3">أدخل كلمة المرور الجديدة</p>
                    </div>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.password(class="form-control", placeholder="أدخل كلمة المرور الجديدة") }}
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <small class="strength-text text-muted d-block mt-1"></small>
                        </div>
                        
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.confirm_password(class="form-control", placeholder="أعد إدخال كلمة المرور الجديدة") }}
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> حفظ كلمة المرور الجديدة
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i> العودة إلى صفحة تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const input = this.closest('.input-group').querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Password strength meter
        const passwordInput = document.getElementById('password');
        const progressBar = document.querySelector('.progress-bar');
        const strengthText = document.querySelector('.strength-text');
        
        if (passwordInput && progressBar && strengthText) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                
                if (password.length >= 8) strength += 1;
                if (password.match(/[a-z]+/)) strength += 1;
                if (password.match(/[A-Z]+/)) strength += 1;
                if (password.match(/[0-9]+/)) strength += 1;
                if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
                
                let strengthClass = '';
                let strengthLabel = '';
                
                switch(strength) {
                    case 0:
                    case 1:
                        strengthClass = 'bg-danger';
                        strengthLabel = 'ضعيفة جداً';
                        break;
                    case 2:
                        strengthClass = 'bg-warning';
                        strengthLabel = 'ضعيفة';
                        break;
                    case 3:
                        strengthClass = 'bg-info';
                        strengthLabel = 'متوسطة';
                        break;
                    case 4:
                        strengthClass = 'bg-primary';
                        strengthLabel = 'قوية';
                        break;
                    case 5:
                        strengthClass = 'bg-success';
                        strengthLabel = 'قوية جداً';
                        break;
                }
                
                const progressWidth = (strength / 5) * 100;
                
                progressBar.className = `progress-bar ${strengthClass}`;
                progressBar.style.width = `${progressWidth}%`;
                progressBar.setAttribute('aria-valuenow', progressWidth);
                
                if (password.length > 0) {
                    strengthText.textContent = `قوة كلمة المرور: ${strengthLabel}`;
                } else {
                    strengthText.textContent = '';
                }
            });
        }
    });
</script>
{% endblock %}