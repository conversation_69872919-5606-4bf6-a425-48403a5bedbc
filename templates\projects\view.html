{% extends "base.html" %}

{% block title %}{{ project.name }} - تفاصيل المشروع{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ project.name }}</h1>
            <p class="text-muted">تفاصيل المشروع</p>
        </div>
        <div>
            <div class="btn-group">
                <a href="{{ url_for('projects.list_projects') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
                </a>
                <a href="{{ url_for('projects.edit_project', project_id=project.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{{ url_for('invoices.add_invoice') }}?project_id={{ project.id }}" class="btn btn-success">
                    <i class="fas fa-file-invoice me-2"></i>إضافة فاتورة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Project Information -->
        <div class="col-lg-8">
            <!-- Project Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>نظرة عامة
                    </h5>
                </div>
                <div class="card-body">
                    {% if project.description %}
                    <p class="mb-3">{{ project.description }}</p>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات المشروع</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>العميل:</strong></td>
                                    <td>
                                        {% if project.client_id %}
                                            {% set client = clients|selectattr('id', 'equalto', project.client_id)|first %}
                                            {% if client %}
                                            <a href="{{ url_for('clients.view_client', client_id=client.id) }}">{{ client.name }}</a>
                                            {% else %}
                                            <span class="text-muted">عميل محذوف</span>
                                            {% endif %}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>{{ project.category or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الأولوية:</strong></td>
                                    <td>
                                        {% if project.priority == 'urgent' %}
                                            <span class="badge bg-danger">عاجلة</span>
                                        {% elif project.priority == 'high' %}
                                            <span class="badge bg-warning">عالية</span>
                                        {% elif project.priority == 'medium' %}
                                            <span class="badge bg-info">متوسطة</span>
                                        {% elif project.priority == 'low' %}
                                            <span class="badge bg-secondary">منخفضة</span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>مدير المشروع:</strong></td>
                                    <td>{{ project.project_manager or 'غير محدد' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">الجدول الزمني والميزانية</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>تاريخ البداية:</strong></td>
                                    <td>{{ project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ النهاية:</strong></td>
                                    <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الميزانية:</strong></td>
                                    <td>{{ project.budget|format_currency if project.budget else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الساعات المقدرة:</strong></td>
                                    <td>{{ project.estimated_hours or 'غير محدد' }} ساعة</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Requirements -->
            {% if project.requirements %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>المتطلبات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ project.requirements }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Team Members -->
            {% if project.team_members %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>فريق العمل
                    </h5>
                </div>
                <div class="card-body">
                    {% set members = project.team_members.split('\n') %}
                    <div class="row">
                        {% for member in members %}
                        {% if member.strip() %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 32px; height: 32px;">
                                    <span class="text-white fw-bold">{{ member.strip()[0].upper() }}</span>
                                </div>
                                <span>{{ member.strip() }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Project Invoices -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>فواتير المشروع
                    </h5>
                    <a href="{{ url_for('invoices.add_invoice') }}?project_id={{ project.id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة فاتورة
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="text-decoration-none">
                                            <strong>#{{ invoice.invoice_number }}</strong>
                                        </a>
                                    </td>
                                    <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ invoice.total_amount|format_currency }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% elif invoice.status == 'unpaid' %}
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        {% elif invoice.status == 'partially_paid' %}
                                            <span class="badge bg-warning">مدفوعة جزئياً</span>
                                        {% elif invoice.status == 'overdue' %}
                                            <span class="badge bg-dark">متأخرة</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" 
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}" 
                                               class="btn btn-outline-secondary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد فواتير</h6>
                        <p class="text-muted">لم يتم إنشاء أي فواتير لهذا المشروع بعد.</p>
                        <a href="{{ url_for('invoices.add_invoice') }}?project_id={{ project.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة فاتورة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Notes -->
            {% if project.notes %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>الملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ project.notes }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status and Progress -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>الحالة والتقدم
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        {% if project.status == 'active' %}
                            <span class="badge bg-success fs-6">نشط</span>
                        {% elif project.status == 'completed' %}
                            <span class="badge bg-primary fs-6">مكتمل</span>
                        {% elif project.status == 'on_hold' %}
                            <span class="badge bg-warning fs-6">معلق</span>
                        {% elif project.status == 'cancelled' %}
                            <span class="badge bg-danger fs-6">ملغي</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">{{ project.status }}</span>
                        {% endif %}
                    </div>
                    
                    {% set progress = project.progress or 0 %}
                    <div class="mb-3">
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar 
                                {% if progress < 25 %}bg-danger
                                {% elif progress < 50 %}bg-warning
                                {% elif progress < 75 %}bg-info
                                {% else %}bg-success{% endif %}" 
                                role="progressbar" style="width: {{ progress }}%">
                                {{ progress }}%
                            </div>
                        </div>
                        <small class="text-muted">نسبة الإنجاز</small>
                    </div>
                </div>
            </div>

            <!-- Project Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات المشروع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ project_stats.total_invoices or 0 }}</h4>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ project_stats.total_amount|format_currency }}</h4>
                            <small class="text-muted">إجمالي المبلغ</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-info">{{ project_stats.paid_amount|format_currency }}</h5>
                            <small class="text-muted">المبلغ المدفوع</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ project_stats.unpaid_amount|format_currency }}</h5>
                            <small class="text-muted">المبلغ المستحق</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>الجدول الزمني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء المشروع</h6>
                                <p class="text-muted mb-0">{{ project.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% if project.start_date %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">بداية المشروع</h6>
                                <p class="text-muted mb-0">{{ project.start_date.strftime('%Y-%m-%d') }}</p>
                            </div>
                        </div>
                        {% endif %}
                        {% if project.end_date %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">النهاية المتوقعة</h6>
                                <p class="text-muted mb-0">{{ project.end_date.strftime('%Y-%m-%d') }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
{% endblock %}
