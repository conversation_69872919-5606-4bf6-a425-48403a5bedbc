#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة المصادقة وإدارة المستخدمين
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime
import logging
import uuid

# استيراد النماذج وقاعدة البيانات
from models.user import User
from extensions import db, login_manager
from forms import LoginForm, RegisterForm, ResetPasswordRequestForm, ResetPasswordForm

# إنشاء مخطط Blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# إعداد السجل
logger = logging.getLogger(__name__)

@login_manager.user_loader
def load_user(user_id):
    """
تحميل المستخدم من معرف المستخدم
    """
    return User.query.get(int(user_id))

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """
صفحة تسجيل الدخول
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    form = LoginForm()

    if form.validate_on_submit():
        try:
            user = User.query.filter_by(email=form.email.data).first()

            if not user or not user.check_password(form.password.data):
                flash('البريد الإلكتروني أو كلمة المرور غير صحيحة.', 'danger')
                return render_template('auth/login.html', form=form)

            # تحديث آخر تسجيل دخول
            user.update_last_login()
            db.session.commit()

            # تسجيل الدخول
            login_user(user, remember=form.remember_me.data)

            # توجيه المستخدم إلى الصفحة التي كان يحاول الوصول إليها
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)

            return redirect(url_for('main.dashboard'))

        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            flash('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'danger')

    return render_template('auth/login.html', form=form)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """
صفحة التسجيل
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    form = RegisterForm()

    if form.validate_on_submit():
        try:
            # التحقق من وجود المستخدم
            existing_user = User.query.filter_by(email=form.email.data).first()
            if existing_user:
                flash('البريد الإلكتروني مسجل بالفعل.', 'danger')
                return render_template('auth/register.html', form=form)

            # إنشاء اسم مستخدم من البريد الإلكتروني
            email_data = form.email.data or ""
            username = email_data.split('@')[0] if email_data else "user"

            # التأكد من أن اسم المستخدم فريد
            counter = 1
            original_username = username
            while User.query.filter_by(username=username).first():
                username = f"{original_username}{counter}"
                counter += 1

            # إنشاء مستخدم جديد
            new_user = User()
            new_user.username = username
            new_user.first_name = form.first_name.data
            new_user.last_name = form.last_name.data
            new_user.email = form.email.data
            new_user.company_name = form.company_name.data
            new_user.phone = form.phone.data
            new_user.is_active = True
            new_user.is_admin = False
            new_user.api_key = str(uuid.uuid4())
            new_user.created_at = datetime.now()
            new_user.updated_at = datetime.now()
            new_user.last_login = datetime.now()

            # تعيين كلمة المرور باستخدام الـ property setter
            new_user.password = form.password.data

            db.session.add(new_user)
            db.session.commit()

            # تسجيل الدخول تلقائيًا بعد التسجيل
            login_user(new_user)

            flash('تم إنشاء حسابك بنجاح! مرحباً بك في نظام المحاسبة الذكي.', 'success')
            return redirect(url_for('main.dashboard'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"Registration error: {str(e)}")
            flash('حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.', 'danger')
    else:
        # إذا كان النموذج غير صالح، اعرض الأخطاء
        if form.errors:
            for _, errors in form.errors.items():
                for error in errors:
                    flash(f'{error}', 'danger')

    return render_template('auth/register.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    """
تسجيل الخروج
    """
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """
صفحة الملف الشخصي
    """
    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        company_name = request.form.get('company_name')
        phone = request.form.get('phone')
        address = request.form.get('address')
        
        try:
            # التحقق من البريد الإلكتروني
            if email != current_user.email:
                existing_user = User.query.filter_by(email=email).first()
                if existing_user:
                    flash('البريد الإلكتروني مستخدم بالفعل.', 'danger')
                    return render_template('auth/profile.html')
            
            # تحديث بيانات المستخدم
            current_user.first_name = first_name
            current_user.last_name = last_name
            current_user.email = email
            current_user.company_name = company_name
            current_user.phone = phone
            current_user.address = address
            current_user.updated_at = datetime.now()
            
            db.session.commit()
            
            flash('تم تحديث الملف الشخصي بنجاح.', 'success')
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Profile update error: {str(e)}")
            flash('حدث خطأ أثناء تحديث الملف الشخصي. يرجى المحاولة مرة أخرى.', 'danger')
    
    return render_template('auth/profile.html')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """
تغيير كلمة المرور
    """
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not current_password or not new_password or not confirm_password:
            flash('يرجى ملء جميع الحقول.', 'danger')
            return render_template('auth/change_password.html')
        
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة.', 'danger')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('كلمات المرور الجديدة غير متطابقة.', 'danger')
            return render_template('auth/change_password.html')
        
        if len(new_password) < 8:
            flash('يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل.', 'danger')
            return render_template('auth/change_password.html')
        
        try:
            # تحديث كلمة المرور
            current_user.password = new_password  # سيتم تشفير كلمة المرور في نموذج المستخدم
            current_user.updated_at = datetime.now()
            
            db.session.commit()
            
            flash('تم تغيير كلمة المرور بنجاح.', 'success')
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Password change error: {str(e)}")
            flash('حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.', 'danger')
    
    return render_template('auth/change_password.html')

@auth_bp.route('/reset-password', methods=['GET', 'POST'])
def reset_password_request():
    """
طلب إعادة تعيين كلمة المرور
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        
        if not email:
            flash('يرجى إدخال البريد الإلكتروني.', 'danger')
            return render_template('auth/reset_password_request.html')
        
        try:
            user = User.query.filter_by(email=email).first()
            
            if user:
                # إنشاء رمز إعادة تعيين كلمة المرور
                reset_token = str(uuid.uuid4())
                user.reset_token = reset_token
                user.reset_token_expiry = datetime.now() + current_app.config['RESET_TOKEN_EXPIRY']
                
                db.session.commit()
                
                # إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
                # TODO: تنفيذ إرسال البريد الإلكتروني
                
                flash('تم إرسال تعليمات إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.', 'success')
            else:
                # لا نكشف عن وجود المستخدم لأسباب أمنية
                flash('تم إرسال تعليمات إعادة تعيين كلمة المرور إلى بريدك الإلكتروني إذا كان الحساب موجودًا.', 'success')
            
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Password reset request error: {str(e)}")
            flash('حدث خطأ أثناء طلب إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.', 'danger')
    
    form = ResetPasswordRequestForm()
    return render_template('auth/reset_password_request.html', form=form)

@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """
إعادة تعيين كلمة المرور باستخدام الرمز
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    # التحقق من صحة الرمز
    user = User.query.filter_by(reset_token=token).first()
    
    if not user or user.reset_token_expiry < datetime.now():
        flash('رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية.', 'danger')
        return redirect(url_for('auth.reset_password_request'))
    
    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        if not password or not confirm_password:
            flash('يرجى ملء جميع الحقول.', 'danger')
            return render_template('auth/reset_password.html', token=token)
        
        if password != confirm_password:
            flash('كلمات المرور غير متطابقة.', 'danger')
            return render_template('auth/reset_password.html', token=token)
        
        if len(password) < 8:
            flash('يجب أن تكون كلمة المرور 8 أحرف على الأقل.', 'danger')
            return render_template('auth/reset_password.html', token=token)
        
        try:
            # تحديث كلمة المرور
            user.password = password  # سيتم تشفير كلمة المرور في نموذج المستخدم
            user.reset_token = None
            user.reset_token_expiry = None
            user.updated_at = datetime.now()
            
            db.session.commit()
            
            flash('تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Password reset error: {str(e)}")
            flash('حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.', 'danger')
    
    form = ResetPasswordForm()
    return render_template('auth/reset_password.html', form=form, token=token)

@auth_bp.route('/api/regenerate-api-key', methods=['POST'])
@login_required
def regenerate_api_key():
    """
إعادة إنشاء مفتاح API
    """
    try:
        # إنشاء مفتاح API جديد
        current_user.api_key = str(uuid.uuid4())
        current_user.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'تم إعادة إنشاء مفتاح API بنجاح.',
            'api_key': current_user.api_key
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API key regeneration error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إعادة إنشاء مفتاح API.'
        }), 500

@auth_bp.route('/api/check-auth')
def api_check_auth():
    """
التحقق من حالة المصادقة
    """
    if current_user.is_authenticated:
        return jsonify({
            'status': 'success',
            'authenticated': True,
            'user': {
                'id': current_user.id,
                'name': current_user.get_full_name(),
                'email': current_user.email,
                'is_admin': current_user.is_admin
            }
        })
    else:
        return jsonify({
            'status': 'success',
            'authenticated': False
        })

@auth_bp.errorhandler(401)
def unauthorized(e):
    """خطأ غير مصرح به"""
    _ = e  # متغير غير مستخدم
    flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة.', 'danger')
    return redirect(url_for('auth.login'))

@auth_bp.errorhandler(403)
def forbidden(e):
    """خطأ محظور"""
    _ = e  # متغير غير مستخدم
    flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
    return redirect(url_for('main.dashboard'))