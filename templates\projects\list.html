{% extends "base.html" %}

{% block title %}قائمة المشاريع{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">قائمة المشاريع</h1>
            <p class="text-muted">إدارة جميع المشاريع</p>
        </div>
        <div>
            <a href="{{ url_for('projects.add_project') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مشروع جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المشاريع</h6>
                            <h4 class="mb-0">{{ projects|length }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-project-diagram fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مشاريع نشطة</h6>
                            <h4 class="mb-0">{{ active_projects_count or 0 }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مشاريع مكتملة</h6>
                            <h4 class="mb-0">{{ completed_projects_count or 0 }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي القيمة</h6>
                            <h4 class="mb-0">{{ total_value|format_currency }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>البحث والفلترة
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query or '' }}" placeholder="البحث بالاسم أو الوصف">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if not status_filter or status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                        <option value="on_hold" {% if status_filter == 'on_hold' %}selected{% endif %}>معلق</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="client_id" class="form-label">العميل</label>
                    <select class="form-select" id="client_id" name="client_id">
                        <option value="">جميع العملاء</option>
                        {% for client in clients %}
                        <option value="{{ client.id }}" {% if client_filter == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم</option>
                        <option value="start_date" {% if sort_by == 'start_date' %}selected{% endif %}>تاريخ البداية</option>
                        <option value="end_date" {% if sort_by == 'end_date' %}selected{% endif %}>تاريخ النهاية</option>
                        <option value="budget" {% if sort_by == 'budget' %}selected{% endif %}>الميزانية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Projects Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>المشاريع ({{ projects|length }})
            </h5>
        </div>
        <div class="card-body p-0">
            {% if projects %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>اسم المشروع</th>
                            <th>العميل</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>الميزانية</th>
                            <th>التقدم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in projects %}
                        <tr>
                            <td>
                                <div>
                                    <a href="{{ url_for('projects.view_project', project_id=project.id) }}" class="text-decoration-none">
                                        <strong>{{ project.name }}</strong>
                                    </a>
                                    {% if project.description %}
                                    <br><small class="text-muted">{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if project.client_id %}
                                    {% set client = clients|selectattr('id', 'equalto', project.client_id)|first %}
                                    {% if client %}
                                    <a href="{{ url_for('clients.view_client', client_id=client.id) }}" class="text-decoration-none">
                                        {{ client.name }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">عميل محذوف</span>
                                    {% endif %}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if project.start_date %}
                                {{ project.start_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if project.end_date %}
                                {{ project.end_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if project.budget %}
                                {{ project.budget|format_currency }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set progress = project.progress or 0 %}
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar 
                                        {% if progress < 25 %}bg-danger
                                        {% elif progress < 50 %}bg-warning
                                        {% elif progress < 75 %}bg-info
                                        {% else %}bg-success{% endif %}" 
                                        role="progressbar" style="width: {{ progress }}%">
                                        {{ progress }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if project.status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                {% elif project.status == 'completed' %}
                                    <span class="badge bg-primary">مكتمل</span>
                                {% elif project.status == 'on_hold' %}
                                    <span class="badge bg-warning">معلق</span>
                                {% elif project.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ project.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('projects.view_project', project_id=project.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('projects.edit_project', project_id=project.id) }}" 
                                       class="btn btn-outline-secondary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('invoices.add_invoice') }}?project_id={{ project.id }}" 
                                       class="btn btn-outline-success" title="إضافة فاتورة">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete({{ project.id }}, '{{ project.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مشاريع</h5>
                <p class="text-muted">لم يتم العثور على أي مشاريع تطابق معايير البحث.</p>
                <a href="{{ url_for('projects.add_project') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مشروع جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف المشروع <strong id="projectName"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: سيتم حذف جميع الفواتير والمهام المرتبطة بهذا المشروع.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(projectId, projectName) {
    document.getElementById('projectName').textContent = projectName;
    document.getElementById('deleteForm').action = `/projects/${projectId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status, #client_id, #sort_by');
    filterSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
