#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام SmartBiz - نسخة مضمونة التشغيل
"""

print("🚀 بدء تحميل نظام SmartBiz...")

try:
    from flask import Flask, request, redirect, session
    print("✅ تم تحميل Flask")
except ImportError:
    print("❌ Flask غير مثبت")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask'])
    from flask import Flask, request, redirect, session

try:
    from flask_sqlalchemy import SQLAlchemy
    print("✅ تم تحميل SQLAlchemy")
except ImportError:
    print("❌ Flask-SQLAlchemy غير مثبت")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask-sqlalchemy'])
    from flask_sqlalchemy import SQLAlchemy

from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'smartbiz-2024-secret'

# إعداد قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smartbiz.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

print("✅ تم إعداد التطبيق وقاعدة البيانات")

# نماذج البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(50), nullable=False)

class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))

print("✅ تم تعريف النماذج")

# الصفحات
@app.route('/')
def home():
    if 'user' in session:
        return f'''
        <html dir="rtl">
        <head><title>SmartBiz</title></head>
        <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 800px; margin: 0 auto;">
                <h1 style="color: #007bff;">🏢 مرحباً بك في نظام SmartBiz</h1>
                <p>مرحباً <strong>{session['user']}</strong></p>
                
                <div style="margin: 20px 0;">
                    <h3>الإحصائيات:</h3>
                    <p>📊 عدد العملاء: {Client.query.count()}</p>
                </div>
                
                <div style="margin: 20px 0;">
                    <a href="/clients" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إدارة العملاء</a>
                    <a href="/add_client" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إضافة عميل</a>
                    <a href="/logout" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">خروج</a>
                </div>
            </div>
        </body>
        </html>
        '''
    else:
        return '''
        <html dir="rtl">
        <head><title>تسجيل الدخول - SmartBiz</title></head>
        <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 50px auto;">
                <h1 style="color: #007bff; text-align: center;">🏢 SmartBiz</h1>
                <h2>تسجيل الدخول</h2>
                <form method="post" action="/login">
                    <p><label>اسم المستخدم:</label><br>
                    <input type="text" name="username" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></p>
                    
                    <p><label>كلمة المرور:</label><br>
                    <input type="password" name="password" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></p>
                    
                    <p><input type="submit" value="دخول" style="background: #007bff; color: white; padding: 12px 20px; border: none; border-radius: 5px; width: 100%; cursor: pointer;"></p>
                </form>
                
                <div style="text-align: center; margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 5px;">
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    اسم المستخدم: <code>admin</code><br>
                    كلمة المرور: <code>admin123</code>
                </div>
            </div>
        </body>
        </html>
        '''

@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    password = request.form['password']
    
    user = User.query.filter_by(username=username, password=password).first()
    if user:
        session['user'] = username
        return redirect('/')
    else:
        return '''
        <html dir="rtl">
        <body style="font-family: Arial; padding: 20px;">
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                ❌ اسم المستخدم أو كلمة المرور غير صحيحة
            </div>
            <a href="/" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة</a>
        </body>
        </html>
        '''

@app.route('/clients')
def clients():
    if 'user' not in session:
        return redirect('/')
    
    clients = Client.query.all()
    clients_html = ''
    for client in clients:
        clients_html += f'''
        <div style="background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-right: 4px solid #007bff;">
            <strong>{client.name}</strong><br>
            📧 {client.email or 'لا يوجد'}<br>
            📞 {client.phone or 'لا يوجد'}
        </div>
        '''
    
    return f'''
    <html dir="rtl">
    <head><title>العملاء - SmartBiz</title></head>
    <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
        <div style="background: white; padding: 30px; border-radius: 10px; max-width: 800px; margin: 0 auto;">
            <h1 style="color: #007bff;">👥 إدارة العملاء</h1>
            
            <div style="margin: 20px 0;">
                <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">العودة للرئيسية</a>
                <a href="/add_client" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إضافة عميل جديد</a>
            </div>
            
            <h3>قائمة العملاء ({len(clients)}):</h3>
            {clients_html if clients_html else '<p style="color: #6c757d;">لا توجد عملاء مسجلين بعد</p>'}
        </div>
    </body>
    </html>
    '''

@app.route('/add_client', methods=['GET', 'POST'])
def add_client():
    if 'user' not in session:
        return redirect('/')
    
    if request.method == 'POST':
        name = request.form['name']
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        
        client = Client(name=name, email=email, phone=phone)
        db.session.add(client)
        db.session.commit()
        
        return '''
        <html dir="rtl">
        <body style="font-family: Arial; padding: 20px;">
            <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                ✅ تم إضافة العميل بنجاح!
            </div>
            <a href="/clients" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض العملاء</a>
        </body>
        </html>
        '''
    
    return '''
    <html dir="rtl">
    <head><title>إضافة عميل - SmartBiz</title></head>
    <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
        <div style="background: white; padding: 30px; border-radius: 10px; max-width: 500px; margin: 0 auto;">
            <h1 style="color: #007bff;">➕ إضافة عميل جديد</h1>
            
            <form method="post">
                <p><label><strong>اسم العميل:</strong> <span style="color: red;">*</span></label><br>
                <input type="text" name="name" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></p>
                
                <p><label><strong>البريد الإلكتروني:</strong></label><br>
                <input type="email" name="email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></p>
                
                <p><label><strong>رقم الهاتف:</strong></label><br>
                <input type="text" name="phone" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></p>
                
                <p>
                    <input type="submit" value="إضافة العميل" style="background: #28a745; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                    <a href="/clients" style="background: #6c757d; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px;">إلغاء</a>
                </p>
            </form>
        </div>
    </body>
    </html>
    '''

@app.route('/logout')
def logout():
    session.pop('user', None)
    return redirect('/')

# تهيئة قاعدة البيانات
def init_database():
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin', password='admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي")

if __name__ == '__main__':
    print("🔧 تهيئة قاعدة البيانات...")
    init_database()
    
    print("\n" + "=" * 50)
    print("🎉 نظام SmartBiz جاهز للعمل!")
    print("🌐 الرابط: http://127.0.0.1:5000")
    print("🔑 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
