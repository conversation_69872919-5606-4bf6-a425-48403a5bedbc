"""Update clients table with company_name and last_contact fields

Revision ID: cc902db49035
Revises: f224b0718445
Create Date: 2025-06-04 09:19:43.129800

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cc902db49035'
down_revision = 'f224b0718445'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.add_column(sa.Column('company_name', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('last_contact', sa.DateTime(), nullable=True))
        batch_op.alter_column('payment_method',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=50),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.alter_column('payment_method',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
        batch_op.drop_column('last_contact')
        batch_op.drop_column('company_name')

    # ### end Alembic commands ###
