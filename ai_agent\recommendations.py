#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة توصيات الذكاء الاصطناعي
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import func
import logging
from openai import OpenAI
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# تكوين OpenAI API
client = OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))

# إعداد السجل
logger = logging.getLogger(__name__)

class SmartBizRecommendations:
    """
فئة لتوليد توصيات ذكية لتحسين الأعمال
    """
    def __init__(self, db):
        self.db = db
    
    def get_cash_flow_recommendations(self, user_id):
        """
توليد توصيات لتحسين التدفق النقدي
        """
        from models.invoice import Invoice
        from models.transaction import Transaction
        
        # الحصول على الفواتير غير المدفوعة
        unpaid_invoices = Invoice.query.filter(
            Invoice.user_id == user_id,
            Invoice.status.in_(['unpaid', 'partially_paid'])
        ).all()
        
        total_unpaid = sum(invoice.amount_due for invoice in unpaid_invoices)
        
        # الحصول على إجمالي المصروفات الشهرية
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)
        
        monthly_expenses = self.db.session.query(
            func.sum(Transaction.amount)
        ).filter(
            Transaction.user_id == user_id,
            Transaction.transaction_type == 'expense',
            Transaction.date >= start_date,
            Transaction.date <= end_date
        ).scalar() or 0
        
        # توليد توصيات بناءً على البيانات
        recommendations = []
        
        # توصية 1: تحسين تحصيل المدفوعات
        if unpaid_invoices:
            overdue_invoices = [inv for inv in unpaid_invoices if inv.due_date < datetime.utcnow().date()]
            if overdue_invoices:
                recommendations.append({
                    'type': 'cash_flow',
                    'priority': 'high' if len(overdue_invoices) > 5 or total_unpaid > monthly_expenses * 2 else 'medium',
                    'title': 'تحسين تحصيل المدفوعات المتأخرة',
                    'description': f'لديك {len(overdue_invoices)} فواتير متأخرة بقيمة إجمالية {total_unpaid}. ننصح بمتابعة العملاء وتحسين عملية التحصيل.',
                    'actions': [
                        'إرسال تذكيرات للعملاء بالفواتير المستحقة',
                        'تقديم خيارات دفع مرنة للعملاء',
                        'تطبيق خصومات للدفع المبكر',
                        'تحديث سياسة الائتمان الخاصة بك'
                    ],
                    'data': {
                        'overdue_count': len(overdue_invoices),
                        'total_unpaid': total_unpaid,
                        'oldest_invoice_days': max([inv.due_date for inv in overdue_invoices], key=lambda x: datetime.utcnow().date() - x).days if overdue_invoices else 0
                    }
                })
        
        # توصية 2: إدارة المصروفات
        high_expense_categories = self.db.session.query(
            Transaction.category,
            func.sum(Transaction.amount).label('total_amount')
        ).filter(
            Transaction.user_id == user_id,
            Transaction.transaction_type == 'expense',
            Transaction.date >= start_date,
            Transaction.date <= end_date
        ).group_by(Transaction.category).order_by(func.sum(Transaction.amount).desc()).limit(3).all()
        
        if high_expense_categories:
            recommendations.append({
                'type': 'expense_management',
                'priority': 'medium',
                'title': 'تحسين إدارة المصروفات',
                'description': 'تشكل الفئات التالية أعلى نسبة من مصروفاتك الشهرية. ننصح بمراجعة هذه المصروفات وتحديد فرص التوفير.',
                'actions': [
                    'مراجعة عقود الموردين وإعادة التفاوض عليها',
                    'البحث عن بدائل أقل تكلفة',
                    'تحديد المصروفات غير الضرورية وتقليلها',
                    'وضع ميزانية شهرية لكل فئة من المصروفات'
                ],
                'data': {
                    'categories': [{
                        'name': cat.category or 'أخرى',
                        'amount': cat.total_amount
                    } for cat in high_expense_categories]
                }
            })
        
        return recommendations
    
    def get_sales_recommendations(self, user_id):
        """
توليد توصيات لتحسين المبيعات
        """
        from models.invoice import Invoice
        from models.client import Client
        
        # تحليل اتجاه المبيعات
        end_date = datetime.utcnow().date()
        start_date_6m = end_date - timedelta(days=180)  # 6 أشهر
        start_date_3m = end_date - timedelta(days=90)   # 3 أشهر
        
        # المبيعات في آخر 3 أشهر
        sales_3m = self.db.session.query(
            func.sum(Invoice.total_amount)
        ).filter(
            Invoice.user_id == user_id,
            Invoice.issue_date >= start_date_3m,
            Invoice.issue_date <= end_date
        ).scalar() or 0
        
        # المبيعات في الـ 3 أشهر السابقة
        sales_prev_3m = self.db.session.query(
            func.sum(Invoice.total_amount)
        ).filter(
            Invoice.user_id == user_id,
            Invoice.issue_date >= start_date_6m,
            Invoice.issue_date < start_date_3m
        ).scalar() or 0
        
        # حساب معدل النمو
        growth_rate = ((sales_3m / sales_prev_3m) - 1) * 100 if sales_prev_3m > 0 else 0
        
        # الحصول على العملاء غير النشطين
        inactive_threshold = end_date - timedelta(days=90)  # 3 أشهر
        
        inactive_clients = self.db.session.query(Client).filter(
            Client.user_id == user_id,
            Client.id.in_(
                self.db.session.query(Invoice.client_id).filter(
                    Invoice.user_id == user_id
                ).group_by(Invoice.client_id).having(
                    func.max(Invoice.issue_date) < inactive_threshold
                )
            )
        ).all()
        
        # توليد توصيات بناءً على البيانات
        recommendations = []
        
        # توصية 1: تحسين المبيعات
        if growth_rate < 0:
            recommendations.append({
                'type': 'sales_improvement',
                'priority': 'high',
                'title': 'تحسين أداء المبيعات',
                'description': f'انخفضت مبيعاتك بنسبة {abs(growth_rate):.1f}% في الأشهر الثلاثة الأخيرة مقارنة بالفترة السابقة. ننصح باتخاذ إجراءات لتحسين المبيعات.',
                'actions': [
                    'تطوير استراتيجية تسويق جديدة',
                    'تقديم عروض ترويجية للعملاء',
                    'استهداف أسواق أو قطاعات جديدة',
                    'تحسين عملية المبيعات وتدريب فريق المبيعات'
                ],
                'data': {
                    'growth_rate': growth_rate,
                    'current_period_sales': sales_3m,
                    'previous_period_sales': sales_prev_3m
                }
            })
        
        # توصية 2: إعادة تنشيط العملاء غير النشطين
        if inactive_clients:
            recommendations.append({
                'type': 'client_reactivation',
                'priority': 'medium',
                'title': 'إعادة تنشيط العملاء غير النشطين',
                'description': f'لديك {len(inactive_clients)} عملاء لم يقوموا بأي معاملات في الأشهر الثلاثة الأخيرة. ننصح بالتواصل معهم وتقديم عروض خاصة.',
                'actions': [
                    'إرسال رسائل تذكير ودية',
                    'تقديم عروض خاصة للعملاء غير النشطين',
                    'طلب ملاحظات حول سبب عدم استمرارهم في التعامل معك',
                    'تطوير برنامج ولاء للعملاء'
                ],
                'data': {
                    'inactive_count': len(inactive_clients),
                    'inactive_clients': [{
                        'id': client.id,
                        'name': client.name,
                        'last_invoice_date': self.db.session.query(func.max(Invoice.issue_date)).filter(Invoice.client_id == client.id).scalar().isoformat() if self.db.session.query(func.max(Invoice.issue_date)).filter(Invoice.client_id == client.id).scalar() else None
                    } for client in inactive_clients[:5]]  # إرسال أول 5 عملاء فقط
                }
            })
        
        return recommendations
    
    def get_profitability_recommendations(self, user_id):
        """
توليد توصيات لتحسين الربحية
        """
        from models.project import Project
        
        # الحصول على المشاريع النشطة
        active_projects = Project.query.filter(
            Project.user_id == user_id,
            Project.status == 'active'
        ).all()
        
        # تحليل ربحية المشاريع
        low_margin_projects = []
        for project in active_projects:
            profit_margin = project.get_profit_margin()
            if profit_margin < 20:  # هامش ربح أقل من 20%
                low_margin_projects.append({
                    'id': project.id,
                    'name': project.name,
                    'profit_margin': profit_margin,
                    'income': project.get_total_income(),
                    'expenses': project.get_total_expenses()
                })
        
        # توليد توصيات بناءً على البيانات
        recommendations = []
        
        # توصية 1: تحسين هوامش الربح
        if low_margin_projects:
            recommendations.append({
                'type': 'profit_margin_improvement',
                'priority': 'high',
                'title': 'تحسين هوامش الربح',
                'description': f'لديك {len(low_margin_projects)} مشاريع نشطة بهامش ربح منخفض (أقل من 20%). ننصح بمراجعة هيكل التكاليف وأسعار هذه المشاريع.',
                'actions': [
                    'مراجعة تسعير المشاريع وزيادة الأسعار إذا أمكن',
                    'تحليل التكاليف وتحديد مجالات خفض التكاليف',
                    'التفاوض مع الموردين للحصول على أسعار أفضل',
                    'تحسين كفاءة العمليات لتقليل ساعات العمل'
                ],
                'data': {
                    'projects': low_margin_projects
                }
            })
        
        # توصية 2: تنويع مصادر الدخل
        project_types = set(project.project_type for project in active_projects)
        if len(project_types) < 3 and len(active_projects) > 0:
            recommendations.append({
                'type': 'income_diversification',
                'priority': 'medium',
                'title': 'تنويع مصادر الدخل',
                'description': 'تعتمد أعمالك على عدد محدود من أنواع المشاريع. ننصح بتنويع مصادر الدخل لتقليل المخاطر وزيادة فرص النمو.',
                'actions': [
                    'استكشاف أنواع جديدة من المشاريع أو الخدمات',
                    'استهداف قطاعات أو أسواق جديدة',
                    'تطوير منتجات أو خدمات تكميلية',
                    'البحث عن فرص للإيرادات المتكررة'
                ],
                'data': {
                    'current_project_types': list(project_types)
                }
            })
        
        return recommendations
    
    def get_ai_recommendations(self, user_id):
        """
توليد توصيات ذكية باستخدام OpenAI
        """
        try:
            # جمع البيانات الأساسية
            from models.user import User
            from models.project import Project
            from models.client import Client
            from models.invoice import Invoice
            from models.transaction import Transaction
            
            user = User.query.get(user_id)
            
            # إحصائيات المشاريع
            projects = Project.query.filter_by(user_id=user_id).all()
            active_projects = [p for p in projects if p.status == 'active']
            total_income = sum(p.get_total_income() for p in projects)
            total_expenses = sum(p.get_total_expenses() for p in projects)
            total_profit = total_income - total_expenses
            avg_profit_margin = sum(p.get_profit_margin() for p in projects) / len(projects) if projects else 0
            
            # إحصائيات العملاء
            clients = Client.query.filter_by(user_id=user_id).all()
            total_unpaid = sum(c.get_total_unpaid() for c in clients)
            
            # إحصائيات المبيعات
            end_date = datetime.utcnow().date()
            start_date_3m = end_date - timedelta(days=90)
            start_date_6m = end_date - timedelta(days=180)
            
            sales_3m = self.db.session.query(
                func.sum(Invoice.total_amount)
            ).filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= start_date_3m,
                Invoice.issue_date <= end_date
            ).scalar() or 0
            
            sales_prev_3m = self.db.session.query(
                func.sum(Invoice.total_amount)
            ).filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= start_date_6m,
                Invoice.issue_date < start_date_3m
            ).scalar() or 0
            
            growth_rate = ((sales_3m / sales_prev_3m) - 1) * 100 if sales_prev_3m > 0 else 0
            
            # إعداد السياق للاستعلام
            context = f"""بناءً على البيانات التالية لشركة {user.full_name}:
            - إجمالي الدخل: {total_income}
            - إجمالي المصروفات: {total_expenses}
            - إجمالي الربح: {total_profit}
            - متوسط هامش الربح: {avg_profit_margin:.1f}%
            - إجمالي المبالغ غير المدفوعة: {total_unpaid}
            - عدد المشاريع النشطة: {len(active_projects)}
            - عدد العملاء: {len(clients)}
            - معدل نمو المبيعات في آخر 3 أشهر: {growth_rate:.1f}%
            
            قدم 3-5 توصيات عملية وذكية لتحسين أداء الشركة وزيادة الربحية. لكل توصية، قدم:
            1. عنوان التوصية
            2. وصف مفصل للتوصية
            3. 3-4 إجراءات محددة يمكن اتخاذها لتنفيذ التوصية
            
            قدم التوصيات بتنسيق JSON كما يلي:
            [
                {{
                    "title": "عنوان التوصية",
                    "description": "وصف التوصية",
                    "actions": ["إجراء 1", "إجراء 2", "إجراء 3"]
                }},
                ...
            ]
            """
            
            # استدعاء OpenAI API
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "أنت مستشار أعمال محترف متخصص في تحسين أداء الشركات الصغيرة والمتوسطة. قدم توصيات عملية وقابلة للتنفيذ بتنسيق JSON."},
                    {"role": "user", "content": context}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            # استخراج التوصيات
            ai_recommendations_text = response.choices[0].message.content
            
            # تنظيف النص واستخراج JSON
            ai_recommendations_text = ai_recommendations_text.strip()
            if ai_recommendations_text.startswith('```json'):
                ai_recommendations_text = ai_recommendations_text[7:]
            if ai_recommendations_text.endswith('```'):
                ai_recommendations_text = ai_recommendations_text[:-3]
            
            # تحويل النص إلى JSON
            ai_recommendations = json.loads(ai_recommendations_text)
            
            # إضافة معلومات الأولوية والنوع
            for i, rec in enumerate(ai_recommendations):
                rec['priority'] = 'high' if i == 0 else 'medium'
                rec['type'] = 'ai_generated'
            
            return ai_recommendations
            
        except Exception as e:
            logger.error(f"Error generating AI recommendations: {str(e)}")
            return [{
                'type': 'error',
                'priority': 'low',
                'title': 'حدث خطأ أثناء توليد التوصيات',
                'description': f'لم نتمكن من توليد توصيات ذكية في هذا الوقت. يرجى المحاولة مرة أخرى لاحقًا. الخطأ: {str(e)}',
                'actions': ['المحاولة مرة أخرى لاحقًا']
            }]
    
    def get_all_recommendations(self, user_id):
        """
الحصول على جميع التوصيات المتاحة
        """
        try:
            # جمع التوصيات من مختلف الفئات
            cash_flow_recommendations = self.get_cash_flow_recommendations(user_id)
            sales_recommendations = self.get_sales_recommendations(user_id)
            profitability_recommendations = self.get_profitability_recommendations(user_id)
            ai_recommendations = self.get_ai_recommendations(user_id)
            
            # دمج جميع التوصيات
            all_recommendations = cash_flow_recommendations + sales_recommendations + profitability_recommendations + ai_recommendations
            
            # ترتيب التوصيات حسب الأولوية
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            all_recommendations.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 3))
            
            return {
                'status': 'success',
                'recommendations': all_recommendations,
                'count': len(all_recommendations),
                'categories': {
                    'cash_flow': len(cash_flow_recommendations),
                    'sales': len(sales_recommendations),
                    'profitability': len(profitability_recommendations),
                    'ai_generated': len(ai_recommendations)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting all recommendations: {str(e)}")
            return {
                'status': 'error',
                'message': f'حدث خطأ أثناء الحصول على التوصيات: {str(e)}'
            }