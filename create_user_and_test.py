#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إنشاء مستخدم واختبار إضافة عميل
Create user and test client addition
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from extensions import db
from models.user import User
from models.client import Client
from flask import Flask

def create_simple_app():
    """إنشاء تطبيق Flask بسيط"""
    app = Flask(__name__)
    
    # إعدادات قاعدة البيانات
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smartbiz.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'dev-secret-key'
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    return app

def create_user():
    """إنشاء مستخدم افتراضي"""
    
    app = create_simple_app()
    
    with app.app_context():
        try:
            print("🔄 إنشاء مستخدم افتراضي...")
            
            # التحقق من وجود مستخدم
            existing_user = User.query.first()
            if existing_user:
                print(f"✅ المستخدم موجود مسبقاً: {existing_user.email}")
                return existing_user
            
            # إنشاء مستخدم جديد
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                is_active=True,
                is_admin=True
            )
            
            # تعيين كلمة المرور
            admin_user.password = 'admin123'
            
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"✅ تم إنشاء المستخدم: {admin_user.email}")
            return admin_user
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return None

def test_client_creation():
    """اختبار إنشاء عميل"""
    
    app = create_simple_app()
    
    with app.app_context():
        try:
            print("\n🔄 اختبار إنشاء عميل...")
            
            # الحصول على المستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين")
                return False
            
            print(f"✅ المستخدم: {user.email} (ID: {user.id})")
            
            # إنشاء عميل تجريبي
            test_client = Client(
                user_id=user.id,
                name='عميل تجريبي نهائي',
                email='<EMAIL>',
                phone='+************',
                address='الرياض، المملكة العربية السعودية',
                company_name='شركة الاختبار النهائي',
                payment_method='cash',
                notes='عميل للاختبار النهائي',
                is_active=True
            )
            
            print("🔄 إضافة العميل إلى قاعدة البيانات...")
            db.session.add(test_client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل بـ ID: {test_client.id}")
            print(f"   الاسم: {test_client.name}")
            print(f"   البريد: {test_client.email}")
            print(f"   الشركة: {test_client.company_name}")
            print(f"   الهاتف: {test_client.phone}")
            
            # التحقق من الحفظ
            saved_client = Client.query.get(test_client.id)
            if saved_client:
                print("✅ تم التحقق من حفظ العميل في قاعدة البيانات")
                
                # عرض جميع العملاء
                all_clients = Client.query.all()
                print(f"\n📊 إجمالي العملاء في النظام: {len(all_clients)}")
                for client in all_clients:
                    print(f"  - {client.name} ({client.email}) - شركة: {client.company_name}")
                
                return True
            else:
                print("❌ فشل في التحقق من حفظ العميل")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في اختبار العميل: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return False

def test_form_simulation():
    """محاكاة إرسال نموذج إضافة عميل"""
    
    app = create_simple_app()
    
    with app.app_context():
        try:
            print("\n🔄 محاكاة نموذج إضافة عميل...")
            
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين")
                return False
            
            # محاكاة بيانات النموذج
            form_data = {
                'name': 'أحمد محمد علي',
                'email': '<EMAIL>',
                'phone': '+************',
                'address': 'الرياض، المملكة العربية السعودية',
                'company_name': 'شركة الأمل للتجارة',
                'payment_method': 'bank_transfer',
                'notes': 'عميل مهم - يفضل التواصل عبر الهاتف',
                'is_active': 'on'  # كما يأتي من checkbox
            }
            
            # معالجة البيانات كما في blueprint
            name = form_data.get('name')
            email = form_data.get('email')
            phone = form_data.get('phone')
            address = form_data.get('address')
            company = form_data.get('company_name')
            payment_method = form_data.get('payment_method')
            notes = form_data.get('notes')
            is_active = True if form_data.get('is_active') else False
            
            print(f"📋 البيانات المعالجة:")
            print(f"   name: {name}")
            print(f"   email: {email}")
            print(f"   company: {company}")
            print(f"   is_active: {is_active}")
            
            # إنشاء العميل كما في blueprint
            new_client = Client(
                user_id=user.id,
                name=name,
                email=email if email else None,
                phone=phone if phone else None,
                address=address if address else None,
                company_name=company if company else None,
                payment_method=payment_method if payment_method else None,
                notes=notes if notes else None,
                is_active=is_active
            )
            
            db.session.add(new_client)
            db.session.commit()
            
            print(f"✅ تم حفظ العميل بـ ID: {new_client.id}")
            print("🎉 محاكاة النموذج نجحت!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة النموذج: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🚀 بدء إنشاء المستخدم واختبار العملاء...")
    
    user = create_user()
    success1 = test_client_creation() if user else False
    success2 = test_form_simulation() if user else False
    
    print("\n" + "="*50)
    if user and success1 and success2:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ المستخدم تم إنشاؤه")
        print("✅ إنشاء العميل يعمل")
        print("✅ محاكاة النموذج تعمل")
        print("\n🌟 يمكنك الآن استخدام التطبيق:")
        print("   البريد: <EMAIL>")
        print("   كلمة المرور: admin123")
    else:
        print("❌ حدثت مشاكل في الاختبارات")
    
    input("\nاضغط Enter للخروج...")
