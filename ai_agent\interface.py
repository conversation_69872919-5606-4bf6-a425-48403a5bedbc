#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
واجهة وكيل الذكاء الاصطناعي
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
import json
import logging
from datetime import datetime
from .agent import SmartBizAgent

# إعداد السجل
logger = logging.getLogger(__name__)

# إنشاء Blueprint
ai_agent_bp = Blueprint('ai_agent', __name__, url_prefix='/ai')

@ai_agent_bp.route('/', methods=['GET'])
@login_required
def ai_dashboard():
    """
عرض لوحة تحكم الوكيل الذكي
    """
    return render_template('ai_agent/dashboard.html')

@ai_agent_bp.route('/insights', methods=['GET'])
@login_required
def get_insights():
    """
الحصول على رؤى الأعمال
    """
    try:
        agent = SmartBizAgent(current_app.db)
        insights = agent.get_business_insights(current_user.id)
        return jsonify(insights)
    except Exception as e:
        logger.error(f"Error getting business insights: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء الحصول على رؤى الأعمال: {str(e)}'
        }), 500

@ai_agent_bp.route('/sales-trend', methods=['GET'])
@login_required
def get_sales_trend():
    """
الحصول على اتجاه المبيعات
    """
    try:
        period = request.args.get('period', 'month')
        months = int(request.args.get('months', 6))
        
        agent = SmartBizAgent(current_app.db)
        trend_data = agent.analyze_sales_trend(current_user.id, period, months)
        return jsonify(trend_data)
    except Exception as e:
        logger.error(f"Error analyzing sales trend: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء تحليل اتجاه المبيعات: {str(e)}'
        }), 500

@ai_agent_bp.route('/client-segments', methods=['GET'])
@login_required
def get_client_segments():
    """
الحصول على تحليل شرائح العملاء
    """
    try:
        agent = SmartBizAgent(current_app.db)
        segments_data = agent.analyze_client_segments(current_user.id)
        return jsonify(segments_data)
    except Exception as e:
        logger.error(f"Error analyzing client segments: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء تحليل شرائح العملاء: {str(e)}'
        }), 500

@ai_agent_bp.route('/cash-flow', methods=['GET'])
@login_required
def get_cash_flow():
    """
الحصول على تحليل التدفق النقدي
    """
    try:
        period = request.args.get('period', 'month')
        months = int(request.args.get('months', 12))
        
        agent = SmartBizAgent(current_app.db)
        cash_flow_data = agent.analyze_cash_flow(current_user.id, period, months)
        return jsonify(cash_flow_data)
    except Exception as e:
        logger.error(f"Error analyzing cash flow: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء تحليل التدفق النقدي: {str(e)}'
        }), 500

@ai_agent_bp.route('/unpaid-alerts', methods=['GET'])
@login_required
def get_unpaid_alerts():
    """
الحصول على تنبيهات الفواتير غير المدفوعة
    """
    try:
        days_threshold = int(request.args.get('days', 30))
        
        agent = SmartBizAgent(current_app.db)
        alerts = agent.get_unpaid_invoices_alert(current_user.id, days_threshold)
        return jsonify(alerts)
    except Exception as e:
        logger.error(f"Error getting unpaid invoice alerts: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء الحصول على تنبيهات الفواتير غير المدفوعة: {str(e)}'
        }), 500

@ai_agent_bp.route('/query', methods=['POST'])
@login_required
def process_query():
    """
معالجة استعلام بلغة طبيعية
    """
    try:
        data = request.get_json()
        query = data.get('query', '')
        language = data.get('language', 'ar')
        
        if not query:
            return jsonify({
                'status': 'error',
                'message': 'الاستعلام مطلوب'
            }), 400
        
        agent = SmartBizAgent(current_app.db)
        result = agent.process_natural_language_query(query, current_user.id, language)
        
        # تسجيل الاستعلام
        log_query(query, result)
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error processing natural language query: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء معالجة الاستعلام: {str(e)}'
        }), 500

@ai_agent_bp.route('/summarize', methods=['POST'])
@login_required
def summarize_text():
    """
تلخيص نص
    """
    try:
        data = request.get_json()
        text = data.get('text', '')
        language = data.get('language', 'ar')
        
        if not text:
            return jsonify({
                'status': 'error',
                'message': 'النص مطلوب'
            }), 400
        
        agent = SmartBizAgent(current_app.db)
        result = agent.summarize_meeting(text, language)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error summarizing text: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء تلخيص النص: {str(e)}'
        }), 500

@ai_agent_bp.route('/recommendations', methods=['GET'])
@login_required
def get_recommendations():
    """
الحصول على توصيات لتحسين الأعمال
    """
    try:
        agent = SmartBizAgent(current_app.db)
        recommendations = agent.generate_business_recommendation(current_user.id)
        return jsonify(recommendations)
    except Exception as e:
        logger.error(f"Error generating business recommendations: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء توليد توصيات الأعمال: {str(e)}'
        }), 500

def log_query(query, result):
    """
تسجيل الاستعلام ونتيجته
    """
    try:
        from models.ai import AIQuery
        
        # إنشاء سجل استعلام جديد
        ai_query = AIQuery(
            user_id=current_user.id,
            query_text=query,
            query_type=result.get('query_type', 'unknown'),
            response=json.dumps(result),
            timestamp=datetime.utcnow()
        )
        
        # حفظ في قاعدة البيانات
        current_app.db.session.add(ai_query)
        current_app.db.session.commit()
    except Exception as e:
        logger.error(f"Error logging AI query: {str(e)}")
        # لا نريد أن نفشل الاستجابة الرئيسية إذا فشل التسجيل
        pass