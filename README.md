# 🏢 SmartBiz Accounting - نظام المحاسبة الذكي

## 📋 نظرة عامة

نظام المحاسبة الذكي هو حل شامل ومتطور لإدارة الأعمال والمحاسبة، مصمم خصيصاً للشركات الناطقة بالعربية. يتميز النظام بواجهة مستخدم حديثة ومساعد ذكي مدعوم بالذكاء الاصطناعي.

## ✨ الميزات الرئيسية

### 🤖 المساعد الذكي المحسن
- **تكامل Google Gemini AI**: مساعد ذكي متقدم للمحاسبة والأعمال
- **دعم اللغة العربية**: تفاعل طبيعي باللغة العربية
- **التعلم التكيفي**: يتعلم من تفاعلات المستخدم لتحسين الإجابات
- **الإدخال الصوتي**: دعم التعرف على الصوت باللغة العربية
- **اقتراحات ذكية**: اقتراحات سياقية بناءً على الصفحة الحالية
- **تحليلات متقدمة**: تحليلات ذكية مستندة إلى بيانات المستخدم

### 💼 إدارة الأعمال
- **إدارة الفواتير**: إنشاء وتتبع الفواتير بسهولة
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **إدارة المشاريع**: تتبع المشاريع والمهام
- **التقارير المالية**: تقارير مفصلة وتحليلات

### 🔒 الأمان المتقدم
- **حماية XSS**: حماية متقدمة ضد هجمات Cross-Site Scripting
- **حماية CSRF**: حماية ضد هجمات Cross-Site Request Forgery
- **حماية Clickjacking**: منع هجمات النقر الخادع
- **تشفير البيانات**: تشفير متقدم لحماية البيانات الحساسة
- **مراقبة الأمان**: مراقبة مستمرة للأنشطة المشبوهة

### ⚡ الأداء المحسن
- **Service Worker**: تخزين مؤقت متقدم وعمل بدون اتصال
- **تحسين الصور**: ضغط وتحسين الصور تلقائياً
- **التحميل الكسول**: تحميل المحتوى عند الحاجة
- **مراقبة الأداء**: مراقبة مستمرة لأداء النظام
- **تحسين الذاكرة**: إدارة ذكية للذاكرة

### 🎨 واجهة المستخدم
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم RTL**: دعم كامل للغة العربية
- **الوضع المظلم**: دعم الوضع المظلم
- **رسوم متحركة سلسة**: تأثيرات بصرية جذابة
- **إمكانية الوصول**: مصمم للجميع

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.8+**
- **Flask**: إطار عمل الويب
- **SQLAlchemy**: ORM لقاعدة البيانات
- **Flask-Login**: إدارة المصادقة
- **Flask-WTF**: معالجة النماذج
- **Flask-Migrate**: إدارة قاعدة البيانات

### Frontend
- **HTML5 & CSS3**
- **Bootstrap 5 RTL**: إطار عمل CSS
- **JavaScript ES6+**
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات

### AI & ML
- **Google Gemini AI**: المساعد الذكي المتطور
- **Speech Recognition API**: التعرف على الصوت
- **Natural Language Processing**: معالجة اللغة الطبيعية
- **Incremental Learning System**: نظام التعلم التدريجي

### Database
- **SQLite** (للتطوير)
- **PostgreSQL** (للإنتاج)

## 🚀 التثبيت والإعداد

### المتطلبات
```bash
Python 3.8+
Node.js 14+ (اختياري للتطوير)
```

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/smartbiz-accounting.git
cd smartbiz-accounting
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة مفتاح OpenAI API
```

5. **إعداد قاعدة البيانات**
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

6. **تشغيل النظام**
```bash
flask run
```

## 🔧 الإعداد

### متغيرات البيئة

```env
# إعدادات Flask
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///smartbiz.db

# إعدادات OpenAI
OPENAI_API_KEY=sk-your-openai-api-key-here

# إعدادات الأمان
CSRF_SECRET_KEY=your-csrf-secret-key
SESSION_TIMEOUT=1800

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### إعداد OpenAI API

1. احصل على مفتاح API من [OpenAI](https://platform.openai.com/api-keys)
2. أضف المفتاح إلى ملف `.env`
3. تأكد من وجود رصيد كافٍ في حسابك

## 📱 الاستخدام

### تسجيل الدخول
1. انتقل إلى `http://localhost:5000`
2. انقر على "إنشاء حساب" لإنشاء حساب جديد
3. أو استخدم بيانات الاعتماد الافتراضية (إن وجدت)

### استخدام المساعد الذكي
1. انتقل إلى صفحة "المساعد الذكي"
2. اكتب سؤالك أو استخدم الإدخال الصوتي
3. اختر السياق المناسب (محاسبة، أعمال، عام)
4. استمتع بالإجابات الذكية والمفيدة

### إدارة الفواتير
1. انتقل إلى قسم "الفواتير"
2. انقر على "إنشاء فاتورة جديدة"
3. املأ البيانات المطلوبة
4. احفظ أو أرسل الفاتورة

## 🔒 الأمان

### الميزات الأمنية
- **تشفير كلمات المرور**: باستخدام bcrypt
- **حماية CSRF**: رموز CSRF لجميع النماذج
- **حماية XSS**: تنظيف وتحقق من المدخلات
- **جلسات آمنة**: إدارة آمنة للجلسات
- **مراقبة الأمان**: تسجيل ومراقبة الأنشطة المشبوهة

### أفضل الممارسات
- استخدم كلمات مرور قوية
- قم بتحديث النظام بانتظام
- راقب سجلات الأمان
- استخدم HTTPS في الإنتاج

## 📊 مراقبة الأداء

### المقاييس المراقبة
- **وقت تحميل الصفحة**
- **استخدام الذاكرة**
- **أداء الشبكة**
- **أخطاء JavaScript**
- **مقاييس Core Web Vitals**

### التحسينات التلقائية
- **ضغط الصور**
- **تخزين مؤقت ذكي**
- **تحميل كسول للمحتوى**
- **تحسين CSS و JavaScript**

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
python -m pytest tests/

# اختبارات التكامل
python -m pytest tests/integration/

# اختبارات الأداء
python -m pytest tests/performance/
```

### تغطية الكود
```bash
coverage run -m pytest
coverage report
coverage html
```

## 📦 النشر

### Docker
```bash
# بناء الصورة
docker build -t smartbiz-accounting .

# تشغيل الحاوية
docker run -p 5000:5000 smartbiz-accounting
```

### Heroku
```bash
# تسجيل الدخول إلى Heroku
heroku login

# إنشاء تطبيق جديد
heroku create your-app-name

# نشر التطبيق
git push heroku main
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs.smartbiz-accounting.com](https://docs.smartbiz-accounting.com)
- **المجتمع**: [community.smartbiz-accounting.com](https://community.smartbiz-accounting.com)

## 🙏 شكر وتقدير

- **OpenAI** لتوفير GPT-4 API
- **Bootstrap** لإطار عمل CSS الرائع
- **Flask** لإطار عمل Python المرن
- **المجتمع المفتوح المصدر** لجميع المكتبات المستخدمة

---

**تم تطويره بـ ❤️ للمجتمع العربي**
