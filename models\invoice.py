#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
نموذج الفاتورة
"""

from datetime import datetime, timedelta
import json
from app import db

class Invoice(db.Model):
    """
نموذج الفاتورة - يخزن معلومات الفواتير
    """
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200))
    description = db.Column(db.Text)
    issue_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='unpaid')  # unpaid, paid, partially_paid, cancelled, overdue
    payment_terms = db.Column(db.String(100))  # شروط الدفع
    notes = db.Column(db.Text)
    subtotal = db.Column(db.Float, nullable=False)
    tax_rate = db.Column(db.Float, default=0)  # نسبة الضريبة
    tax_amount = db.Column(db.Float, default=0)  # مبلغ الضريبة
    discount_rate = db.Column(db.Float, default=0)  # نسبة الخصم
    discount_amount = db.Column(db.Float, default=0)  # مبلغ الخصم
    total_amount = db.Column(db.Float, nullable=False)  # المبلغ الإجمالي
    amount_paid = db.Column(db.Float, default=0)  # المبلغ المدفوع
    amount_due = db.Column(db.Float)  # المبلغ المستحق
    currency = db.Column(db.String(3), default='MAD')  # العملة (درهم مغربي افتراضيًا)
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    payment_date = db.Column(db.Date)  # تاريخ الدفع
    reminder_sent = db.Column(db.Boolean, default=False)  # هل تم إرسال تذكير
    last_reminder_date = db.Column(db.DateTime)  # تاريخ آخر تذكير
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    transactions = db.relationship('Transaction', backref='invoice', lazy='dynamic')

    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        # تعيين تاريخ الاستحقاق افتراضيًا بعد 30 يومًا من تاريخ الإصدار
        if not self.due_date and self.issue_date:
            self.due_date = self.issue_date + timedelta(days=30)
        # حساب المبلغ المستحق
        self.amount_due = self.total_amount - self.amount_paid

    def update_status(self):
        """
تحديث حالة الفاتورة بناءً على المدفوعات
        """
        if self.amount_paid >= self.total_amount:
            self.status = 'paid'
        elif self.amount_paid > 0:
            self.status = 'partially_paid'
        elif datetime.utcnow().date() > self.due_date:
            self.status = 'overdue'
        else:
            self.status = 'unpaid'
        
        self.amount_due = self.total_amount - self.amount_paid
        db.session.commit()

    def add_payment(self, amount, payment_method=None, payment_date=None):
        """
إضافة دفعة للفاتورة
        """
        from models.transaction import Transaction
        
        if not payment_date:
            payment_date = datetime.utcnow().date()
            
        # إنشاء معاملة جديدة
        transaction = Transaction(
            date=payment_date,
            amount=amount,
            transaction_type='income',
            description=f"دفعة للفاتورة رقم {self.invoice_number}",
            payment_method=payment_method,
            client_id=self.client_id,
            project_id=self.project_id,
            invoice_id=self.id,
            user_id=self.user_id
        )
        
        db.session.add(transaction)
        
        # تحديث المبلغ المدفوع
        self.amount_paid += amount
        self.payment_method = payment_method
        self.payment_date = payment_date
        
        # تحديث حالة الفاتورة
        self.update_status()
        
        db.session.commit()
        return transaction

    def send_reminder(self):
        """
إرسال تذكير للعميل بالفاتورة المستحقة
        """
        # هنا يمكن إضافة رمز لإرسال بريد إلكتروني أو إشعار
        self.reminder_sent = True
        self.last_reminder_date = datetime.utcnow()
        db.session.commit()

    def calculate_totals(self):
        """
حساب المجاميع للفاتورة
        """
        # حساب المجموع الفرعي من عناصر الفاتورة
        self.subtotal = sum(item.total for item in self.items)
        
        # حساب مبلغ الضريبة
        self.tax_amount = self.subtotal * (self.tax_rate / 100) if self.tax_rate else 0
        
        # حساب مبلغ الخصم
        self.discount_amount = self.subtotal * (self.discount_rate / 100) if self.discount_rate else 0
        
        # حساب المبلغ الإجمالي
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        
        # حساب المبلغ المستحق
        self.amount_due = self.total_amount - self.amount_paid
        
        db.session.commit()

    def to_dict(self):
        """
تحويل بيانات الفاتورة إلى قاموس
        """
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'title': self.title,
            'description': self.description,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'status': self.status,
            'payment_terms': self.payment_terms,
            'subtotal': self.subtotal,
            'tax_rate': self.tax_rate,
            'tax_amount': self.tax_amount,
            'discount_rate': self.discount_rate,
            'discount_amount': self.discount_amount,
            'total_amount': self.total_amount,
            'amount_paid': self.amount_paid,
            'amount_due': self.amount_due,
            'currency': self.currency,
            'payment_method': self.payment_method,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'client_id': self.client_id,
            'project_id': self.project_id,
            'items': [item.to_dict() for item in self.items],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'


class InvoiceItem(db.Model):
    """
نموذج عنصر الفاتورة - يخزن عناصر الفاتورة
    """
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    description = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Float, nullable=False, default=1)
    unit_price = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), default='وحدة')  # وحدة القياس (ساعة، يوم، قطعة، إلخ)
    tax_rate = db.Column(db.Float, default=0)  # نسبة الضريبة على العنصر
    discount_rate = db.Column(db.Float, default=0)  # نسبة الخصم على العنصر
    total = db.Column(db.Float, nullable=False)  # المجموع (الكمية × السعر)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, **kwargs):
        super(InvoiceItem, self).__init__(**kwargs)
        # حساب المجموع تلقائيًا
        if self.quantity and self.unit_price:
            self.calculate_total()

    def calculate_total(self):
        """
حساب المجموع للعنصر
        """
        # المجموع الأساسي (الكمية × السعر)
        base_total = self.quantity * self.unit_price
        
        # تطبيق الخصم إذا وجد
        discount = base_total * (self.discount_rate / 100) if self.discount_rate else 0
        
        # تطبيق الضريبة إذا وجدت
        tax = (base_total - discount) * (self.tax_rate / 100) if self.tax_rate else 0
        
        # المجموع النهائي
        self.total = base_total - discount + tax
        return self.total

    def to_dict(self):
        """
تحويل بيانات عنصر الفاتورة إلى قاموس
        """
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'description': self.description,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'unit': self.unit,
            'tax_rate': self.tax_rate,
            'discount_rate': self.discount_rate,
            'total': self.total,
            'notes': self.notes
        }

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'