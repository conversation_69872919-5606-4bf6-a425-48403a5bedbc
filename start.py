#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل نظام SmartBiz الأساسي
"""

import sys
import os

def main():
    print("🚀 بدء تشغيل نظام SmartBiz...")
    
    try:
        # إضافة المجلد الحالي إلى مسار Python
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # استيراد التطبيق
        from app import create_app
        
        # إنشاء التطبيق
        app = create_app()
        
        print("✅ تم إنشاء التطبيق بنجاح")
        print("\n🌐 الروابط المتاحة:")
        print("   - الصفحة الرئيسية: http://127.0.0.1:5000")
        print("   - لوحة التحكم: http://127.0.0.1:5000/dashboard")
        print("   - العملاء: http://127.0.0.1:5000/clients")
        print("   - الفواتير: http://127.0.0.1:5000/invoices")
        print("   - المشاريع: http://127.0.0.1:5000/projects")
        print("   - التقارير: http://127.0.0.1:5000/reports")
        
        print("\n🔑 بيانات الدخول:")
        print("   البريد: <EMAIL>")
        print("   كلمة المرور: admin123")
        
        print("\n⚡ بدء الخادم على المنفذ 5000...")
        print("📝 اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 50)
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
