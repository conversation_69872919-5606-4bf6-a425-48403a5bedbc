# دليل المساهمة في SmartBiz Accounting

شكراً لاهتمامك بالمساهمة في SmartBiz Accounting! نحن نرحب بجميع أنواع المساهمات من المجتمع.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)
- [إرشادات التطوير](#إرشادات-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [المجتمع](#المجتمع)

## 🤝 كيفية المساهمة

### 1. Fork المشروع
```bash
# انقر على زر Fork في GitHub
# ثم استنسخ المشروع محلياً
git clone https://github.com/YOUR_USERNAME/smartbiz-accounting.git
cd smartbiz-accounting
```

### 2. إعداد بيئة التطوير
```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/macOS
# أو
venv\Scripts\activate     # Windows

# تثبيت التبعيات
make install-dev

# إعداد pre-commit hooks
pre-commit install
```

### 3. إنشاء فرع للميزة
```bash
git checkout -b feature/amazing-feature
# أو
git checkout -b fix/bug-description
```

### 4. تطوير الميزة
- اكتب الكود مع اتباع معايير المشروع
- أضف اختبارات للميزات الجديدة
- حدث التوثيق عند الحاجة

### 5. اختبار التغييرات
```bash
# تشغيل الاختبارات
make test

# فحص جودة الكود
make lint

# تنسيق الكود
make format
```

### 6. إرسال Pull Request
```bash
git add .
git commit -m "feat: add amazing feature"
git push origin feature/amazing-feature
```

## 🐛 الإبلاغ عن الأخطاء

عند الإبلاغ عن خطأ، يرجى تضمين:

### معلومات البيئة
- نظام التشغيل
- إصدار Python
- إصدار المتصفح (إن أمكن)

### وصف المشكلة
- وصف واضح للمشكلة
- خطوات إعادة إنتاج المشكلة
- السلوك المتوقع مقابل السلوك الفعلي
- لقطات شاشة (إن أمكن)

### مثال على تقرير خطأ
```markdown
**وصف المشكلة**
عند محاولة إنشاء فاتورة جديدة، يظهر خطأ 500.

**خطوات إعادة الإنتاج**
1. اذهب إلى صفحة الفواتير
2. انقر على "إنشاء فاتورة جديدة"
3. املأ البيانات المطلوبة
4. انقر على "حفظ"

**السلوك المتوقع**
يجب أن يتم إنشاء الفاتورة بنجاح.

**السلوك الفعلي**
يظهر خطأ 500 ولا يتم إنشاء الفاتورة.

**البيئة**
- نظام التشغيل: Ubuntu 22.04
- Python: 3.11.0
- المتصفح: Chrome 120.0
```

## 💡 اقتراح ميزات جديدة

نرحب بأفكاركم لتحسين النظام! عند اقتراح ميزة جديدة:

### قبل الاقتراح
- تأكد من أن الميزة غير موجودة
- ابحث في Issues الموجودة
- فكر في كيفية تناسب الميزة مع رؤية المشروع

### تنسيق الاقتراح
```markdown
**وصف الميزة**
وصف واضح للميزة المقترحة.

**المشكلة التي تحلها**
ما هي المشكلة التي ستحلها هذه الميزة؟

**الحل المقترح**
كيف تتصور تنفيذ هذه الميزة؟

**البدائل المدروسة**
هل فكرت في حلول أخرى؟

**معلومات إضافية**
أي معلومات أخرى مفيدة.
```

## 🛠️ إرشادات التطوير

### هيكل المشروع
```
smartbiz-accounting/
├── app.py                 # التطبيق الرئيسي
├── extensions.py          # إضافات Flask
├── config/               # ملفات التكوين
├── models/               # نماذج قاعدة البيانات
├── blueprints/           # مخططات Flask
├── templates/            # قوالب HTML
├── static/               # ملفات CSS/JS/صور

├── utils/                # أدوات مساعدة
├── tests/                # الاختبارات
└── docs/                 # التوثيق
```

### أنواع المساهمات

#### 🐛 إصلاح الأخطاء
- ابدأ اسم الفرع بـ `fix/`
- اكتب اختبار يثبت وجود الخطأ
- أصلح الخطأ
- تأكد من نجاح الاختبار

#### ✨ ميزات جديدة
- ابدأ اسم الفرع بـ `feature/`
- اكتب اختبارات للميزة الجديدة
- حدث التوثيق
- أضف مثال على الاستخدام

#### 📚 تحسين التوثيق
- ابدأ اسم الفرع بـ `docs/`
- تأكد من وضوح ودقة المحتوى
- أضف أمثلة عملية

#### 🎨 تحسين التصميم
- ابدأ اسم الفرع بـ `ui/`
- تأكد من التوافق مع الأجهزة المختلفة
- اتبع إرشادات التصميم

## 📏 معايير الكود

### Python
- اتبع PEP 8
- استخدم type hints
- اكتب docstrings للدوال والكلاسات
- أقصى طول سطر: 88 حرف

### JavaScript
- استخدم ES6+
- اتبع معايير Airbnb
- استخدم const/let بدلاً من var

### HTML/CSS
- استخدم HTML5 semantic elements
- اتبع معايير BEM للـ CSS
- تأكد من إمكانية الوصول (accessibility)

### رسائل Commit
استخدم تنسيق Conventional Commits:

```
type(scope): description

[optional body]

[optional footer]
```

أنواع الـ commits:
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث التوثيق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث اختبارات
- `chore`: مهام صيانة

أمثلة:
```
feat(invoices): add PDF export functionality
fix(auth): resolve login redirect issue
docs(api): update authentication examples
```

## 🔍 عملية المراجعة

### قبل إرسال Pull Request
- [ ] تشغيل جميع الاختبارات
- [ ] فحص جودة الكود
- [ ] تحديث التوثيق
- [ ] إضافة اختبارات للتغييرات
- [ ] التأكد من عدم كسر الميزات الموجودة

### معايير القبول
- جميع الاختبارات تنجح
- تغطية الاختبارات لا تقل عن 80%
- الكود يتبع معايير المشروع
- التوثيق محدث
- لا توجد تعارضات في الدمج

### عملية المراجعة
1. **المراجعة التلقائية**: GitHub Actions
2. **مراجعة الكود**: من قبل المطورين
3. **اختبار الميزة**: التأكد من عمل الميزة
4. **الدمج**: بعد الموافقة

## 👥 المجتمع

### قنوات التواصل
- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للنقاشات العامة
- **Discord**: للدردشة المباشرة
- **Email**: <EMAIL>

### قواعد السلوك
- كن محترماً ومهذباً
- ساعد الآخرين
- تقبل النقد البناء
- ركز على ما هو أفضل للمجتمع

### الحصول على المساعدة
- اقرأ التوثيق أولاً
- ابحث في Issues الموجودة
- اسأل في GitHub Discussions
- انضم إلى Discord للمساعدة السريعة

## 🏆 الاعتراف بالمساهمين

نحن نقدر جميع المساهمات ونعترف بها:
- إضافة اسمك إلى قائمة المساهمين
- ذكر مساهماتك في سجل التغييرات
- شارات خاصة للمساهمين النشطين

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT License).

---

شكراً لك على مساهمتك في جعل SmartBiz Accounting أفضل! 🚀
