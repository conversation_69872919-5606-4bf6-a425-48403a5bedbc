{% extends "base.html" %}

{% block title %}مساعد الذكاء الاصطناعي{% endblock %}

{% block content %}
<div class="container-fluid h-100">
    <div class="row h-100">
        <!-- Chat History Sidebar -->
        <div class="col-md-3 bg-light border-end h-100">
            <div class="p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>المحادثات
                    </h5>
                    <button class="btn btn-sm btn-primary" onclick="startNewChat()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <!-- Search Chat History -->
                <div class="mb-3">
                    <input type="text" class="form-control form-control-sm" placeholder="البحث في المحادثات..."
                        id="searchChats">
                </div>

                <!-- Chat History List -->
                <div id="chatHistory" class="chat-history">
                    <div class="chat-item active" data-chat-id="current">
                        <div class="chat-preview">
                            <div class="chat-title">محادثة جديدة</div>
                            <div class="chat-time">الآن</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="col-md-9 d-flex flex-column h-100">
            <!-- Chat Header -->
            <div class="chat-header bg-primary text-white p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">
                            <i class="fas fa-robot me-2"></i>مساعد الأعمال الذكي
                            <span class="expertise-indicator expertise-indicator-js ms-2">مبتدئ</span>
                        </h4>
                        <small>مساعدك الشخصي لإدارة الأعمال والفواتير</small>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-light btn-sm" onclick="clearChat()" title="مسح المحادثة">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="exportChat()" title="تصدير المحادثة">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="toggleSettings()" title="الإعدادات">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div class="chat-messages flex-grow-1 p-3" id="chatMessages">
                <!-- Welcome Message -->
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            مرحباً! أنا مساعدك الذكي لإدارة الأعمال. يمكنني مساعدتك في:
                            <ul class="mt-2">
                                <li>إنشاء وإدارة الفواتير</li>
                                <li>تحليل البيانات المالية</li>
                                <li>إدارة العملاء والمشاريع</li>
                                <li>إنشاء التقارير</li>
                                <li>الإجابة على أسئلتك حول النظام</li>
                            </ul>
                            كيف يمكنني مساعدتك اليوم؟
                        </div>
                        <div class="message-time">الآن</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions p-2 bg-light border-top">
                <div class="row g-2">
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" onclick="sendQuickMessage('أنشئ فاتورة جديدة')">
                            <i class="fas fa-file-invoice me-1"></i>إنشاء فاتورة
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-success btn-sm"
                            onclick="sendQuickMessage('أظهر تقرير المبيعات')">
                            <i class="fas fa-chart-bar me-1"></i>تقرير المبيعات
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-info btn-sm" onclick="sendQuickMessage('أضف عميل جديد')">
                            <i class="fas fa-user-plus me-1"></i>إضافة عميل
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-warning btn-sm"
                            onclick="sendQuickMessage('ما هي الفواتير المستحقة؟')">
                            <i class="fas fa-exclamation-triangle me-1"></i>الفواتير المستحقة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chat Input Area -->
            <div class="chat-input p-3 border-top">
                <form id="chatForm" onsubmit="sendMessage(event)">
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" placeholder="اكتب رسالتك هنا..."
                            autocomplete="off">
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleVoiceInput()"
                            title="الإدخال الصوتي">
                            <i class="fas fa-microphone" id="voiceIcon"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="attachFile()"
                            title="إرفاق ملف">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button type="submit" class="btn btn-primary" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>

                <!-- Typing Indicator -->
                <div id="typingIndicator" class="typing-indicator mt-2" style="display: none;">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="typing-text">المساعد يكتب...</span>
                </div>

                <!-- منطقة الاقتراحات السريعة المحسنة -->
                <div class="quick-suggestions mt-3" style="display: none;">
                    <h6>اقتراحات ذكية:</h6>
                    <!-- سيتم ملؤها ديناميكياً بناءً على السياق -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">إعدادات المساعد الذكي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="aiModel" class="form-label">نموذج الذكاء الاصطناعي</label>
                    <select class="form-select" id="aiModel">
                        <option value="gemini-pro" selected>Google Gemini Pro (متقدم)</option>
                        <option value="gemini-flash">Google Gemini Flash (سريع)</option>
                        <option value="gpt-4">GPT-4 (دقيق)</option>
                        <option value="claude-3">Claude-3 (متوازن)</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="responseLength" class="form-label">طول الإجابة</label>
                    <select class="form-select" id="responseLength">
                        <option value="short">قصيرة</option>
                        <option value="medium" selected>متوسطة</option>
                        <option value="long">مفصلة</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="expertiseLevel" class="form-label">مستوى الخبرة</label>
                    <select class="form-select" id="expertiseLevel">
                        <option value="beginner">مبتدئ</option>
                        <option value="intermediate" selected>متوسط</option>
                        <option value="expert">خبير</option>
                    </select>
                    <small class="text-muted">يساعد في تخصيص الإجابات حسب مستوى خبرتك</small>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoSave" checked>
                        <label class="form-check-label" for="autoSave">
                            حفظ المحادثات تلقائياً
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="soundNotifications" checked>
                        <label class="form-check-label" for="soundNotifications">
                            تنبيهات صوتية
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="adaptiveLearning" checked>
                        <label class="form-check-label" for="adaptiveLearning">
                            التعلم التكيفي
                        </label>
                        <small class="d-block text-muted">يتيح للمساعد التعلم من تفاعلاتك السابقة</small>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="contextMemory" checked>
                        <label class="form-check-label" for="contextMemory">
                            الذاكرة المتقدمة
                        </label>
                        <small class="d-block text-muted">يتذكر المساعد سياق المحادثات السابقة</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">حفظ الإعدادات</button>
            </div>
        </div>
    </div>
</div>

<!-- File Upload Input (Hidden) -->
<input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.txt,.csv,.xlsx"
    onchange="handleFileUpload(event)">
{% endblock %}

{% block styles %}
<style>
    .chat-history {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }

    .chat-item {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .chat-item:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }

    .chat-item.active {
        background-color: rgba(0, 123, 255, 0.2);
        border-left: 3px solid #007bff;
    }

    .chat-title {
        font-weight: 500;
        font-size: 0.9rem;
        margin-bottom: 2px;
    }

    .chat-time {
        font-size: 0.75rem;
        color: #6c757d;
    }

    /* تنسيق أزرار التقييم */
    .message-feedback {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .feedback-label {
        margin-right: 10px;
    }

    .feedback-buttons {
        display: flex;
        gap: 5px;
    }

    .feedback-button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.2s;
    }

    .feedback-button:hover {
        background-color: #e9ecef;
    }

    .feedback-button.active {
        color: #007bff;
    }

    .thumbs-up.active {
        color: #28a745;
    }

    .thumbs-down.active {
        color: #dc3545;
    }

    /* تنسيق إضافي للرسائل المحسنة */
    .ai-message.enhanced {
        background-color: #f0f7ff;
        border-left: 4px solid #007bff;
        box-shadow: 0 2px 5px rgba(0, 123, 255, 0.1);
    }

    /* تنسيق لإعدادات الذكاء الاصطناعي */
    .ai-settings-section {
        border-top: 1px solid #dee2e6;
        padding-top: 15px;
        margin-top: 15px;
    }

    .ai-settings-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #007bff;
    }

    .settings-row {
        margin-bottom: 10px;
    }

    /* تنسيق لمؤشر الخبرة */
    .expertise-indicator {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .expertise-beginner {
        background-color: #e9ecef;
        color: #495057;
    }

    .expertise-intermediate {
        background-color: #cff4fc;
        color: #055160;
    }

    .expertise-advanced {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .expertise-expert {
        background-color: #f8d7da;
        color: #842029;
    }

    .chat-messages {
        height: calc(100vh - 300px);
        overflow-y: auto;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .message {
        display: flex;
        margin-bottom: 20px;
        animation: fadeIn 0.3s ease-in;
    }

    .message.user-message {
        justify-content: flex-end;
    }

    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10px;
        font-size: 1.2rem;
    }

    .ai-message .message-avatar {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }

    .user-message .message-avatar {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        order: 2;
    }

    .message-content {
        max-width: 70%;
        background: white;
        border-radius: 18px;
        padding: 12px 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .user-message .message-content {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }

    .message-text {
        margin-bottom: 5px;
        line-height: 1.4;
    }

    .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        text-align: right;
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        color: #6c757d;
    }

    .typing-dots {
        display: flex;
        margin-right: 8px;
    }

    .typing-dots span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #007bff;
        margin: 0 2px;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) {
        animation-delay: -0.32s;
    }

    .typing-dots span:nth-child(2) {
        animation-delay: -0.16s;
    }

    @keyframes typing {

        0%,
        80%,
        100% {
            transform: scale(0.8);
            opacity: 0.5;
        }

        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .quick-actions {
        border-top: 1px solid #dee2e6;
    }

    .chat-input {
        background: white;
    }

    .voice-recording {
        background-color: #dc3545 !important;
        color: white !important;
    }

    .voice-recording .fa-microphone {
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Scrollbar Styling */
    .chat-messages::-webkit-scrollbar,
    .chat-history::-webkit-scrollbar {
        width: 6px;
    }

    .chat-messages::-webkit-scrollbar-track,
    .chat-history::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .chat-messages::-webkit-scrollbar-thumb,
    .chat-history::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .chat-messages::-webkit-scrollbar-thumb:hover,
    .chat-history::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* تحسينات النظام المحسن */
    .message.enhanced {
        border-left: 3px solid #007bff;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .intent-badge {
        display: inline-block;
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        margin-bottom: 5px;
    }

    .intent-badge i {
        margin-right: 3px;
    }

    .message-header {
        margin-bottom: 8px;
    }

    .message-keywords {
        margin-top: 8px;
        padding: 5px 10px;
        background: rgba(0, 123, 255, 0.1);
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }

    .message-keywords small {
        color: #495057;
        font-style: italic;
    }

    .quick-suggestions {
        margin-top: 15px;
        padding: 15px;
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
        border-radius: 10px;
        border: 1px solid #e9ecef;
    }

    .quick-suggestions h6 {
        color: #495057;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .quick-suggestions .btn {
        transition: all 0.3s ease;
    }

    .quick-suggestions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    /* تأثيرات الحركة */
    .message {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    let isRecording = false;
    let recognition = null;

    // Initialize speech recognition if available
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        recognition.lang = 'ar-SA';
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onresult = function (event) {
            const transcript = event.results[0][0].transcript;
            document.getElementById('messageInput').value = transcript;
        };

        recognition.onerror = function (event) {
            console.error('Speech recognition error:', event.error);
            stopVoiceInput();
        };

        recognition.onend = function () {
            stopVoiceInput();
        };
    }

    function sendMessage(event) {
        event.preventDefault();

        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message) return;

        // Add user message to chat
        addMessage(message, 'user');

        // Clear input
        messageInput.value = '';

        // Show typing indicator
        showTypingIndicator();

        // Send message to AI
        sendToAI(message);
    }

    function sendQuickMessage(message) {
        document.getElementById('messageInput').value = message;
        sendMessage(new Event('submit'));
    }

    function addMessage(text, sender) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const currentTime = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
        </div>
        <div class="message-content">
            <div class="message-text">${text}</div>
            <div class="message-time">${currentTime}</div>
        </div>
    `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function showTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'flex';
    }

    function hideTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'none';
    }

    // متغيرات النظام المحسن
    let currentConversationId = generateConversationId();
    let userPreferences = loadUserPreferences();

    async function sendToAI(message) {
        try {
            // تحضير سياق المحادثة المحسن
            const currentPage = window.location.pathname;
            const currentTime = new Date();
            const timeOfDay = getTimeOfDay(currentTime);

            // جمع معلومات السياق المحسنة
            const enhancedContext = {
                conversation_id: currentConversationId,
                timestamp: currentTime.toISOString(),
                user_preferences: userPreferences,
                session_data: {
                    current_page: currentPage,
                    time_of_day: timeOfDay,
                    device_type: getDeviceType(),
                    session_duration: getSessionDuration(userPreferences.lastSession)
                },
                adaptive_learning: userPreferences.adaptiveLearning,
                context_memory: userPreferences.contextMemory,
                expertise_level: userPreferences.expertiseLevel,
                previous_interactions: userPreferences.contextMemory ?
                    userPreferences.interactionHistory.slice(-5) : [] // آخر 5 تفاعلات فقط
            };

            console.log('Sending enhanced context to AI:', enhancedContext);

            const response = await fetch('/ai/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    context: enhancedContext
                })
            });

            const data = await response.json();

            // تحليل الاستجابة وإضافة تأخير ديناميكي
            const responseLength = data.response ? data.response.length : 0;
            const dynamicDelay = Math.min(Math.max(responseLength * 10, 800), 2000); // بين 800 و 2000 مللي ثانية

            setTimeout(() => {
                hideTypingIndicator();

                if (data.status === 'success') {
                    // إضافة الرد المحسن
                    addEnhancedMessage(data.response, data.intent, data.keywords, data.suggestions);

                    // تحديث الاقتراحات السريعة
                    if (data.suggestions && data.suggestions.length > 0) {
                        updateQuickSuggestions(data.suggestions);
                    }

                    // تحديث تفضيلات المستخدم وحفظ التفاعل
                    updateUserPreferences(data.intent, data.keywords);

                    // حفظ التفاعل في سجل التفاعلات إذا كان التعلم التكيفي مفعلاً
                    if (userPreferences.adaptiveLearning) {
                        saveInteraction(message, data.response, data.intent, data.keywords);
                    }

                    // تشغيل صوت الإشعار إذا كان مفعلاً
                    if (userPreferences.soundNotifications) {
                        playNotificationSound();
                    }
                } else {
                    addMessage(data.message || 'عذراً، حدث خطأ في الاتصال.', 'ai');
                }
            }, dynamicDelay);

        } catch (error) {
            console.error('Error sending message to AI:', error);
            setTimeout(() => {
                hideTypingIndicator();
                addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'ai');
            }, 1000);
        }
    }

    // وظائف مساعدة جديدة
    function getTimeOfDay(date) {
        const hours = date.getHours();
        if (hours >= 5 && hours < 12) return 'morning';
        if (hours >= 12 && hours < 17) return 'afternoon';
        if (hours >= 17 && hours < 21) return 'evening';
        return 'night';
    }

    function getDeviceType() {
        const userAgent = navigator.userAgent;
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
            return 'mobile';
        }
        return 'desktop';
    }

    function getSessionDuration(lastSession) {
        if (!lastSession) return 0;
        const lastSessionDate = new Date(lastSession);
        const now = new Date();
        return Math.floor((now - lastSessionDate) / 1000); // بالثواني
    }

    function saveInteraction(userMessage, aiResponse, intent, keywords) {
        // حفظ التفاعل في سجل التفاعلات
        const interaction = {
            timestamp: new Date().toISOString(),
            user_message: userMessage,
            ai_response: aiResponse,
            intent: intent,
            keywords: keywords
        };

        // إضافة التفاعل إلى سجل التفاعلات
        userPreferences.interactionHistory.push(interaction);

        // الاحتفاظ بآخر 20 تفاعل فقط
        if (userPreferences.interactionHistory.length > 20) {
            userPreferences.interactionHistory = userPreferences.interactionHistory.slice(-20);
        }

        // تحديث وقت آخر جلسة
        userPreferences.lastSession = new Date().toISOString();

        // حفظ التفضيلات المحدثة
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
    }

    function playNotificationSound() {
        // إنشاء عنصر صوت وتشغيله
        const audio = new Audio('/static/sounds/notification.mp3');
        audio.volume = 0.5;
        audio.play().catch(e => console.log('Could not play notification sound:', e));
    }

    function addEnhancedMessage(text, intent, keywords, suggestions) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message enhanced';
        messageDiv.dataset.timestamp = new Date().toISOString();

        const currentTime = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // إضافة أيقونة حسب نوع الرسالة
        const intentIcon = getIntentIcon(intent);

        messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-header">
                <span class="intent-badge">
                    <i class="fas fa-${intentIcon}"></i>
                    ${getIntentLabel(intent)}
                </span>
            </div>
            <div class="message-text">${text}</div>
            ${keywords && keywords.length > 0 ? `
                <div class="message-keywords">
                    <small>الكلمات المفتاحية: ${keywords.join(', ')}</small>
                </div>
            ` : ''}
            <div class="message-time">${currentTime}</div>
            <div class="message-feedback">
                <span class="feedback-label">هل كان هذا الرد مفيداً؟</span>
                <div class="feedback-buttons">
                    <button class="feedback-button thumbs-up" onclick="provideFeedback('${new Date().toISOString()}', 'positive')">
                        <i class="fas fa-thumbs-up"></i>
                    </button>
                    <button class="feedback-button thumbs-down" onclick="provideFeedback('${new Date().toISOString()}', 'negative')">
                        <i class="fas fa-thumbs-down"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // تحديث إحصائيات المستخدم
        userPreferences.totalInteractions++;
        userPreferences.successfulResponses++;

        // حفظ التفضيلات المحدثة
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
    }

    function getIntentIcon(intent) {
        const icons = {
            'greeting': 'hand-wave',
            'accounting_question': 'book',
            'data_query': 'database',
            'calculation': 'calculator',
            'analysis_request': 'chart-line',
            'help': 'question-circle',
            'general': 'comment'
        };
        return icons[intent] || 'comment';
    }

    function getIntentLabel(intent) {
        const labels = {
            'greeting': 'تحية',
            'accounting_question': 'سؤال محاسبي',
            'data_query': 'استعلام بيانات',
            'calculation': 'حساب',
            'analysis_request': 'طلب تحليل',
            'help': 'مساعدة',
            'general': 'عام'
        };
        return labels[intent] || 'عام';
    }

    function updateQuickSuggestions(suggestions) {
        const suggestionsContainer = document.querySelector('.quick-suggestions');
        if (!suggestionsContainer) return;

        if (suggestions && suggestions.length > 0) {
            suggestionsContainer.innerHTML = '<h6>اقتراحات ذكية:</h6>';

            suggestions.forEach(suggestion => {
                const button = document.createElement('button');
                button.className = 'btn btn-outline-primary btn-sm me-2 mb-2';
                button.textContent = suggestion;
                button.onclick = () => sendQuickMessage(suggestion);
                suggestionsContainer.appendChild(button);
            });

            // إظهار منطقة الاقتراحات
            suggestionsContainer.style.display = 'block';

            // إخفاء الاقتراحات بعد 30 ثانية
            setTimeout(() => {
                suggestionsContainer.style.display = 'none';
            }, 30000);
        } else {
            suggestionsContainer.style.display = 'none';
        }
    }

    function generateConversationId() {
        return 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    function loadUserPreferences() {
        const savedPreferences = localStorage.getItem('userPreferences');
        if (savedPreferences) {
            const userPrefs = JSON.parse(savedPreferences);

            // التأكد من وجود الحقول الجديدة
            return ensurePreferenceFields(userPrefs);
        } else {
            // إعدادات افتراضية
            return {
                language: 'ar',
                aiModel: 'gemini-pro',
                responseLength: 'medium',
                expertiseLevel: 'intermediate',
                autoSave: true,
                soundNotifications: true,
                adaptiveLearning: true,
                contextMemory: true,
                interactionHistory: [],
                favoriteTopics: {},
                intents: {},
                keywords: {},
                topKeywords: [],
                lastSession: new Date().toISOString(),
                totalInteractions: 0,
                successfulResponses: 0
            };
        }
    }

    function ensurePreferenceFields(prefs) {
        // التأكد من وجود جميع الحقول المطلوبة
        const defaultFields = {
            language: 'ar',
            aiModel: 'gemini-pro',
            responseLength: 'medium',
            expertiseLevel: 'intermediate',
            autoSave: true,
            soundNotifications: true,
            adaptiveLearning: true,
            contextMemory: true,
            interactionHistory: [],
            favoriteTopics: {},
            intents: {},
            keywords: {},
            topKeywords: [],
            lastSession: new Date().toISOString(),
            totalInteractions: 0,
            successfulResponses: 0
        };

        // إضافة الحقول المفقودة
        for (const [key, value] of Object.entries(defaultFields)) {
            if (prefs[key] === undefined) {
                prefs[key] = value;
            }
        }

        return prefs;
    }

    function updateUserPreferences(intent, keywords) {
        // تحديث تفضيلات المستخدم بناءً على النية والكلمات المفتاحية
        if (intent) {
            if (!userPreferences.intents) {
                userPreferences.intents = {};
            }

            if (!userPreferences.intents[intent]) {
                userPreferences.intents[intent] = 1;
            } else {
                userPreferences.intents[intent]++;
            }

            // تحديث قائمة المواضيع المفضلة
            updateFavoriteTopics(intent);
        }

        if (keywords && keywords.length > 0) {
            if (!userPreferences.keywords) {
                userPreferences.keywords = {};
            }

            keywords.forEach(keyword => {
                if (!userPreferences.keywords[keyword]) {
                    userPreferences.keywords[keyword] = 1;
                } else {
                    userPreferences.keywords[keyword]++;
                }
            });

            // تحديث قائمة الكلمات المفتاحية الأكثر استخداماً
            updateTopKeywords();
        }

        // تحديث مستوى الخبرة تلقائياً إذا كان التعلم التكيفي مفعلاً
        if (userPreferences.adaptiveLearning) {
            updateExpertiseLevel();
        }

        // حفظ التفضيلات المحدثة
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
    }

    function updateFavoriteTopics(intent) {
        // التأكد من وجود مصفوفة المواضيع المفضلة
        if (!userPreferences.favoriteTopics) {
            userPreferences.favoriteTopics = [];
        }

        // البحث عن الموضوع في المصفوفة
        const existingTopic = userPreferences.favoriteTopics.find(topic => topic.name === intent);

        if (existingTopic) {
            // زيادة عدد مرات الاستخدام
            existingTopic.count++;
            // إعادة ترتيب المصفوفة حسب عدد مرات الاستخدام
            userPreferences.favoriteTopics.sort((a, b) => b.count - a.count);
        } else {
            // إضافة موضوع جديد
            userPreferences.favoriteTopics.push({
                name: intent,
                count: 1,
                lastUsed: new Date().toISOString()
            });
        }

        // الاحتفاظ بأكثر 10 مواضيع استخداماً فقط
        if (userPreferences.favoriteTopics.length > 10) {
            userPreferences.favoriteTopics = userPreferences.favoriteTopics.slice(0, 10);
        }
    }

    function updateTopKeywords() {
        // تحويل الكلمات المفتاحية إلى مصفوفة مرتبة
        const sortedKeywords = Object.entries(userPreferences.keywords)
            .map(([keyword, count]) => ({ keyword, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 20); // الاحتفاظ بأكثر 20 كلمة مفتاحية استخداماً

        // تخزين الكلمات المفتاحية الأكثر استخداماً
        userPreferences.topKeywords = sortedKeywords;
    }

    function updateExpertiseLevel() {
        // تحديث مستوى الخبرة تلقائياً بناءً على عدد التفاعلات
        const interactionCount = userPreferences.interactionHistory ? userPreferences.interactionHistory.length : 0;
        const intentCount = userPreferences.intents ? Object.keys(userPreferences.intents).length : 0;

        // حساب النقاط بناءً على عدد التفاعلات وتنوع المواضيع
        const points = interactionCount + (intentCount * 2);

        // تحديد مستوى الخبرة بناءً على النقاط
        if (points < 10) {
            userPreferences.expertiseLevel = 'beginner';
        } else if (points < 30) {
            userPreferences.expertiseLevel = 'intermediate';
        } else if (points < 60) {
            userPreferences.expertiseLevel = 'advanced';
        } else {
            userPreferences.expertiseLevel = 'expert';
        }

        // تحديث عنصر واجهة المستخدم إذا كان موجوداً
        const expertiseLevelSelect = document.getElementById('expertiseLevel');
        if (expertiseLevelSelect) {
            expertiseLevelSelect.value = userPreferences.expertiseLevel;
        }
    }

    function toggleVoiceInput() {
        if (!recognition) {
            alert('الإدخال الصوتي غير مدعوم في هذا المتصفح');
            return;
        }

        if (isRecording) {
            stopVoiceInput();
        } else {
            startVoiceInput();
        }
    }

    function startVoiceInput() {
        isRecording = true;
        const voiceIcon = document.getElementById('voiceIcon');
        const button = voiceIcon.parentElement;

        button.classList.add('voice-recording');
        voiceIcon.className = 'fas fa-microphone';

        recognition.start();
    }

    function stopVoiceInput() {
        isRecording = false;
        const voiceIcon = document.getElementById('voiceIcon');
        const button = voiceIcon.parentElement;

        button.classList.remove('voice-recording');
        voiceIcon.className = 'fas fa-microphone';

        if (recognition) {
            recognition.stop();
        }
    }

    function attachFile() {
        document.getElementById('fileInput').click();
    }

    function handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
            addMessage(`تم إرفاق الملف: ${file.name}`, 'user');
            // Here you would typically upload the file to the server
            showTypingIndicator();
            setTimeout(() => {
                hideTypingIndicator();
                addMessage('تم استلام الملف بنجاح. كيف يمكنني مساعدتك في تحليله؟', 'ai');
            }, 2000);
        }
    }

    function clearChat() {
        if (confirm('هل أنت متأكد من رغبتك في مسح المحادثة؟')) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">تم مسح المحادثة. كيف يمكنني مساعدتك؟</div>
                    <div class="message-time">الآن</div>
                </div>
            </div>
        `;
        }
    }

    function exportChat() {
        const messages = document.querySelectorAll('.message');
        let chatText = 'محادثة مع المساعد الذكي\n';
        chatText += '='.repeat(30) + '\n\n';

        messages.forEach(message => {
            const sender = message.classList.contains('user-message') ? 'أنت' : 'المساعد';
            const text = message.querySelector('.message-text').textContent;
            const time = message.querySelector('.message-time').textContent;

            chatText += `[${time}] ${sender}: ${text}\n\n`;
        });

        const blob = new Blob([chatText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }

    function toggleSettings() {
        const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
        modal.show();
    }

    function saveSettings() {
        // حفظ إعدادات النموذج
        userPreferences.aiModel = document.getElementById('aiModel').value;
        userPreferences.responseLength = document.getElementById('responseLength').value;
        userPreferences.expertiseLevel = document.getElementById('expertiseLevel').value;

        // حفظ إعدادات الواجهة
        userPreferences.autoSave = document.getElementById('autoSave').checked;
        userPreferences.soundNotifications = document.getElementById('soundNotifications').checked;

        // حفظ إعدادات التعلم الذكي
        userPreferences.adaptiveLearning = document.getElementById('adaptiveLearning').checked;
        userPreferences.contextMemory = document.getElementById('contextMemory').checked;

        // إذا تم تعطيل ذاكرة السياق، قم بمسح سجل التفاعلات
        if (!userPreferences.contextMemory) {
            userPreferences.interactionHistory = [];
        }

        // حفظ التفضيلات في التخزين المحلي
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));

        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
        modal.hide();

        // عرض رسالة تأكيد
        alert('تم حفظ الإعدادات بنجاح');

        // تحديث واجهة المستخدم
        updateUIFromPreferences();

        // تحديث مؤشر مستوى الخبرة
        updateExpertiseIndicator();

        // إرسال التفضيلات إلى الخادم إذا كان الحفظ التلقائي مفعلاً
        if (userPreferences.autoSave) {
            sendPreferencesToServer();
        }
    }

    async function sendPreferencesToServer() {
        try {
            const response = await fetch('/ai/api/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    preferences: userPreferences
                })
            });

            const data = await response.json();
            if (data.status === 'success') {
                console.log('تم حفظ التفضيلات على الخادم بنجاح');
            } else {
                console.error('فشل حفظ التفضيلات على الخادم:', data.message);
            }
        } catch (error) {
            console.error('خطأ في إرسال التفضيلات إلى الخادم:', error);
        }
    }

    function updateUIFromPreferences() {
        // تحديث عناصر واجهة المستخدم من التفضيلات
        const aiModelSelect = document.getElementById('aiModel');
        if (aiModelSelect) {
            aiModelSelect.value = userPreferences.aiModel || 'gemini-pro';
        }

        const responseLengthSelect = document.getElementById('responseLength');
        if (responseLengthSelect) {
            responseLengthSelect.value = userPreferences.responseLength || 'medium';
        }

        const expertiseLevelSelect = document.getElementById('expertiseLevel');
        if (expertiseLevelSelect) {
            expertiseLevelSelect.value = userPreferences.expertiseLevel || 'intermediate';
        }

        // تحديث مؤشر مستوى الخبرة
        updateExpertiseIndicator();

        // تحديث الاقتراحات السريعة
        loadQuickSuggestions();

        // تحديث عناصر واجهة المستخدم الأخرى
        const adaptiveLearningSwitch = document.getElementById('adaptiveLearning');
        if (adaptiveLearningSwitch) {
            adaptiveLearningSwitch.checked = userPreferences.adaptiveLearning !== false;
        }

        const contextMemorySwitch = document.getElementById('contextMemory');
        if (contextMemorySwitch) {
            contextMemorySwitch.checked = userPreferences.contextMemory !== false;
        }

        const autoSaveSwitch = document.getElementById('autoSave');
        if (autoSaveSwitch) {
            autoSaveSwitch.checked = userPreferences.autoSave !== false;
        }

        const soundNotificationsSwitch = document.getElementById('soundNotifications');
        if (soundNotificationsSwitch) {
            soundNotificationsSwitch.checked = userPreferences.soundNotifications !== false;
        }
    }

    function loadSettings() {
        const settings = localStorage.getItem('aiChatSettings');
        if (settings) {
            const parsed = JSON.parse(settings);
            document.getElementById('aiModel').value = parsed.aiModel || 'gpt-4';
            document.getElementById('responseLength').value = parsed.responseLength || 'medium';
            document.getElementById('autoSave').checked = parsed.autoSave !== false;
            document.getElementById('soundNotifications').checked = parsed.soundNotifications !== false;
        }
    }

    function saveSettings() {
        // جمع الإعدادات من النموذج
        const settings = {
            aiModel: document.getElementById('aiModel').value,
            responseLength: document.getElementById('responseLength').value,
            autoSave: document.getElementById('autoSave').checked,
            soundNotifications: document.getElementById('soundNotifications').checked
        };

        // حفظ الإعدادات في التخزين المحلي
        localStorage.setItem('aiChatSettings', JSON.stringify(settings));

        // تحديث تفضيلات المستخدم
        userPreferences.expertiseLevel = document.getElementById('expertiseLevel').value;
        userPreferences.adaptiveLearning = document.getElementById('adaptiveLearning').checked;
        userPreferences.contextMemory = document.getElementById('contextMemory').checked;
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));

        // تحديث واجهة المستخدم
        updateExpertiseIndicator();
        loadQuickSuggestions();

        // إغلاق نافذة الإعدادات
        const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
        if (modal) {
            modal.hide();
        }

        // إظهار رسالة نجاح
        showToast('تم حفظ الإعدادات بنجاح');
    }

    function startNewChat() {
        clearChat();
    }

    function toggleSettings() {
        // الحصول على مرجع لنافذة الإعدادات
        const settingsModal = document.getElementById('settingsModal');

        // تحميل الإعدادات الحالية قبل فتح النافذة
        const expertiseLevelSelect = document.getElementById('expertiseLevel');
        if (expertiseLevelSelect) {
            expertiseLevelSelect.value = userPreferences.expertiseLevel || 'intermediate';
        }

        const adaptiveLearningSwitch = document.getElementById('adaptiveLearning');
        if (adaptiveLearningSwitch) {
            adaptiveLearningSwitch.checked = userPreferences.adaptiveLearning !== false;
        }

        const contextMemorySwitch = document.getElementById('contextMemory');
        if (contextMemorySwitch) {
            contextMemorySwitch.checked = userPreferences.contextMemory !== false;
        }

        // فتح نافذة الإعدادات
        const modal = new bootstrap.Modal(settingsModal);
        modal.show();
    }

    function provideFeedback(timestamp, feedbackType) {
        console.log(`تقديم تقييم: ${feedbackType} للرسالة بتاريخ ${timestamp}`);

        // تحديث واجهة المستخدم
        const messageElement = document.querySelector(`.ai-message[data-timestamp="${timestamp}"]`);
        if (messageElement) {
            const feedbackButtons = messageElement.querySelectorAll('.feedback-button');
            feedbackButtons.forEach(button => {
                button.classList.remove('active');
            });

            if (feedbackType === 'positive') {
                messageElement.querySelector('.thumbs-up').classList.add('active');
            } else {
                messageElement.querySelector('.thumbs-down').classList.add('active');
            }

            // إظهار رسالة شكر
            const feedbackLabel = messageElement.querySelector('.feedback-label');
            if (feedbackLabel) {
                feedbackLabel.textContent = 'شكراً على تقييمك!';

                // إعادة النص الأصلي بعد 3 ثوانٍ
                setTimeout(() => {
                    feedbackLabel.textContent = 'هل كان هذا الرد مفيداً؟';
                }, 3000);
            }
        }

        // تحديث إحصائيات المستخدم
        if (feedbackType === 'positive') {
            userPreferences.feedbackRating = (userPreferences.feedbackRating || 0) + 1;
        } else {
            userPreferences.feedbackRating = (userPreferences.feedbackRating || 0) - 0.5;
        }

        // حفظ التفضيلات المحدثة
        localStorage.setItem('userPreferences', JSON.stringify(userPreferences));

        // إرسال التقييم إلى الخادم
        sendFeedbackToServer(timestamp, feedbackType);
    }

    async function sendFeedbackToServer(timestamp, feedbackType) {
        try {
            const response = await fetch('/ai/api/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    conversation_id: currentConversationId,
                    message_timestamp: timestamp,
                    feedback_type: feedbackType,
                    user_preferences: userPreferences
                })
            });

            const data = await response.json();
            if (data.status === 'success') {
                console.log('تم إرسال التقييم بنجاح');

                // تحديث الاقتراحات إذا كانت موجودة
                if (data.suggestions && data.suggestions.length > 0) {
                    updateQuickSuggestions(data.suggestions);
                }

                // تحديث تفضيلات المستخدم إذا كانت موجودة
                if (data.user_preferences) {
                    Object.assign(userPreferences, data.user_preferences);
                    localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
                }
            } else {
                console.error('فشل إرسال التقييم:', data.message);
            }
        } catch (error) {
            console.error('خطأ في إرسال التقييم:', error);
        }
    }

    // تحميل الاقتراحات السريعة
    function loadQuickSuggestions() {
        const quickSuggestionsContainer = document.querySelector('.quick-suggestions');
        if (!quickSuggestionsContainer) return;

        quickSuggestionsContainer.innerHTML = '<h6>اقتراحات ذكية:</h6>';

        // اقتراحات افتراضية
        const defaultSuggestions = [
            'كيف يمكنني البدء في تعلم البرمجة؟',
            'ما هي أفضل لغات البرمجة للمبتدئين؟',
            'اشرح لي مفهوم الذكاء الاصطناعي',
            'ما هي أهم تقنيات الويب الحديثة؟',
            'كيف أطور مهاراتي في تحليل البيانات؟'
        ];

        // استخدام الاقتراحات المخصصة إذا كانت متوفرة
        let suggestions = defaultSuggestions;

        // إذا كان التعلم التكيفي مفعلاً وهناك مواضيع مفضلة، استخدمها لتخصيص الاقتراحات
        if (userPreferences.adaptiveLearning && userPreferences.favoriteTopics && userPreferences.favoriteTopics.length > 0) {
            suggestions = generateCustomSuggestions();
        }

        // إضافة الاقتراحات إلى الواجهة
        suggestions.forEach(suggestion => {
            const button = document.createElement('button');
            button.className = 'btn btn-outline-primary btn-sm me-2 mb-2';
            button.textContent = suggestion;
            button.addEventListener('click', () => {
                document.getElementById('messageInput').value = suggestion;
                sendMessage(new Event('submit'));
            });
            quickSuggestionsContainer.appendChild(button);
        });

        // إظهار منطقة الاقتراحات
        quickSuggestionsContainer.style.display = 'block';
    }

    // توليد اقتراحات مخصصة بناءً على تفضيلات المستخدم
    function generateCustomSuggestions() {
        const customSuggestions = [];
        const expertiseLevel = userPreferences.expertiseLevel || 'beginner';

        // قائمة من قوالب الأسئلة حسب مستوى الخبرة
        const questionTemplates = {
            beginner: [
                'ما هي أساسيات {topic}؟',
                'كيف أبدأ في تعلم {topic}؟',
                'اشرح لي مفهوم {topic} بطريقة مبسطة',
                'ما هي الموارد المناسبة للمبتدئين في {topic}؟',
                'ما هي أهم المصطلحات في مجال {topic}؟'
            ],
            intermediate: [
                'كيف أطور مهاراتي في {topic}؟',
                'ما هي أفضل الممارسات في {topic}؟',
                'اشرح لي كيفية {topic} مع أمثلة عملية',
                'ما هي الأدوات المستخدمة في {topic}؟',
                'كيف أحل مشكلة شائعة في {topic}؟'
            ],
            advanced: [
                'ما هي أحدث التقنيات في مجال {topic}؟',
                'كيف يمكنني تحسين أداء {topic}؟',
                'اشرح لي مفهوماً متقدماً في {topic}',
                'ما هي استراتيجيات {topic} للمشاريع الكبيرة؟',
                'كيف أتعامل مع تحديات {topic} المعقدة؟'
            ],
            expert: [
                'ما هي أحدث الأبحاث في مجال {topic}؟',
                'كيف يمكنني المساهمة في تطوير {topic}؟',
                'ناقش معي الاتجاهات المستقبلية في {topic}',
                'ما هي الحلول المبتكرة لمشكلات {topic}؟',
                'كيف أقوم بتحسين/تطوير {topic} بطرق غير تقليدية؟'
            ]
        };

        // اختيار القوالب المناسبة لمستوى الخبرة
        const templates = questionTemplates[expertiseLevel] || questionTemplates.beginner;

        // استخدام المواضيع المفضلة لتوليد اقتراحات مخصصة
        let favoriteTopics = [];

        // التحقق من نوع favoriteTopics (مصفوفة أو كائن)
        if (Array.isArray(userPreferences.favoriteTopics)) {
            favoriteTopics = userPreferences.favoriteTopics.slice(0, 5); // أخذ أهم 5 مواضيع
        } else if (typeof userPreferences.favoriteTopics === 'object') {
            // تحويل الكائن إلى مصفوفة مرتبة حسب العدد
            favoriteTopics = Object.entries(userPreferences.favoriteTopics)
                .map(([name, count]) => ({ name, count }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 5);
        }

        favoriteTopics.forEach(topic => {
            // اختيار قالب عشوائي لكل موضوع
            const randomTemplate = templates[Math.floor(Math.random() * templates.length)];
            const topicName = typeof topic === 'object' ? topic.name : topic;
            const suggestion = randomTemplate.replace('{topic}', topicName);
            customSuggestions.push(suggestion);
        });

        // إضافة بعض الاقتراحات العامة إذا لم يكن هناك ما يكفي من المواضيع المفضلة
        while (customSuggestions.length < 5) {
            const defaultSuggestion = [
                'كيف يمكنني تحسين مهاراتي في البرمجة؟',
                'ما هي أحدث التقنيات في مجال الذكاء الاصطناعي؟',
                'اشرح لي مفهوم تعلم الآلة',
                'كيف أبدأ مشروعاً في مجال تطوير الويب؟',
                'ما هي أفضل الممارسات في تطوير البرمجيات؟'
            ][Math.floor(Math.random() * 5)];

            if (!customSuggestions.includes(defaultSuggestion)) {
                customSuggestions.push(defaultSuggestion);
            }
        }

        return customSuggestions;
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
        loadSettings();

        // تهيئة المتغيرات
        userPreferences = loadUserPreferences();
        currentConversationId = generateConversationId();

        // تحديث مؤشر مستوى الخبرة
        updateExpertiseIndicator();

        // تحميل الاقتراحات السريعة
        loadQuickSuggestions();

        // Focus on message input
        document.getElementById('messageInput').focus();

        // Handle Enter key
        document.getElementById('messageInput').addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage(e);
            }
        });

        // إضافة مستمع حدث لتغيير مستوى الخبرة
        const expertiseLevelSelect = document.getElementById('expertiseLevel');
        if (expertiseLevelSelect) {
            expertiseLevelSelect.addEventListener('change', function () {
                // تحديث تفضيلات المستخدم
                userPreferences.expertiseLevel = this.value;
                localStorage.setItem('userPreferences', JSON.stringify(userPreferences));

                // تحديث مؤشر مستوى الخبرة
                updateExpertiseIndicator();

                // إعادة تحميل الاقتراحات السريعة
                loadQuickSuggestions();
            });
        }
    });

    function updateExpertiseIndicator() {
        // تحديث مؤشر مستوى الخبرة في واجهة المستخدم
        const expertiseIndicators = document.querySelectorAll('.expertise-indicator-js');
        const expertiseLevel = userPreferences.expertiseLevel || 'beginner';

        // تعيين النص المناسب بناءً على مستوى الخبرة
        let expertiseText = 'مبتدئ';
        let expertiseClass = 'expertise-beginner';

        switch (expertiseLevel) {
            case 'beginner':
                expertiseText = 'مبتدئ';
                expertiseClass = 'expertise-beginner';
                break;
            case 'intermediate':
                expertiseText = 'متوسط';
                expertiseClass = 'expertise-intermediate';
                break;
            case 'advanced':
                expertiseText = 'متقدم';
                expertiseClass = 'expertise-advanced';
                break;
            case 'expert':
                expertiseText = 'خبير';
                expertiseClass = 'expertise-expert';
                break;
        }

        // تحديث جميع مؤشرات الخبرة في الصفحة
        expertiseIndicators.forEach(indicator => {
            indicator.textContent = expertiseText;

            // إزالة جميع فئات الخبرة السابقة
            indicator.classList.remove(
                'expertise-beginner',
                'expertise-intermediate',
                'expertise-advanced',
                'expertise-expert'
            );

            // إضافة فئة الخبرة الجديدة
            indicator.classList.add(expertiseClass);
        });
    }

    function showToast(message, type = 'success') {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        // إنشاء محتوى التنبيه
        const toastBody = document.createElement('div');
        toastBody.className = 'toast-body d-flex align-items-center';

        // إضافة أيقونة مناسبة
        let icon = 'check-circle';
        if (type === 'danger') icon = 'exclamation-triangle';
        if (type === 'info') icon = 'info-circle';
        if (type === 'warning') icon = 'exclamation-circle';

        toastBody.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;

        // إضافة زر الإغلاق
        const closeButton = document.createElement('button');
        closeButton.type = 'button';
        closeButton.className = 'btn-close btn-close-white ms-auto';
        closeButton.setAttribute('data-bs-dismiss', 'toast');
        closeButton.setAttribute('aria-label', 'Close');

        toastBody.appendChild(closeButton);
        toast.appendChild(toastBody);

        // إضافة التنبيه إلى الصفحة
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            // إنشاء حاوية التنبيهات إذا لم تكن موجودة
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '1050';
            document.body.appendChild(container);
            container.appendChild(toast);
        } else {
            toastContainer.appendChild(toast);
        }

        // تهيئة وعرض التنبيه
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
    }
</script>
{% endblock %}