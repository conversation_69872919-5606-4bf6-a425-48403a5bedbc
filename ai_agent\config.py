#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات الوكيل الذكي المحسن
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class AIAgentConfig:
    """إعدادات الوكيل الذكي"""
    
    # إعدادات عامة
    AGENT_NAME = "SmartBiz AI Assistant"
    AGENT_VERSION = "2.0.0"
    LANGUAGE = "ar"
    
    # إعدادات معالجة اللغة الطبيعية
    NLP_CONFIG = {
        'max_tokens': 500,
        'confidence_threshold': 0.7,
        'enable_stemming': True,
        'enable_keyword_extraction': True
    }
    
    # إعدادات التعلم الآلي المحسن
    ML_CONFIG = {
        'sales_prediction': {
            'model_type': 'ensemble',  # استخدام نماذج متعددة
            'models': ['linear_regression', 'random_forest', 'gradient_boosting'],
            'features': ['date', 'amount', 'client_type', 'seasonality', 'trend'],
            'lookback_days': 365,  # زيادة فترة التحليل
            'forecast_days': 90,   # توقعات أطول
            'auto_retrain': True,  # إعادة تدريب تلقائية
            'confidence_intervals': True
        },
        'client_segmentation': {
            'model_type': 'advanced_clustering',
            'algorithms': ['kmeans', 'dbscan', 'hierarchical'],
            'n_clusters': 'auto',  # تحديد تلقائي للعدد الأمثل
            'features': ['total_spent', 'frequency', 'recency', 'avg_order_value', 'payment_behavior'],
            'update_frequency': 'daily',
            'incremental_learning': True
        },
        'anomaly_detection': {
            'model_type': 'ensemble_anomaly',
            'algorithms': ['isolation_forest', 'one_class_svm', 'local_outlier_factor'],
            'contamination': 'auto',
            'features': ['amount', 'time', 'client_id', 'transaction_pattern'],
            'real_time_detection': True
        },
        'learning_system': {
            'incremental_learning': True,
            'memory_retention': 0.95,  # الاحتفاظ بـ 95% من المعرفة السابقة
            'adaptation_rate': 0.1,    # معدل التكيف مع البيانات الجديدة
            'knowledge_decay': 0.01,   # تدهور المعرفة القديمة
            'auto_model_selection': True,
            'performance_monitoring': True
        }
    }
    
    # إعدادات قاعدة المعرفة
    KNOWLEDGE_BASE = {
        'accounting_principles': {
            'accrual_principle': {
                'ar': 'مبدأ الاستحقاق',
                'definition': 'تسجيل المعاملات عند حدوثها وليس عند تحصيل النقد',
                'examples': [
                    'تسجيل المبيعات عند التسليم وليس عند الدفع',
                    'تسجيل المصروفات عند تكبدها وليس عند السداد'
                ],
                'practical_application': 'يساعد في إظهار الأداء الحقيقي للشركة',
                'keywords': ['استحقاق', 'تسجيل', 'معاملات', 'حدوث']
            },
            'matching_principle': {
                'ar': 'مبدأ المطابقة',
                'definition': 'مطابقة الإيرادات مع المصروفات في نفس الفترة',
                'examples': [
                    'ربط تكلفة البضاعة المباعة مع إيرادات المبيعات',
                    'توزيع مصروفات الإعلان على فترات الاستفادة'
                ],
                'practical_application': 'يضمن قياس دقيق للربحية في كل فترة',
                'keywords': ['مطابقة', 'إيرادات', 'مصروفات', 'فترة']
            },
            'conservatism_principle': {
                'ar': 'مبدأ الحيطة والحذر',
                'definition': 'عدم المبالغة في تقدير الأصول والإيرادات',
                'examples': [
                    'تقدير الديون المشكوك في تحصيلها',
                    'تقييم المخزون بالتكلفة أو السوق أيهما أقل'
                ],
                'practical_application': 'يحمي المستثمرين من المعلومات المضللة',
                'keywords': ['حيطة', 'حذر', 'تقدير', 'أصول']
            },
            'consistency_principle': {
                'ar': 'مبدأ الثبات',
                'definition': 'استخدام نفس الطرق المحاسبية من فترة لأخرى',
                'examples': [
                    'استخدام نفس طريقة الإهلاك كل سنة',
                    'تطبيق نفس طريقة تقييم المخزون'
                ],
                'practical_application': 'يسمح بمقارنة الأداء عبر الفترات',
                'keywords': ['ثبات', 'طرق', 'محاسبية', 'مقارنة']
            },
            'materiality_principle': {
                'ar': 'مبدأ الأهمية النسبية',
                'definition': 'التركيز على البنود ذات التأثير الجوهري على القرارات',
                'examples': [
                    'عدم رسملة المشتريات الصغيرة',
                    'الإفصاح عن الأخطاء الجوهرية فقط'
                ],
                'practical_application': 'يوفر الوقت والجهد في المعالجة المحاسبية',
                'keywords': ['أهمية', 'نسبية', 'جوهري', 'تأثير']
            }
        },
        'financial_statements': {
            'income_statement': {
                'ar': 'قائمة الدخل',
                'definition': 'تظهر الإيرادات والمصروفات والأرباح لفترة معينة',
                'components': ['الإيرادات', 'تكلفة البضاعة المباعة', 'المصروفات التشغيلية', 'صافي الربح']
            },
            'balance_sheet': {
                'ar': 'الميزانية العمومية',
                'definition': 'تظهر الأصول والخصوم وحقوق الملكية في تاريخ معين',
                'components': ['الأصول المتداولة', 'الأصول الثابتة', 'الخصوم المتداولة', 'حقوق الملكية']
            },
            'cash_flow': {
                'ar': 'قائمة التدفقات النقدية',
                'definition': 'تظهر التدفقات النقدية الداخلة والخارجة',
                'components': ['الأنشطة التشغيلية', 'الأنشطة الاستثمارية', 'الأنشطة التمويلية']
            }
        },
        'financial_ratios': {
            'liquidity_ratios': {
                'current_ratio': 'الأصول المتداولة / الخصوم المتداولة',
                'quick_ratio': '(الأصول المتداولة - المخزون) / الخصوم المتداولة',
                'cash_ratio': 'النقد / الخصوم المتداولة'
            },
            'profitability_ratios': {
                'gross_margin': '(الإيرادات - تكلفة البضاعة) / الإيرادات',
                'net_margin': 'صافي الربح / الإيرادات',
                'roe': 'صافي الربح / حقوق الملكية'
            },
            'efficiency_ratios': {
                'inventory_turnover': 'تكلفة البضاعة المباعة / متوسط المخزون',
                'receivables_turnover': 'المبيعات الآجلة / متوسط الذمم المدينة',
                'asset_turnover': 'الإيرادات / متوسط الأصول'
            }
        }
    }
    
    # إعدادات الردود الذكية المحسنة
    RESPONSE_CONFIG = {
        'greeting_responses': [
            "مرحباً بك في SmartBiz! 🤖 أنا مساعدك الذكي المحسن. كيف يمكنني مساعدتك اليوم؟",
            "أهلاً وسهلاً! 👋 أنا مساعدك الذكي للمحاسبة مع قدرات تعلم متقدمة. ما الذي تحتاج إليه؟",
            "السلام عليكم! 🌟 أنا هنا لمساعدتك في جميع أمورك المحاسبية بذكاء اصطناعي محسن.",
            "صباح الخير! ☀️ كيف يمكنني أن أساعدك في إدارة أعمالك اليوم بخبرتي المحاسبية المتطورة؟"
        ],
        'contextual_responses': {
            'first_time_user': [
                "مرحباً! يبدو أنك جديد هنا. دعني أعرفك على إمكانياتي المحاسبية المتقدمة.",
                "أهلاً بك! أنا مساعد ذكي متخصص في المحاسبة. يمكنني تعلم تفضيلاتك وتقديم مساعدة مخصصة."
            ],
            'returning_user': [
                "مرحباً بعودتك! لاحظت أنك تهتم بـ {favorite_topic}. كيف يمكنني مساعدتك اليوم؟",
                "أهلاً مرة أخرى! بناءً على تفاعلاتنا السابقة، أعتقد أنك قد تحتاج إلى {suggestion}."
            ],
            'expert_user': [
                "مرحباً! أرى أنك خبير في المحاسبة. يمكنني تقديم تحليلات متقدمة ورؤى عميقة.",
                "أهلاً بالخبير! دعني أقدم لك أحدث التحليلات والتوقعات المالية."
            ]
        },
        'fallback_responses': [
            "أعتذر، لم أتمكن من فهم طلبك بوضوح. 🤔 يمكنك إعادة صياغة السؤال أو طلب المساعدة.",
            "يبدو أن سؤالك معقد. 💭 يمكنني مساعدتك في الأمور المحاسبية الأساسية مثل الفواتير والعملاء والتقارير.",
            "لم أتمكن من معالجة طلبك حالياً. 🔄 جرب أسئلة مثل: 'اظهر الفواتير' أو 'احسب الأرباح' أو 'حلل المبيعات'.",
            "أحتاج إلى مزيد من التوضيح. 📝 يمكنك سؤالي عن الفواتير، العملاء، المشاريع، أو المبادئ المحاسبية."
        ],
        'error_responses': [
            "عذراً، حدث خطأ تقني. ⚠️ يرجى المحاولة مرة أخرى.",
            "أواجه صعوبة في الوصول إلى البيانات حالياً. 🔄 يرجى المحاولة لاحقاً.",
            "حدث خطأ أثناء معالجة طلبك. 🛠️ يرجى التحقق من صحة البيانات."
        ],
        'learning_responses': [
            "شكراً لك! تعلمت شيئاً جديداً من تفاعلنا. 🧠",
            "ممتاز! سأتذكر تفضيلك هذا للمرات القادمة. 💡",
            "رائع! أضفت هذه المعلومة إلى قاعدة معرفتي. 📚"
        ],
        'confidence_levels': {
            'high': "أنا واثق من هذه الإجابة بنسبة عالية. ✅",
            'medium': "هذه إجابة جيدة، لكن قد تحتاج إلى تأكيد إضافي. ⚡",
            'low': "هذه محاولة للإجابة، لكن أنصح بالتحقق من مصادر أخرى. ⚠️"
        }
    }
    
    # إعدادات الاقتراحات الذكية
    SUGGESTIONS_CONFIG = {
        'greeting': [
            "اظهر لي ملخص الأعمال",
            "احسب إجمالي الأرباح",
            "حلل اتجاه المبيعات",
            "ما هي الفواتير المتأخرة؟"
        ],
        'accounting_question': [
            "اشرح لي المزيد عن هذا المفهوم",
            "أعطني مثال عملي",
            "ما هي المبادئ المحاسبية الأساسية؟",
            "كيف أطبق هذا في عملي؟"
        ],
        'data_query': [
            "اظهر تفاصيل أكثر",
            "قارن مع الشهر الماضي",
            "صدر هذه البيانات",
            "حلل هذه البيانات"
        ],
        'calculation': [
            "احسب النسب المالية",
            "قارن مع الفترة السابقة",
            "اظهر التفاصيل",
            "احسب معدل النمو"
        ],
        'analysis_request': [
            "اظهر توقعات المستقبل",
            "قدم توصيات للتحسين",
            "قارن مع المعايير الصناعية",
            "حلل المخاطر"
        ]
    }
    
    # إعدادات التحليلات المتقدمة
    ANALYTICS_CONFIG = {
        'sales_analysis': {
            'trend_periods': [7, 30, 90, 365],  # أيام
            'growth_thresholds': {
                'excellent': 20,  # %
                'good': 10,
                'average': 5,
                'poor': 0
            }
        },
        'client_analysis': {
            'segmentation_criteria': {
                'high_value': {'min_spent': 10000, 'min_frequency': 10},
                'medium_value': {'min_spent': 5000, 'min_frequency': 5},
                'low_value': {'min_spent': 1000, 'min_frequency': 1}
            }
        },
        'financial_health': {
            'ratios_benchmarks': {
                'current_ratio': {'excellent': 2.0, 'good': 1.5, 'poor': 1.0},
                'profit_margin': {'excellent': 0.2, 'good': 0.1, 'poor': 0.05}
            }
        }
    }
    
    # إعدادات التكامل الخارجي
    EXTERNAL_CONFIG = {
        'openai': {
            'api_key': os.environ.get('OPENAI_API_KEY'),
            'model': 'gpt-3.5-turbo',
            'max_tokens': 500,
            'temperature': 0.7
        },
        'redis': {
            'host': os.environ.get('REDIS_HOST', 'localhost'),
            'port': int(os.environ.get('REDIS_PORT', 6379)),
            'db': int(os.environ.get('REDIS_DB', 0))
        }
    }
    
    @classmethod
    def get_config(cls, section: Optional[str] = None) -> Dict[str, Any]:
        """الحصول على إعدادات معينة"""
        if section:
            return getattr(cls, section.upper() + '_CONFIG', {})
        return {
            'nlp': cls.NLP_CONFIG,
            'ml': cls.ML_CONFIG,
            'knowledge_base': cls.KNOWLEDGE_BASE,
            'response': cls.RESPONSE_CONFIG,
            'suggestions': cls.SUGGESTIONS_CONFIG,
            'analytics': cls.ANALYTICS_CONFIG,
            'external': cls.EXTERNAL_CONFIG
        }
    
    # إعدادات التعلم المتقدم والذاكرة
    ADVANCED_LEARNING_CONFIG = {
        'memory_system': {
            'short_term_memory': {
                'capacity': 100,  # عدد التفاعلات المحفوظة
                'retention_time': 3600,  # ثانية (ساعة واحدة)
                'decay_rate': 0.1
            },
            'long_term_memory': {
                'capacity': 10000,
                'retention_time': 2592000,  # ثانية (شهر واحد)
                'consolidation_threshold': 5  # عدد التكرارات للانتقال للذاكرة طويلة المدى
            },
            'episodic_memory': {
                'store_conversations': True,
                'max_conversations': 1000,
                'context_window': 10  # عدد الرسائل السابقة للسياق
            }
        },
        'learning_algorithms': {
            'reinforcement_learning': {
                'enabled': True,
                'learning_rate': 0.01,
                'discount_factor': 0.95,
                'exploration_rate': 0.1
            },
            'transfer_learning': {
                'enabled': True,
                'similarity_threshold': 0.8,
                'adaptation_rate': 0.05
            },
            'meta_learning': {
                'enabled': True,
                'few_shot_examples': 5,
                'adaptation_steps': 3
            }
        },
        'knowledge_acquisition': {
            'auto_extraction': True,
            'confidence_threshold': 0.7,
            'validation_required': True,
            'sources': ['user_interactions', 'external_apis', 'documents']
        },
        'personalization': {
            'user_profiling': True,
            'preference_learning': True,
            'adaptive_responses': True,
            'expertise_detection': True
        }
    }

    # إعدادات تحسين الأداء
    PERFORMANCE_CONFIG = {
        'caching': {
            'enabled': True,
            'ttl': 3600,  # ثانية
            'max_size': 1000,
            'strategy': 'lru'  # Least Recently Used
        },
        'parallel_processing': {
            'enabled': True,
            'max_workers': 4,
            'timeout': 30
        },
        'model_optimization': {
            'auto_pruning': True,
            'quantization': False,
            'batch_processing': True
        }
    }

    @classmethod
    def update_config(cls, section: str, updates: Dict[str, Any]):
        """تحديث الإعدادات"""
        config_attr = section.upper() + '_CONFIG'
        if hasattr(cls, config_attr):
            current_config = getattr(cls, config_attr)
            current_config.update(updates)
            setattr(cls, config_attr, current_config)

    @classmethod
    def get_learning_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات التعلم المتقدم"""
        return cls.ADVANCED_LEARNING_CONFIG

    @classmethod
    def get_performance_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات الأداء"""
        return cls.PERFORMANCE_CONFIG

# متغير للتوافق مع الكود القديم
ACCOUNTING_KNOWLEDGE = AIAgentConfig.KNOWLEDGE_BASE
