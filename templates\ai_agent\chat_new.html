{% extends "base.html" %}

{% block title %}SmartBiz AI Agent - المساعد الذكي{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --light-bg: #f8fafc;
        --dark-bg: #1e293b;
        --border-color: #e2e8f0;
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
    
    .chat-container {
        height: calc(100vh - 120px);
        max-width: 1000px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: var(--shadow-lg);
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .chat-header {
        background: linear-gradient(135deg, var(--primary-color), #3b82f6);
        color: white;
        padding: 25px;
        text-align: center;
        position: relative;
    }
    
    .chat-header h1 {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }
    
    .chat-header .subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin-top: 8px;
    }
    
    .chat-messages {
        flex: 1;
        padding: 25px;
        overflow-y: auto;
        background: var(--light-bg);
        scrollbar-width: thin;
        scrollbar-color: var(--border-color) transparent;
    }
    
    .chat-messages::-webkit-scrollbar {
        width: 6px;
    }
    
    .chat-messages::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .chat-messages::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 3px;
    }
    
    .message {
        margin-bottom: 20px;
        padding: 16px 20px;
        border-radius: 20px;
        max-width: 85%;
        word-wrap: break-word;
        line-height: 1.6;
        position: relative;
        animation: fadeInUp 0.3s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .user-message {
        background: linear-gradient(135deg, var(--primary-color), #3b82f6);
        color: white;
        margin-left: auto;
        text-align: right;
        box-shadow: var(--shadow);
    }
    
    .ai-message {
        background: white;
        color: #374151;
        border: 1px solid var(--border-color);
        margin-right: auto;
        box-shadow: var(--shadow);
    }
    
    .ai-message strong {
        color: var(--primary-color);
    }
    
    .typing-indicator {
        display: none;
        padding: 15px 20px;
        font-style: italic;
        color: var(--secondary-color);
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 20px;
        margin-right: auto;
        max-width: 200px;
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1; }
    }
    
    .suggestions {
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .suggestion-btn {
        font-size: 0.85rem;
        padding: 8px 15px;
        border-radius: 20px;
        border: 1px solid var(--border-color);
        background: white;
        color: var(--secondary-color);
        transition: all 0.2s ease;
        cursor: pointer;
    }
    
    .suggestion-btn:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-1px);
    }
    
    .chat-input {
        padding: 25px;
        background: white;
        border-top: 1px solid var(--border-color);
    }
    
    .input-group {
        position: relative;
    }
    
    .form-control {
        border-radius: 25px;
        border: 2px solid var(--border-color);
        padding: 15px 60px 15px 20px;
        font-size: 1rem;
        transition: all 0.2s ease;
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .btn-send {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--primary-color);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.2s ease;
    }
    
    .btn-send:hover {
        background: #1d4ed8;
        transform: translateY(-50%) scale(1.05);
    }
    
    .btn-send:disabled {
        background: var(--secondary-color);
        cursor: not-allowed;
        transform: translateY(-50%) scale(1);
    }
    
    .welcome-message {
        text-align: center;
        padding: 40px 20px;
        color: var(--secondary-color);
    }
    
    .welcome-message h3 {
        color: var(--primary-color);
        margin-bottom: 15px;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    
    .quick-action {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .quick-action:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }
    
    .quick-action i {
        font-size: 2rem;
        color: var(--primary-color);
        margin-bottom: 10px;
    }
    
    .quick-action h5 {
        color: var(--primary-color);
        margin-bottom: 5px;
    }
    
    .quick-action p {
        color: var(--secondary-color);
        font-size: 0.9rem;
        margin: 0;
    }
    
    @media (max-width: 768px) {
        .chat-container {
            height: calc(100vh - 80px);
            border-radius: 0;
            margin: 0;
        }
        
        .chat-header, .chat-input {
            padding: 20px;
        }
        
        .chat-messages {
            padding: 20px;
        }
        
        .message {
            max-width: 95%;
            padding: 12px 16px;
        }
        
        .chat-header h1 {
            font-size: 1.5rem;
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="chat-container">
        <!-- Chat Header -->
        <div class="chat-header">
            <h1>
                <i class="fas fa-robot"></i>
                SmartBiz AI Agent
            </h1>
            <div class="subtitle">مساعدك الذكي لإدارة الأعمال والمحاسبة</div>
        </div>
        
        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h3>مرحباً بك في SmartBiz AI Agent!</h3>
                <p>أنا مساعدك الذكي لإدارة الأعمال. يمكنني مساعدتك في جميع احتياجاتك المحاسبية والإدارية.</p>
                
                <div class="quick-actions">
                    <div class="quick-action" onclick="sendQuickMessage('اظهر لي ملخص الأعمال')">
                        <i class="fas fa-chart-line"></i>
                        <h5>ملخص الأعمال</h5>
                        <p>عرض ملخص شامل لحالة أعمالك</p>
                    </div>
                    
                    <div class="quick-action" onclick="sendQuickMessage('احسب إجمالي الأرباح')">
                        <i class="fas fa-calculator"></i>
                        <h5>حساب الأرباح</h5>
                        <p>حساب الأرباح والخسائر</p>
                    </div>
                    
                    <div class="quick-action" onclick="sendQuickMessage('حلل اتجاه المبيعات')">
                        <i class="fas fa-trending-up"></i>
                        <h5>تحليل المبيعات</h5>
                        <p>تحليل اتجاهات المبيعات</p>
                    </div>
                    
                    <div class="quick-action" onclick="sendQuickMessage('مساعدة')">
                        <i class="fas fa-question-circle"></i>
                        <h5>المساعدة</h5>
                        <p>تعرف على ما يمكنني فعله</p>
                    </div>
                </div>
            </div>
            
            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typingIndicator">
                <i class="fas fa-robot me-2"></i>
                جاري الكتابة...
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="chat-input">
            <form id="chatForm">
                <div class="input-group">
                    <input type="text" 
                           class="form-control" 
                           id="messageInput" 
                           placeholder="اكتب رسالتك هنا..."
                           autocomplete="off">
                    <button type="submit" class="btn-send" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
