{% extends "base.html" %}

{% block title %}تعديل {{ client.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تعديل {{ client.name }}</h1>
            <p class="text-muted">تحديث بيانات العميل</p>
        </div>
        <div>
            <a href="{{ url_for('clients.view_client', client_id=client.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى العميل
            </a>
        </div>
    </div>

    <form method="POST" enctype="multipart/form-data">
        <div class="row">
            <!-- Main Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ client.name }}"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span
                                        class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email"
                                    value="{{ client.email }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                    value="{{ client.phone or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name"
                                    value="{{ client.company_name or '' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>معلومات العنوان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                    placeholder="العنوان التفصيلي">{{ client.address or '' }}</textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city"
                                    value="{{ client.city or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">الدولة</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="">اختر الدولة</option>
                                    <option value="SA" {% if client.country=='SA' %}selected{% endif %}>المملكة العربية
                                        السعودية</option>
                                    <option value="AE" {% if client.country=='AE' %}selected{% endif %}>الإمارات العربية
                                        المتحدة</option>
                                    <option value="KW" {% if client.country=='KW' %}selected{% endif %}>الكويت</option>
                                    <option value="QA" {% if client.country=='QA' %}selected{% endif %}>قطر</option>
                                    <option value="BH" {% if client.country=='BH' %}selected{% endif %}>البحرين</option>
                                    <option value="OM" {% if client.country=='OM' %}selected{% endif %}>عمان</option>
                                    <option value="JO" {% if client.country=='JO' %}selected{% endif %}>الأردن</option>
                                    <option value="LB" {% if client.country=='LB' %}selected{% endif %}>لبنان</option>
                                    <option value="EG" {% if client.country=='EG' %}selected{% endif %}>مصر</option>
                                    <option value="MA" {% if client.country=='MA' %}selected{% endif %}>المغرب</option>
                                    <option value="TN" {% if client.country=='TN' %}selected{% endif %}>تونس</option>
                                    <option value="DZ" {% if client.country=='DZ' %}selected{% endif %}>الجزائر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>طريقة الدفع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع المفضلة</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash" {% if client.payment_method=='cash' %}selected{% endif %}>نقدي
                                    </option>
                                    <option value="bank_transfer" {% if client.payment_method=='bank_transfer'
                                        %}selected{% endif %}>تحويل بنكي</option>
                                    <option value="card" {% if client.payment_method=='card' %}selected{% endif %}>بطاقة
                                        ائتمان</option>
                                    <option value="digital_wallet" {% if client.payment_method=='digital_wallet'
                                        %}selected{% endif %}>محفظة رقمية</option>
                                    <option value="check" {% if client.payment_method=='check' %}selected{% endif %}>شيك
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number"
                                    value="{{ client.tax_number if client.tax_number else '' }}">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                    placeholder="ملاحظات إضافية عن العميل">{{ client.notes if client.notes else '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Profile Image -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-image me-2"></i>صورة العميل
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div id="imagePreview" class="mb-3">
                                {% if client.profile_image %}
                                <img src="{{ client.profile_image }}" class="rounded-circle"
                                    style="width: 120px; height: 120px; object-fit: cover;" alt="{{ client.name }}">
                                {% else %}
                                <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center text-white"
                                    style="width: 120px; height: 120px;">
                                    <span class="fs-1 fw-bold">{{ client.name[0].upper() }}</span>
                                </div>
                                {% endif %}
                            </div>
                            <input type="file" class="form-control" id="profile_image" name="profile_image"
                                accept="image/*" onchange="previewImage(this)">
                            <small class="form-text text-muted">اختر صورة جديدة للعميل (اختياري)</small>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-toggle-on me-2"></i>الحالة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if
                                client.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                عميل نشط
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            العملاء النشطون يظهرون في قوائم الاختيار عند إنشاء الفواتير
                        </small>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <a href="{{ url_for('clients.view_client', client_id=client.id) }}"
                                class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="button" class="btn btn-danger"
                                onclick="confirmDelete({{ client.id }}, '{{ client.name }}')">
                                <i class="fas fa-trash me-2"></i>حذف العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف العميل <strong id="clientName"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: سيتم حذف جميع الفواتير والمعاملات المرتبطة بهذا العميل.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function previewImage(input) {
        const preview = document.getElementById('imagePreview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function (e) {
                preview.innerHTML = `
                <img src="${e.target.result}" class="rounded-circle" 
                     style="width: 120px; height: 120px; object-fit: cover;" 
                     alt="معاينة الصورة">
            `;
            };

            reader.readAsDataURL(input.files[0]);
        }
    }

    function confirmDelete(clientId, clientName) {
        document.getElementById('clientName').textContent = clientName;
        document.getElementById('deleteForm').action = `/clients/${clientId}/delete`;

        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Form validation
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector('form');
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');

        form.addEventListener('submit', function (e) {
            let isValid = true;

            // Validate name
            if (!nameInput.value.trim()) {
                isValid = false;
                nameInput.classList.add('is-invalid');
            } else {
                nameInput.classList.remove('is-invalid');
            }

            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailInput.value.trim() || !emailRegex.test(emailInput.value)) {
                isValid = false;
                emailInput.classList.add('is-invalid');
            } else {
                emailInput.classList.remove('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                alert('يرجى تصحيح الأخطاء في النموذج');
            }
        });
    });
</script>
{% endblock %}