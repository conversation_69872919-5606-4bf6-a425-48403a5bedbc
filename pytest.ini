# SmartBiz Accounting - نظام المحاسبة الذكي
# تكوين pytest للاختبارات

[tool:pytest]
# مجلدات الاختبارات
testpaths = tests

# أنماط ملفات الاختبار
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# إعدادات الإخراج
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --junitxml=junit.xml

# العلامات المخصصة
markers =
    unit: اختبارات الوحدة
    integration: اختبارات التكامل
    functional: اختبارات وظيفية
    slow: اختبارات بطيئة
    fast: اختبارات سريعة
    api: اختبارات API
    database: اختبارات قاعدة البيانات
    auth: اختبارات المصادقة
    ai: اختبارات الذكاء الاصطناعي
    security: اختبارات الأمان
    performance: اختبارات الأداء
    smoke: اختبارات الدخان
    regression: اختبارات الانحدار
    critical: اختبارات حرجة
    external: اختبارات تتطلب خدمات خارجية

# تصفية التحذيرات
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::ImportWarning
    ignore::ResourceWarning

# متغيرات البيئة للاختبارات
env =
    TESTING = True
    FLASK_ENV = testing
    DATABASE_URL = sqlite:///:memory:
    SECRET_KEY = test-secret-key
    WTF_CSRF_ENABLED = False

# إعدادات التغطية
[coverage:run]
source = .
omit = 
    */venv/*
    */env/*
    */tests/*
    */migrations/*
    */node_modules/*
    */static/*
    */htmlcov/*
    setup.py
    conftest.py
    */conftest.py
    */__pycache__/*
    */.*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
