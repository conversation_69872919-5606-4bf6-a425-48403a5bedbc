<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2c3e50" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3498db" stop-opacity="0.8" />
    </linearGradient>
    
    <!-- Pattern Definition -->
    <pattern id="pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M0,30 L60,30" stroke="#ffffff" stroke-width="0.5" stroke-opacity="0.1" />
      <path d="M30,0 L30,60" stroke="#ffffff" stroke-width="0.5" stroke-opacity="0.1" />
    </pattern>
  </defs>
  
  <!-- Main Background -->
  <rect width="100%" height="100%" fill="url(#bg-gradient)" />
  
  <!-- Pattern Overlay -->
  <rect width="100%" height="100%" fill="url(#pattern)" />
  
  <!-- Decorative Elements -->
  <!-- Abstract Financial Graph -->
  <path d="M0,800 Q480,700 960,750 T1920,650" stroke="#ffffff" stroke-width="3" stroke-opacity="0.2" fill="none" />
  <path d="M0,850 Q480,750 960,800 T1920,700" stroke="#ffffff" stroke-width="2" stroke-opacity="0.15" fill="none" />
  <path d="M0,900 Q480,800 960,850 T1920,750" stroke="#ffffff" stroke-width="1" stroke-opacity="0.1" fill="none" />
  
  <!-- Floating Circles -->
  <g opacity="0.1">
    <!-- Large Circles -->
    <circle cx="200" cy="200" r="100" fill="#ffffff" />
    <circle cx="1720" cy="880" r="120" fill="#ffffff" />
    
    <!-- Medium Circles -->
    <circle cx="400" cy="700" r="60" fill="#ffffff" />
    <circle cx="1200" cy="300" r="70" fill="#ffffff" />
    <circle cx="1600" cy="500" r="50" fill="#ffffff" />
    
    <!-- Small Circles -->
    <circle cx="300" cy="400" r="20" fill="#ffffff" />
    <circle cx="800" cy="200" r="25" fill="#ffffff" />
    <circle cx="1100" cy="600" r="15" fill="#ffffff" />
    <circle cx="1400" cy="800" r="30" fill="#ffffff" />
    <circle cx="600" cy="900" r="35" fill="#ffffff" />
    <circle cx="900" cy="700" r="40" fill="#ffffff" />
    <circle cx="1500" cy="200" r="25" fill="#ffffff" />
    <circle cx="100" cy="600" r="30" fill="#ffffff" />
  </g>
  
  <!-- Abstract Financial Icons -->
  <g opacity="0.15">
    <!-- Calculator Icon -->
    <rect x="100" y="100" width="60" height="80" rx="5" ry="5" fill="#ffffff" />
    <rect x="110" y="110" width="40" height="15" fill="#3498db" />
    <circle cx="115" cy="135" r="5" fill="#3498db" />
    <circle cx="130" cy="135" r="5" fill="#3498db" />
    <circle cx="145" cy="135" r="5" fill="#3498db" />
    <circle cx="115" cy="150" r="5" fill="#3498db" />
    <circle cx="130" cy="150" r="5" fill="#3498db" />
    <circle cx="145" cy="150" r="5" fill="#3498db" />
    <circle cx="115" cy="165" r="5" fill="#3498db" />
    <circle cx="130" cy="165" r="5" fill="#3498db" />
    <circle cx="145" cy="165" r="5" fill="#3498db" />
    
    <!-- Chart Icon -->
    <rect x="1760" y="100" width="10" height="40" fill="#ffffff" />
    <rect x="1780" y="80" width="10" height="60" fill="#ffffff" />
    <rect x="1800" y="120" width="10" height="20" fill="#ffffff" />
    <rect x="1820" y="70" width="10" height="70" fill="#ffffff" />
    <rect x="1840" y="90" width="10" height="50" fill="#ffffff" />
    <line x1="1750" y1="140" x2="1850" y2="140" stroke="#ffffff" stroke-width="2" />
    
    <!-- Document Icon -->
    <path d="M100,900 L140,900 L140,980 L100,980 Z" fill="#ffffff" />
    <line x1="110" y1="920" x2="130" y2="920" stroke="#3498db" stroke-width="2" />
    <line x1="110" y1="930" x2="130" y2="930" stroke="#3498db" stroke-width="2" />
    <line x1="110" y1="940" x2="130" y2="940" stroke="#3498db" stroke-width="2" />
    <line x1="110" y1="950" x2="130" y2="950" stroke="#3498db" stroke-width="2" />
    <line x1="110" y1="960" x2="130" y2="960" stroke="#3498db" stroke-width="2" />
    
    <!-- Coin Icon -->
    <circle cx="1800" cy="900" r="30" fill="#ffffff" />
    <text x="1800" y="910" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#3498db">$</text>
  </g>
  
  <!-- Subtle Grid Lines -->
  <g opacity="0.05">
    <!-- Vertical Lines -->
    <line x1="480" y1="0" x2="480" y2="1080" stroke="#ffffff" stroke-width="1" />
    <line x1="960" y1="0" x2="960" y2="1080" stroke="#ffffff" stroke-width="1" />
    <line x1="1440" y1="0" x2="1440" y2="1080" stroke="#ffffff" stroke-width="1" />
    
    <!-- Horizontal Lines -->
    <line x1="0" y1="270" x2="1920" y2="270" stroke="#ffffff" stroke-width="1" />
    <line x1="0" y1="540" x2="1920" y2="540" stroke="#ffffff" stroke-width="1" />
    <line x1="0" y1="810" x2="1920" y2="810" stroke="#ffffff" stroke-width="1" />
  </g>
</svg>