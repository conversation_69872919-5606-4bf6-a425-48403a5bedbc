#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
نموذج استعلام الذكاء الاصطناعي
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app import db

class AIQuery(db.Model):
    """
نموذج لتخزين استعلامات المستخدم وتفاعلاته مع وكيل الذكاء الاصطناعي
    """
    __tablename__ = 'ai_queries'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50), nullable=False, default='unknown')
    response = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    feedback = Column(Integer, nullable=True)  # تقييم المستخدم للإجابة (1-5)
    feedback_text = Column(Text, nullable=True)  # تعليق المستخدم على الإجابة
    
    # العلاقات
    user = relationship('User', back_populates='ai_queries')
    
    def __init__(self, user_id, query_text, query_type='unknown', response=None, timestamp=None):
        self.user_id = user_id
        self.query_text = query_text
        self.query_type = query_type
        self.response = response
        self.timestamp = timestamp or datetime.utcnow()
    
    def add_feedback(self, rating, feedback_text=None):
        """
إضافة تقييم المستخدم للإجابة
        """
        if 1 <= rating <= 5:
            self.feedback = rating
            self.feedback_text = feedback_text
            return True
        return False
    
    def to_dict(self):
        """
تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'query_text': self.query_text,
            'query_type': self.query_type,
            'response': self.response,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'feedback': self.feedback,
            'feedback_text': self.feedback_text
        }