#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معالج اللغة الطبيعية المتقدم للوكيل الذكي
Advanced Natural Language Processor for Enhanced AI Agent
"""

import re
import logging
from typing import Dict, List, Any, Optional
from collections import Counter, defaultdict

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# استيراد المكتبات الاختيارية
try:
    import numpy as np  # noqa: F401
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    np = None

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity  # noqa: F401
    from sklearn.cluster import KMeans  # noqa: F401
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    TfidfVectorizer = None
    cosine_similarity = None
    KMeans = None

class EnhancedArabicNLP:
    """معالج متقدم للغة العربية مع قدرات تعلم محسنة"""
    
    def __init__(self):
        """تهيئة المعالج المتقدم"""
        
        # كلمات الإيقاف العربية الموسعة
        self.stop_words = {
            # ضمائر
            'أنا', 'أنت', 'أنتم', 'أنتن', 'هو', 'هي', 'هم', 'هن', 'نحن',
            'إياي', 'إياك', 'إياه', 'إياها', 'إيانا', 'إياكم', 'إياكن', 'إياهم', 'إياهن',
            
            # حروف الجر
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بين', 'تحت', 'فوق', 'أمام', 'خلف',
            'عند', 'لدى', 'حول', 'دون', 'سوى', 'خلال', 'أثناء', 'بعد', 'قبل',
            
            # أدوات الربط
            'و', 'أو', 'لكن', 'لكن', 'غير', 'إلا', 'سوى', 'أم', 'إما',
            'كي', 'لكي', 'حتى', 'لعل', 'عسى', 'ليت', 'لو', 'إذا', 'إن',
            
            # أسماء الإشارة
            'هذا', 'هذه', 'ذلك', 'تلك', 'هؤلاء', 'أولئك', 'هنا', 'هناك', 'هنالك',
            
            # أسماء الموصول
            'الذي', 'التي', 'اللذان', 'اللتان', 'اللذين', 'اللتين', 'الذين', 'اللاتي', 'اللواتي',
            
            # أفعال مساعدة
            'كان', 'كانت', 'كانوا', 'كن', 'يكون', 'تكون', 'يكونوا', 'يكن',
            'أصبح', 'أصبحت', 'بات', 'باتت', 'ظل', 'ظلت', 'مازال', 'مازالت',
            
            # نفي واستفهام
            'لا', 'لم', 'لن', 'ما', 'ليس', 'ليست', 'هل', 'أين', 'متى', 'كيف', 'ماذا', 'لماذا',
            
            # كلمات عامة
            'كل', 'بعض', 'جميع', 'كلا', 'كلتا', 'معظم', 'أغلب', 'قليل', 'كثير',
            'أول', 'آخر', 'نفس', 'ذات', 'غير', 'سوى'
        }
        
        # المصطلحات المحاسبية المتقدمة والمحسنة
        self.accounting_terms = {
            # المصطلحات الأساسية
            'basic_terms': {
                'فاتورة', 'فواتير', 'عميل', 'عملاء', 'مشروع', 'مشاريع',
                'ربح', 'أرباح', 'خسارة', 'خسائر', 'مبيعات', 'مشتريات',
                'إيرادات', 'مصروفات', 'أصول', 'خصوم', 'ميزانية',
                'تدفق', 'نقدي', 'تحليل', 'تقرير', 'حساب', 'احسب'
            },
            
            # مصطلحات متقدمة
            'advanced_terms': {
                'استحقاق', 'مطابقة', 'ثبات', 'حيطة', 'حذر', 'أهمية', 'نسبية',
                'إهلاك', 'استنزاف', 'مخصص', 'احتياطي', 'رسملة', 'توزيع',
                'تسوية', 'مراجعة', 'تدقيق', 'رقابة', 'امتثال', 'معايير',
                'استثمار', 'تمويل', 'سيولة', 'ملاءة', 'جودة', 'أرباح'
            },
            
            # النسب المالية
            'financial_ratios': {
                'سيولة', 'ربحية', 'كفاءة', 'مديونية', 'نشاط', 'دوران',
                'هامش', 'عائد', 'معدل', 'نسبة', 'مؤشر', 'قياس',
                'أداء', 'فعالية', 'إنتاجية', 'استغلال', 'توظيف'
            },
            
            # القوائم المالية
            'financial_statements': {
                'دخل', 'مركز', 'مالي', 'تدفقات', 'نقدية', 'ملكية',
                'تغيرات', 'إيضاحات', 'متممة', 'مرفقة', 'شاملة', 'موحدة'
            },
            
            # مصطلحات التكنولوجيا المالية
            'fintech_terms': {
                'رقمي', 'إلكتروني', 'آلي', 'ذكي', 'تحليلات', 'بيانات',
                'خوارزمية', 'تعلم', 'آلة', 'ذكاء', 'اصطناعي', 'تنبؤ'
            }
        }
        
        # أنماط التعبيرات النمطية المحسنة للمعرفة المحاسبية
        self.accounting_patterns = {
            'calculation_request': [
                r'احسب\s+(.+)',
                r'كم\s+(.+)',
                r'ما\s+هو\s+(.+)',
                r'أريد\s+حساب\s+(.+)',
                r'قدر\s+(.+)',
                r'اعطني\s+(.+)',
                r'وضح\s+(.+)'
            ],
            'data_request': [
                r'اظهر\s+(.+)',
                r'عرض\s+(.+)',
                r'أريد\s+رؤية\s+(.+)',
                r'قائمة\s+(.+)',
                r'بيانات\s+(.+)',
                r'معلومات\s+(.+)',
                r'تفاصيل\s+(.+)'
            ],
            'analysis_request': [
                r'حلل\s+(.+)',
                r'تحليل\s+(.+)',
                r'ما\s+رأيك\s+في\s+(.+)',
                r'كيف\s+يبدو\s+(.+)',
                r'قيم\s+(.+)',
                r'ادرس\s+(.+)',
                r'فحص\s+(.+)'
            ],
            'comparison_request': [
                r'قارن\s+(.+)\s+مع\s+(.+)',
                r'الفرق\s+بين\s+(.+)\s+و\s+(.+)',
                r'أيهما\s+أفضل\s+(.+)\s+أم\s+(.+)',
                r'مقارنة\s+(.+)\s+و\s+(.+)'
            ],
            'prediction_request': [
                r'توقع\s+(.+)',
                r'تنبؤ\s+(.+)',
                r'ما\s+المتوقع\s+(.+)',
                r'كيف\s+سيكون\s+(.+)',
                r'اتجاه\s+(.+)'
            ],
            'recommendation_request': [
                r'انصحني\s+(.+)',
                r'اقترح\s+(.+)',
                r'ما\s+رأيك\s+(.+)',
                r'توصية\s+(.+)',
                r'نصيحة\s+(.+)'
            ]
        }
        
        # نماذج التعلم المحسنة
        if HAS_SKLEARN and TfidfVectorizer is not None:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=2000,  # زيادة عدد الميزات
                ngram_range=(1, 3),  # تضمين 3-grams
                stop_words=list(self.stop_words),
                min_df=1,
                max_df=0.95
            )
            self.intent_classifier = None  # سيتم تدريبه لاحقاً
        else:
            self.tfidf_vectorizer = None
            self.intent_classifier = None
        
        # ذاكرة التفاعلات المحسنة
        self.interaction_memory = []
        self.learned_patterns = defaultdict(list)
        self.user_preferences = defaultdict(dict)
        self.context_memory = defaultdict(list)  # ذاكرة السياق
        
        # إحصائيات الاستخدام المحسنة
        self.usage_stats = {
            'total_interactions': 0,
            'intent_distribution': defaultdict(int),
            'keyword_frequency': defaultdict(int),
            'user_satisfaction': defaultdict(list),
            'response_time': defaultdict(list),
            'accuracy_scores': defaultdict(list)
        }
        
        # نظام التعلم التدريجي
        self.learning_system = {
            'pattern_recognition': defaultdict(float),
            'user_behavior': defaultdict(dict),
            'feedback_scores': defaultdict(list),
            'adaptation_rate': 0.1
        }
    
    def preprocess_text(self, text: str) -> str:
        """معالجة أولية متقدمة ومحسنة للنص"""
        if not text:
            return ""
        
        # تنظيف النص
        text = self._normalize_arabic(text)
        text = self._remove_diacritics(text)
        text = self._standardize_punctuation(text)
        text = self._handle_numbers(text)
        
        return text.strip()
    
    def _normalize_arabic(self, text: str) -> str:
        """تطبيع الأحرف العربية المحسن"""
        # توحيد الهمزات
        text = re.sub(r'[أإآ]', 'ا', text)
        text = re.sub(r'[ؤ]', 'و', text)
        text = re.sub(r'[ئ]', 'ي', text)
        text = re.sub(r'[ة]', 'ه', text)
        
        # توحيد الياء والألف المقصورة
        text = re.sub(r'[ى]', 'ي', text)
        
        # توحيد التاء المربوطة والهاء
        text = re.sub(r'[ة]', 'ه', text)
        
        return text
    
    def _remove_diacritics(self, text: str) -> str:
        """إزالة التشكيل"""
        return re.sub(r'[\u064B-\u0652]', '', text)
    
    def _standardize_punctuation(self, text: str) -> str:
        """توحيد علامات الترقيم المحسن"""
        # توحيد المسافات
        text = re.sub(r'\s+', ' ', text)
        
        # توحيد علامات الاستفهام والتعجب
        text = re.sub(r'[؟]', '?', text)
        text = re.sub(r'[!]', '!', text)
        
        # إزالة علامات الترقيم الزائدة
        text = re.sub(r'[^\u0600-\u06FF\s\d\.\,\?\!]', ' ', text)
        
        return text
    
    def _handle_numbers(self, text: str) -> str:
        """معالجة الأرقام في النص"""
        # تحويل الأرقام العربية إلى إنجليزية
        arabic_to_english = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }
        
        for arabic, english in arabic_to_english.items():
            text = text.replace(arabic, english)
        
        return text

    def extract_keywords(self, text: str, min_length: int = 2) -> List[str]:
        """استخراج الكلمات المفتاحية المحسن مع التعلم التدريجي"""
        text = self.preprocess_text(text)
        words = text.split()

        keywords = []
        for word in words:
            if (len(word) >= min_length and
                word not in self.stop_words and
                self._is_meaningful_word(word)):
                keywords.append(word)

        # إضافة الكلمات المركبة
        keywords.extend(self._extract_compound_terms(text))

        # ترتيب حسب الأهمية مع التعلم
        return self._rank_keywords_with_learning(keywords, text)

    def _is_meaningful_word(self, word: str) -> bool:
        """تحديد ما إذا كانت الكلمة ذات معنى مع التحسين"""
        # تحقق من أن الكلمة تحتوي على أحرف عربية
        if not re.search(r'[\u0600-\u06FF]', word):
            return False

        # تحقق من أن الكلمة ليست مجرد أرقام
        if word.isdigit():
            return False

        # تحقق من القائمة السوداء المحسنة
        blacklist = {'هذا', 'هذه', 'ذلك', 'تلك', 'الذي', 'التي', 'يعني', 'يكون'}
        if word in blacklist:
            return False

        # تحقق من الطول الأدنى
        if len(word) < 2:
            return False

        return True

    def _extract_compound_terms(self, text: str) -> List[str]:
        """استخراج المصطلحات المركبة المحسن"""
        compound_terms = []

        # البحث عن مصطلحات محاسبية مركبة محسنة
        patterns = [
            r'قائمة\s+الدخل',
            r'الميزانية\s+العمومية',
            r'التدفقات\s+النقدية',
            r'حقوق\s+الملكية',
            r'تكلفة\s+البضاعة\s+المباعة',
            r'صافي\s+الربح',
            r'إجمالي\s+الربح',
            r'الأصول\s+المتداولة',
            r'الخصوم\s+المتداولة',
            r'رأس\s+المال\s+العامل',
            r'معدل\s+العائد\s+على\s+الاستثمار',
            r'نسبة\s+السيولة\s+السريعة',
            r'هامش\s+الربح\s+الإجمالي',
            r'دورة\s+التشغيل',
            r'فترة\s+التحصيل'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            compound_terms.extend(matches)

        return compound_terms

    def _rank_keywords_with_learning(self, keywords: List[str], text: str) -> List[str]:
        """ترتيب الكلمات المفتاحية حسب الأهمية مع التعلم التدريجي"""
        _ = text  # متغير غير مستخدم حالياً
        if not keywords:
            return []

        # حساب تكرار الكلمات
        word_freq = Counter(keywords)

        # حساب نقاط الأهمية مع التعلم
        scored_keywords = []
        for word in set(keywords):
            score = word_freq[word]  # تكرار الكلمة

            # زيادة النقاط للمصطلحات المحاسبية
            if self._is_accounting_term(word):
                score *= 2.5  # زيادة الوزن

            # زيادة النقاط للكلمات الطويلة
            if len(word) > 5:
                score *= 1.5

            # إضافة نقاط التعلم من التفاعلات السابقة
            if word in self.learning_system['pattern_recognition']:
                score *= (1 + self.learning_system['pattern_recognition'][word])

            # زيادة النقاط للمصطلحات المركبة
            if ' ' in word:
                score *= 2

            scored_keywords.append((word, score))

        # ترتيب حسب النقاط
        scored_keywords.sort(key=lambda x: x[1], reverse=True)

        # تحديث إحصائيات التعلم
        for word, _ in scored_keywords[:5]:  # أفضل 5 كلمات
            self.learning_system['pattern_recognition'][word] += 0.1

        return [word for word, _ in scored_keywords[:15]]  # أفضل 15 كلمة

    def _is_accounting_term(self, word: str) -> bool:
        """تحديد ما إذا كانت الكلمة مصطلحاً محاسبياً مع التحسين"""
        for _, terms in self.accounting_terms.items():
            if word in terms:
                return True

        # البحث في المصطلحات المتعلمة
        if word in self.learned_patterns['accounting_terms']:
            return True

        return False

    def detect_intent_advanced(self, text: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """كشف النية المتقدم مع التعلم والسياق"""
        text = self.preprocess_text(text)

        # استخراج الكلمات المفتاحية
        keywords = self.extract_keywords(text)

        # كشف النية الأساسي
        basic_intent = self._detect_basic_intent(text, keywords)

        # تحسين النية بناءً على السياق
        contextual_intent = self._enhance_intent_with_context(basic_intent, text, user_id)

        # حساب مستوى الثقة
        confidence = self._calculate_confidence(text, keywords, contextual_intent)

        # استخراج الكيانات
        entities = self._extract_entities_advanced(text)

        # تحديث إحصائيات الاستخدام
        self._update_usage_stats(contextual_intent, keywords, user_id)

        return {
            'intent': contextual_intent,
            'confidence': confidence,
            'keywords': keywords,
            'entities': entities,
            'patterns_matched': self._get_matched_patterns(text),
            'context_used': user_id is not None and user_id in self.context_memory
        }

    def _detect_basic_intent(self, text: str, keywords: List[str]) -> str:
        """كشف النية الأساسي المحسن"""
        _ = keywords  # متغير غير مستخدم حالياً
        text_lower = text.lower()

        # تحيات محسنة
        greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء', 'كيف حالك', 'أهلين']
        if any(greeting in text_lower for greeting in greetings):
            return 'greeting'

        # أسئلة محاسبية محسنة
        accounting_terms = ['محاسبة', 'مبدأ', 'قائمة', 'ميزانية', 'نسبة', 'مصطلح', 'تعريف', 'شرح']
        if any(term in text_lower for term in accounting_terms):
            return 'accounting_question'

        # طلبات حسابية محسنة
        calculation_terms = ['احسب', 'حساب', 'كم', 'مجموع', 'متوسط', 'نسبة', 'قدر', 'اعطني']
        if any(term in text_lower for term in calculation_terms):
            return 'calculation'

        # طلبات تحليل محسنة
        analysis_terms = ['تحليل', 'تقرير', 'إحصائيات', 'اتجاه', 'توقع', 'قيم', 'ادرس', 'فحص']
        if any(term in text_lower for term in analysis_terms):
            return 'analysis_request'

        # استعلامات البيانات محسنة
        data_terms = ['اظهر', 'عرض', 'قائمة', 'فواتير', 'عملاء', 'مشاريع', 'بيانات', 'معلومات']
        if any(term in text_lower for term in data_terms):
            return 'data_query'

        # طلبات المساعدة محسنة
        help_terms = ['مساعدة', 'كيف', 'ماذا', 'أين', 'متى', 'ساعدني', 'أحتاج']
        if any(term in text_lower for term in help_terms):
            return 'help'

        # طلبات التوصيات
        recommendation_terms = ['انصحني', 'اقترح', 'ما رأيك', 'توصية', 'نصيحة']
        if any(term in text_lower for term in recommendation_terms):
            return 'recommendation_request'

        # طلبات التنبؤ
        prediction_terms = ['توقع', 'تنبؤ', 'المتوقع', 'سيكون', 'مستقبل']
        if any(term in text_lower for term in prediction_terms):
            return 'prediction_request'

        return 'general'

    def _enhance_intent_with_context(self, basic_intent: str, text: str, user_id: Optional[int] = None) -> str:
        """تحسين النية بناءً على السياق والتعلم"""
        try:
            # إذا لم يكن هناك معرف مستخدم، إرجاع النية الأساسية
            if user_id is None:
                return basic_intent

            # تحسين النية بناءً على تاريخ المستخدم
            if user_id in self.user_context:
                user_history = self.user_context[user_id]

                # إذا كان المستخدم يسأل أسئلة محاسبية متكررة
                if user_history.get('frequent_intent') == 'accounting_question' and basic_intent == 'general':
                    if any(term in text for term in ['ما', 'كيف', 'لماذا', 'متى']):
                        return 'accounting_question'

                # إذا كان المستخدم يطلب تحليلات متكررة
                if user_history.get('frequent_intent') == 'analysis_request' and basic_intent == 'general':
                    if any(term in text for term in ['اظهر', 'عرض', 'بيانات']):
                        return 'analysis_request'

            return basic_intent

        except Exception as e:
            logger.error(f"Error enhancing intent with context: {str(e)}")
            return basic_intent

    def _calculate_confidence(self, text: str, keywords: List[str], intent: str) -> float:
        """حساب مستوى الثقة في كشف النية"""
        try:
            confidence = 0.5  # قيمة أساسية

            # زيادة الثقة بناءً على عدد الكلمات المفتاحية المطابقة
            keyword_score = min(len(keywords) * 0.1, 0.3)
            confidence += keyword_score

            # زيادة الثقة بناءً على وضوح النية
            intent_clarity_scores = {
                'greeting': 0.9,
                'accounting_question': 0.8,
                'calculation': 0.8,
                'analysis_request': 0.7,
                'data_query': 0.7,
                'help': 0.9,
                'general': 0.5
            }

            intent_score = intent_clarity_scores.get(intent, 0.5)
            confidence = max(confidence, intent_score)

            # تقليل الثقة للنصوص القصيرة جداً
            if len(text.split()) < 3:
                confidence *= 0.8

            # زيادة الثقة للنصوص التي تحتوي على مصطلحات محاسبية
            accounting_terms = ['محاسبة', 'مالية', 'ميزانية', 'ربح', 'خسارة', 'فاتورة', 'عميل']
            if any(term in text for term in accounting_terms):
                confidence += 0.1

            return min(confidence, 1.0)  # الحد الأقصى 1.0

        except Exception as e:
            logger.error(f"Error calculating confidence: {str(e)}")
            return 0.5

    def _extract_entities(self, text: str, intent: str) -> Dict[str, Any]:
        """استخراج الكيانات من النص"""
        try:
            entities = {}

            # استخراج الأرقام
            import re
            numbers = re.findall(r'\d+(?:\.\d+)?', text)
            if numbers:
                entities['numbers'] = [float(num) for num in numbers]

            # استخراج التواريخ (بسيط)
            date_patterns = [
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{1,2}-\d{1,2}-\d{4}',
                r'\d{4}/\d{1,2}/\d{1,2}'
            ]

            dates = []
            for pattern in date_patterns:
                dates.extend(re.findall(pattern, text))

            if dates:
                entities['dates'] = dates

            # استخراج أسماء العملاء (إذا كانت موجودة)
            if intent in ['data_query', 'analysis_request']:
                # البحث عن أنماط أسماء العملاء
                client_indicators = ['عميل', 'شركة', 'مؤسسة']
                for indicator in client_indicators:
                    if indicator in text:
                        # محاولة استخراج الاسم بعد المؤشر
                        words = text.split()
                        try:
                            indicator_index = words.index(indicator)
                            if indicator_index + 1 < len(words):
                                entities['client_name'] = words[indicator_index + 1]
                        except ValueError:
                            pass

            # استخراج أنواع التقارير
            report_types = ['ميزانية', 'دخل', 'تدفق', 'أرباح', 'مبيعات']
            for report_type in report_types:
                if report_type in text:
                    if 'report_types' not in entities:
                        entities['report_types'] = []
                    entities['report_types'].append(report_type)

            return entities

        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return {}

    def update_user_context(self, user_id: int, intent: str, keywords: List[str], confidence: float):
        """تحديث سياق المستخدم للتعلم"""
        try:
            if user_id not in self.user_context:
                self.user_context[user_id] = {
                    'interaction_count': 0,
                    'intent_history': [],
                    'keyword_frequency': {},
                    'frequent_intent': None,
                    'avg_confidence': 0.0
                }

            context = self.user_context[user_id]
            context['interaction_count'] += 1
            context['intent_history'].append(intent)

            # الاحتفاظ بآخر 20 تفاعل فقط
            if len(context['intent_history']) > 20:
                context['intent_history'] = context['intent_history'][-20:]

            # تحديث تكرار الكلمات المفتاحية
            for keyword in keywords:
                if keyword in context['keyword_frequency']:
                    context['keyword_frequency'][keyword] += 1
                else:
                    context['keyword_frequency'][keyword] = 1

            # تحديد النية الأكثر تكراراً
            intent_counts = {}
            for hist_intent in context['intent_history']:
                intent_counts[hist_intent] = intent_counts.get(hist_intent, 0) + 1

            if intent_counts:
                context['frequent_intent'] = max(intent_counts.keys(), key=lambda k: intent_counts[k])

            # تحديث متوسط الثقة
            current_avg = context['avg_confidence']
            count = context['interaction_count']
            context['avg_confidence'] = (current_avg * (count - 1) + confidence) / count

        except Exception as e:
            logger.error(f"Error updating user context: {str(e)}")

    def _extract_entities_advanced(self, text: str) -> Dict[str, Any]:
        """استخراج الكيانات المتقدم"""
        try:
            entities = {}

            # استخراج الأرقام
            import re
            numbers = re.findall(r'\d+(?:\.\d+)?', text)
            if numbers:
                entities['numbers'] = [float(num) for num in numbers]

            # استخراج التواريخ
            date_patterns = [
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{1,2}-\d{1,2}-\d{4}',
                r'\d{4}/\d{1,2}/\d{1,2}'
            ]

            dates = []
            for pattern in date_patterns:
                dates.extend(re.findall(pattern, text))

            if dates:
                entities['dates'] = dates

            # استخراج العملات
            currency_patterns = [
                r'\d+(?:\.\d+)?\s*ريال',
                r'\d+(?:\.\d+)?\s*دولار',
                r'\d+(?:\.\d+)?\s*درهم'
            ]

            currencies = []
            for pattern in currency_patterns:
                currencies.extend(re.findall(pattern, text))

            if currencies:
                entities['currencies'] = currencies

            return entities

        except Exception as e:
            logger.error(f"Error extracting advanced entities: {str(e)}")
            return {}

    def _update_usage_stats(self, intent: str, keywords: List[str], user_id: Optional[int] = None):
        """تحديث إحصائيات الاستخدام"""
        _ = user_id  # متغير غير مستخدم حالياً
        try:
            self.usage_stats['total_interactions'] += 1
            self.usage_stats['intent_distribution'][intent] += 1

            for keyword in keywords:
                self.usage_stats['keyword_frequency'][keyword] += 1

        except Exception as e:
            logger.error(f"Error updating usage stats: {str(e)}")

    def _get_matched_patterns(self, text: str) -> List[str]:
        """الحصول على الأنماط المطابقة"""
        try:
            matched = []

            for pattern_type, patterns in self.accounting_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, text, re.IGNORECASE):
                        matched.append(pattern_type)
                        break

            return matched

        except Exception as e:
            logger.error(f"Error getting matched patterns: {str(e)}")
            return []

    # إضافة خاصية user_context إذا لم تكن موجودة
    @property
    def user_context(self):
        """الحصول على سياق المستخدم"""
        if not hasattr(self, '_user_context'):
            self._user_context = defaultdict(dict)
        return self._user_context
