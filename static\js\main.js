/**
 * SmartBiz Accounting - نظام المحاسبة الذكي
 * Main JavaScript File - Enhanced Version
 */

// Global configuration
const SmartBizConfig = {
    apiBaseUrl: '/api',
    language: document.documentElement.lang || 'ar',
    isRTL: document.documentElement.dir === 'rtl',
    theme: localStorage.getItem('theme') || 'light',
    notifications: {
        refreshInterval: 60000, // 1 minute
        maxVisible: 10
    },
    ai: {
        typingDelay: 1000,
        maxRetries: 3
    }
};

// Enhanced initialization
document.addEventListener('DOMContentLoaded', function () {
    console.log('🚀 SmartBiz Accounting System Initializing...');

    try {
        // Core UI components
        initializeTooltips();
        initializePopovers();
        initializeDropdowns();
        initializeModals();

        // Data components
        initializeDataTables();
        initializeCharts();

        // Form components
        setupFormValidation();
        setupEnhancedFormFeatures();

        // System features
        setupLanguageSwitcher();
        setupThemeSwitcher();
        setupNotifications();
        setupAIAssistant();
        setupKeyboardShortcuts();
        setupProgressTracking();

        // Performance monitoring
        setupPerformanceMonitoring();

        console.log('✅ SmartBiz System Initialized Successfully');

    } catch (error) {
        console.error('❌ Error initializing SmartBiz System:', error);
        showErrorNotification('حدث خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة.');
    }

    // Enhanced dynamic content handling
    document.addEventListener('contentLoaded', function () {
        initializeTooltips();
        initializePopovers();
        initializeDataTables();
        setupEnhancedFormFeatures();
    });
});

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            boundary: document.body
        });
    });
}

/**
 * Initialize Bootstrap popovers
 */
function initializePopovers() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Initialize Bootstrap dropdowns
 */
function initializeDropdowns() {
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });
}

/**
 * Initialize Bootstrap modals
 */
function initializeModals() {
    // Auto-focus first input in modals
    const modalElements = document.querySelectorAll('.modal');
    modalElements.forEach(function (modal) {
        modal.addEventListener('shown.bs.modal', function () {
            const firstInput = this.querySelector('input:not([type=hidden]), textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
}

/**
 * Initialize DataTables for tables with the .datatable class
 */
function initializeDataTables() {
    try {
        if (typeof $.fn.DataTable !== 'undefined') {
            const tables = document.querySelectorAll('.datatable');
            tables.forEach(function (table) {
                // Check if DataTable is already initialized
                if (!$.fn.DataTable.isDataTable(table)) {
                    const isRTL = document.documentElement.dir === 'rtl' ||
                        document.body.classList.contains('rtl');

                    $(table).DataTable({
                        responsive: true,
                        language: {
                            url: isRTL ? '/static/js/vendor/datatables/i18n/ar.json' : ''
                        },
                        dom: '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center"l><"d-flex"f>>t<"d-flex justify-content-between align-items-center mt-3"<"text-muted"i><"d-flex"p>>',
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                        order: [[0, 'desc']],
                        pageLength: 10
                    });
                }
            });
        }
    } catch (error) {
        console.error('Error initializing DataTables:', error);
    }
}

/**
 * Initialize Charts using Chart.js
 */
function initializeCharts() {
    try {
        if (typeof Chart !== 'undefined') {
            // Set default options for all charts
            Chart.defaults.font.family = getComputedStyle(document.body).getPropertyValue('font-family');
            Chart.defaults.color = getComputedStyle(document.body).getPropertyValue('color');

            // Initialize all charts with the .chart class
            const chartElements = document.querySelectorAll('.chart');
            chartElements.forEach(function (chartElement) {
                // The chart configuration should be provided as a data attribute
                const chartConfig = chartElement.dataset.config;
                if (chartConfig) {
                    try {
                        const config = JSON.parse(chartConfig);
                        new Chart(chartElement, config);
                    } catch (e) {
                        console.error('Error parsing chart configuration:', e);
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error initializing Charts:', error);
    }
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Custom validation for specific fields
    setupCustomValidation();
}

/**
 * Setup custom validation for specific fields
 */
function setupCustomValidation() {
    // Validate email fields
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(function (field) {
        field.addEventListener('blur', function () {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (field.value && !emailRegex.test(field.value)) {
                field.setCustomValidity('Please enter a valid email address');
            } else {
                field.setCustomValidity('');
            }
        });
    });

    // Validate password fields
    const passwordFields = document.querySelectorAll('input[data-password-validate]');
    passwordFields.forEach(function (field) {
        field.addEventListener('blur', function () {
            const minLength = parseInt(field.dataset.minLength || '8', 10);
            if (field.value && field.value.length < minLength) {
                field.setCustomValidity(`Password must be at least ${minLength} characters long`);
            } else {
                field.setCustomValidity('');
            }
        });
    });

    // Validate password confirmation
    const passwordConfirmFields = document.querySelectorAll('input[data-password-confirm]');
    passwordConfirmFields.forEach(function (field) {
        field.addEventListener('blur', function () {
            const passwordField = document.querySelector(field.dataset.passwordConfirm);
            if (passwordField && field.value !== passwordField.value) {
                field.setCustomValidity('Passwords do not match');
            } else {
                field.setCustomValidity('');
            }
        });
    });
}

/**
 * Setup language switcher
 */
function setupLanguageSwitcher() {
    const languageSwitchers = document.querySelectorAll('.language-switcher');

    languageSwitchers.forEach(function (switcher) {
        switcher.addEventListener('click', function (event) {
            event.preventDefault();

            const language = this.dataset.language;
            if (language) {
                // Set language cookie
                document.cookie = `language=${language}; path=/; max-age=31536000`; // 1 year

                // Reload the page to apply the language change
                window.location.reload();
            }
        });
    });
}

/**
 * Setup theme switcher (light/dark mode)
 */
function setupThemeSwitcher() {
    const themeSwitcher = document.querySelector('.theme-switcher');

    if (themeSwitcher) {
        // Check for saved theme preference or respect OS preference
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.body.classList.add('dark-mode');
            updateThemeIcon(true);
        }

        themeSwitcher.addEventListener('click', function (event) {
            event.preventDefault();

            const isDarkMode = document.body.classList.toggle('dark-mode');
            localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');

            updateThemeIcon(isDarkMode);
        });
    }
}

/**
 * Update theme icon based on current mode
 */
function updateThemeIcon(isDarkMode) {
    const themeIcon = document.querySelector('.theme-switcher i');

    if (themeIcon) {
        if (isDarkMode) {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
}

/**
 * Setup notifications
 */
function setupNotifications() {
    const notificationButton = document.querySelector('.notification-button');

    if (notificationButton) {
        // Fetch notifications
        fetchNotifications();

        // Setup notification refresh
        setInterval(fetchNotifications, 60000); // Refresh every minute

        // Mark notifications as read when clicked
        const notificationList = document.querySelector('.notification-list');
        if (notificationList) {
            notificationList.addEventListener('click', function (event) {
                const notificationItem = event.target.closest('.notification-item');
                if (notificationItem) {
                    const notificationId = notificationItem.dataset.id;
                    if (notificationId) {
                        markNotificationAsRead(notificationId);
                    }
                }
            });
        }
    }
}

/**
 * Fetch notifications from the server
 */
function fetchNotifications() {
    const notificationList = document.querySelector('.notification-list');
    const notificationBadge = document.querySelector('.notification-badge');

    if (notificationList && notificationBadge) {
        fetch('/api/notifications')
            .then(response => response.json())
            .then(data => {
                // Update notification list
                notificationList.innerHTML = '';

                if (data.notifications.length === 0) {
                    notificationList.innerHTML = '<div class="dropdown-item text-center">No new notifications</div>';
                    notificationBadge.style.display = 'none';
                } else {
                    // Count unread notifications
                    const unreadCount = data.notifications.filter(notification => !notification.read).length;

                    // Update badge
                    if (unreadCount > 0) {
                        notificationBadge.textContent = unreadCount;
                        notificationBadge.style.display = 'block';
                    } else {
                        notificationBadge.style.display = 'none';
                    }

                    // Add notifications to list
                    data.notifications.forEach(notification => {
                        const item = document.createElement('div');
                        item.className = `dropdown-item notification-item ${notification.read ? '' : 'unread'}`;
                        item.dataset.id = notification.id;

                        const time = new Date(notification.timestamp);
                        const timeAgo = getTimeAgo(time);

                        item.innerHTML = `
                            <div class="d-flex align-items-center">
                                <div class="notification-icon bg-${notification.type}">
                                    <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-text">${notification.message}</div>
                                    <div class="notification-time">${timeAgo}</div>
                                </div>
                            </div>
                        `;

                        notificationList.appendChild(item);
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching notifications:', error);
            });
    }
}

/**
 * Mark a notification as read
 */
function markNotificationAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': getCSRFToken()
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI
                const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
                if (notificationItem) {
                    notificationItem.classList.remove('unread');
                }

                // Update badge
                const notificationBadge = document.querySelector('.notification-badge');
                if (notificationBadge) {
                    const currentCount = parseInt(notificationBadge.textContent, 10);
                    if (currentCount > 1) {
                        notificationBadge.textContent = currentCount - 1;
                    } else {
                        notificationBadge.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
}

/**
 * Get appropriate icon for notification type
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'primary':
            return 'info-circle';
        case 'success':
            return 'check-circle';
        case 'danger':
            return 'exclamation-circle';
        case 'warning':
            return 'exclamation-triangle';
        default:
            return 'bell';
    }
}

/**
 * Get time ago string from date
 */
function getTimeAgo(date) {
    const seconds = Math.floor((new Date() - date) / 1000);

    let interval = Math.floor(seconds / 31536000);
    if (interval >= 1) {
        return interval + ' year' + (interval === 1 ? '' : 's') + ' ago';
    }

    interval = Math.floor(seconds / 2592000);
    if (interval >= 1) {
        return interval + ' month' + (interval === 1 ? '' : 's') + ' ago';
    }

    interval = Math.floor(seconds / 86400);
    if (interval >= 1) {
        return interval + ' day' + (interval === 1 ? '' : 's') + ' ago';
    }

    interval = Math.floor(seconds / 3600);
    if (interval >= 1) {
        return interval + ' hour' + (interval === 1 ? '' : 's') + ' ago';
    }

    interval = Math.floor(seconds / 60);
    if (interval >= 1) {
        return interval + ' minute' + (interval === 1 ? '' : 's') + ' ago';
    }

    return Math.floor(seconds) + ' second' + (seconds === 1 ? '' : 's') + ' ago';
}

/**
 * Get CSRF token from meta tag
 */
function getCSRFToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
}

/**
 * Setup AI Assistant
 */
function setupAIAssistant() {
    const chatContainer = document.querySelector('.ai-assistant-container');

    if (chatContainer) {
        const chatMessages = chatContainer.querySelector('.chat-messages');
        const chatForm = chatContainer.querySelector('.chat-form');
        const chatInput = chatContainer.querySelector('.chat-input');

        if (chatForm && chatInput) {
            chatForm.addEventListener('submit', function (event) {
                event.preventDefault();

                const message = chatInput.value.trim();
                if (message) {
                    // Add user message to chat
                    addChatMessage(chatMessages, message, 'user');

                    // Clear input
                    chatInput.value = '';

                    // Show typing indicator
                    showTypingIndicator(chatMessages);

                    // Send message to AI Assistant
                    sendMessageToAI(message, chatMessages);
                }
            });
        }

        // Voice input for AI Assistant
        const voiceButton = chatContainer.querySelector('.voice-input-button');
        if (voiceButton) {
            voiceButton.addEventListener('click', function () {
                startVoiceRecognition(chatInput);
            });
        }
    }
}

/**
 * Add a message to the chat
 */
function addChatMessage(chatMessages, message, sender) {
    const messageElement = document.createElement('div');
    messageElement.className = `message message-${sender}`;
    messageElement.textContent = message;

    chatMessages.appendChild(messageElement);

    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Show typing indicator in chat
 */
function showTypingIndicator(chatMessages) {
    const typingIndicator = document.createElement('div');
    typingIndicator.className = 'message message-ai typing-indicator';
    typingIndicator.innerHTML = '<span></span><span></span><span></span>';
    typingIndicator.id = 'typing-indicator';

    chatMessages.appendChild(typingIndicator);

    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Remove typing indicator from chat
 */
function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

/**
 * Send message to AI Assistant
 */
function sendMessageToAI(message, chatMessages) {
    fetch('/api/ai-assistant', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': getCSRFToken()
        },
        body: JSON.stringify({ message: message })
    })
        .then(response => response.json())
        .then(data => {
            // Remove typing indicator
            removeTypingIndicator();

            // Add AI response to chat
            addChatMessage(chatMessages, data.response, 'ai');
        })
        .catch(error => {
            console.error('Error sending message to AI:', error);

            // Remove typing indicator
            removeTypingIndicator();

            // Add error message
            addChatMessage(chatMessages, 'Sorry, I encountered an error. Please try again later.', 'ai');
        });
}

/**
 * Start voice recognition for AI Assistant
 */
function startVoiceRecognition(inputElement) {
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.lang = document.documentElement.lang || 'en-US';
        recognition.continuous = false;
        recognition.interimResults = false;

        // Change button to indicate recording
        const voiceButton = document.querySelector('.voice-input-button');
        if (voiceButton) {
            voiceButton.classList.add('recording');
            voiceButton.querySelector('i').className = 'fas fa-microphone-slash';
        }

        recognition.onresult = function (event) {
            const transcript = event.results[0][0].transcript;
            inputElement.value = transcript;
        };

        recognition.onerror = function (event) {
            console.error('Speech recognition error:', event.error);
        };

        recognition.onend = function () {
            // Reset button
            if (voiceButton) {
                voiceButton.classList.remove('recording');
                voiceButton.querySelector('i').className = 'fas fa-microphone';
            }
        };

        recognition.start();
    } else {
        alert('Speech recognition is not supported in your browser.');
    }
}

/**
 * Handle invoice calculations
 */
function setupInvoiceCalculations() {
    const invoiceForm = document.querySelector('.invoice-form');

    if (invoiceForm) {
        // Calculate line totals when quantity or price changes
        invoiceForm.addEventListener('input', function (event) {
            const target = event.target;

            if (target.classList.contains('item-quantity') || target.classList.contains('item-price')) {
                const row = target.closest('.invoice-item-row');
                calculateLineTotal(row);
                calculateInvoiceTotal();
            }

            if (target.id === 'tax_rate' || target.id === 'discount') {
                calculateInvoiceTotal();
            }
        });

        // Add new line item
        const addItemButton = invoiceForm.querySelector('.add-invoice-item');
        if (addItemButton) {
            addItemButton.addEventListener('click', function (event) {
                event.preventDefault();
                addInvoiceItem();
            });
        }

        // Remove line item
        invoiceForm.addEventListener('click', function (event) {
            if (event.target.classList.contains('remove-invoice-item')) {
                event.preventDefault();
                const row = event.target.closest('.invoice-item-row');
                row.remove();
                calculateInvoiceTotal();
            }
        });

        // Initialize calculations
        calculateInvoiceTotal();
    }
}

/**
 * Calculate line total for invoice item
 */
function calculateLineTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const totalElement = row.querySelector('.item-total');

    const total = quantity * price;
    totalElement.textContent = total.toFixed(2);
    totalElement.dataset.value = total;
}

/**
 * Calculate invoice total
 */
function calculateInvoiceTotal() {
    const itemTotals = document.querySelectorAll('.item-total');
    const subtotalElement = document.getElementById('subtotal');
    const taxRateElement = document.getElementById('tax_rate');
    const taxAmountElement = document.getElementById('tax_amount');
    const discountElement = document.getElementById('discount');
    const discountAmountElement = document.getElementById('discount_amount');
    const totalElement = document.getElementById('total');

    // Calculate subtotal
    let subtotal = 0;
    itemTotals.forEach(function (element) {
        subtotal += parseFloat(element.dataset.value) || 0;
    });

    // Calculate tax
    const taxRate = parseFloat(taxRateElement.value) || 0;
    const taxAmount = subtotal * (taxRate / 100);

    // Calculate discount
    const discount = parseFloat(discountElement.value) || 0;
    const discountAmount = subtotal * (discount / 100);

    // Calculate total
    const total = subtotal + taxAmount - discountAmount;

    // Update elements
    subtotalElement.textContent = subtotal.toFixed(2);
    taxAmountElement.textContent = taxAmount.toFixed(2);
    discountAmountElement.textContent = discountAmount.toFixed(2);
    totalElement.textContent = total.toFixed(2);

    // Update hidden fields
    document.getElementById('subtotal_input').value = subtotal.toFixed(2);
    document.getElementById('tax_amount_input').value = taxAmount.toFixed(2);
    document.getElementById('discount_amount_input').value = discountAmount.toFixed(2);
    document.getElementById('total_input').value = total.toFixed(2);
}

/**
 * Add new invoice item row
 */
function addInvoiceItem() {
    const itemsContainer = document.querySelector('.invoice-items-container');
    const itemTemplate = document.getElementById('invoice-item-template');

    if (itemsContainer && itemTemplate) {
        const newRow = document.importNode(itemTemplate.content, true);
        itemsContainer.appendChild(newRow);

        // Focus on the first input of the new row
        const firstInput = itemsContainer.lastElementChild.querySelector('input');
        if (firstInput) {
            firstInput.focus();
        }
    }
}

/**
 * Handle file uploads with preview
 */
function setupFileUploads() {
    const fileInputs = document.querySelectorAll('.custom-file-input');

    fileInputs.forEach(function (input) {
        input.addEventListener('change', function () {
            const label = input.nextElementSibling;

            if (input.files.length) {
                label.textContent = input.files[0].name;

                // Show preview if it's an image
                const preview = document.querySelector(input.dataset.preview);
                if (preview && input.files[0].type.startsWith('image/')) {
                    const reader = new FileReader();

                    reader.onload = function (e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };

                    reader.readAsDataURL(input.files[0]);
                }
            } else {
                label.textContent = label.dataset.browse || 'Browse';
            }
        });
    });
}

/**
 * Handle dynamic form fields
 */
function setupDynamicFormFields() {
    // Add new form field group
    const addFieldButtons = document.querySelectorAll('.add-field-group');

    addFieldButtons.forEach(function (button) {
        button.addEventListener('click', function (event) {
            event.preventDefault();

            const container = document.querySelector(button.dataset.container);
            const template = document.querySelector(button.dataset.template);

            if (container && template) {
                const newGroup = document.importNode(template.content, true);
                container.appendChild(newGroup);

                // Focus on the first input of the new group
                const firstInput = container.lastElementChild.querySelector('input, select, textarea');
                if (firstInput) {
                    firstInput.focus();
                }

                // Initialize any components in the new group
                const event = new Event('contentLoaded');
                document.dispatchEvent(event);
            }
        });
    });

    // Remove form field group
    document.addEventListener('click', function (event) {
        if (event.target.classList.contains('remove-field-group')) {
            event.preventDefault();

            const group = event.target.closest('.field-group');
            if (group) {
                group.remove();
            }
        }
    });
}

/**
 * Handle print functionality
 */
function setupPrintButtons() {
    const printButtons = document.querySelectorAll('.print-button');

    printButtons.forEach(function (button) {
        button.addEventListener('click', function (event) {
            event.preventDefault();

            window.print();
        });
    });
}

/**
 * Handle export functionality
 */
function setupExportButtons() {
    const exportButtons = document.querySelectorAll('.export-button');

    exportButtons.forEach(function (button) {
        button.addEventListener('click', function (event) {
            event.preventDefault();

            const format = button.dataset.format;
            const url = button.dataset.url;

            if (format && url) {
                window.location.href = `${url}?format=${format}`;
            }
        });
    });
}

/**
 * Handle date pickers
 */
function setupDatePickers() {
    try {
        if (typeof flatpickr !== 'undefined') {
            const dateInputs = document.querySelectorAll('.datepicker');

            dateInputs.forEach(function (input) {
                const isRTL = document.documentElement.dir === 'rtl' ||
                    document.body.classList.contains('rtl');

                flatpickr(input, {
                    dateFormat: 'Y-m-d',
                    locale: isRTL ? 'ar' : 'en',
                    allowInput: true
                });
            });
        }
    } catch (error) {
        console.error('Error initializing date pickers:', error);
    }
}

/**
 * Handle select2 dropdowns
 */
function setupSelect2() {
    try {
        if (typeof $.fn.select2 !== 'undefined') {
            const selectInputs = document.querySelectorAll('.select2');

            selectInputs.forEach(function (input) {
                const isRTL = document.documentElement.dir === 'rtl' ||
                    document.body.classList.contains('rtl');

                $(input).select2({
                    dir: isRTL ? 'rtl' : 'ltr',
                    width: '100%'
                });
            });
        }
    } catch (error) {
        console.error('Error initializing select2:', error);
    }
}

/**
 * Initialize all form-related components
 */
function initializeFormComponents() {
    setupDatePickers();
    setupSelect2();
    setupFileUploads();
    setupDynamicFormFields();
    setupInvoiceCalculations();
}

/**
 * Enhanced Form Features
 */
function setupEnhancedFormFeatures() {
    setupDatePickers();
    setupSelect2();
    setupFileUploads();
    setupDynamicFormFields();
    setupInvoiceCalculations();
    setupAutoSave();
    setupFormProgress();
}

/**
 * Setup auto-save functionality
 */
function setupAutoSave() {
    const autoSaveForms = document.querySelectorAll('[data-auto-save]');

    autoSaveForms.forEach(form => {
        let saveTimeout;
        const saveInterval = parseInt(form.dataset.autoSave) || 30000; // 30 seconds default

        form.addEventListener('input', function () {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                saveFormData(form);
            }, saveInterval);
        });
    });
}

/**
 * Save form data to localStorage
 */
function saveFormData(form) {
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    const formId = form.id || 'unnamed-form';
    localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));

    showSuccessNotification('تم حفظ البيانات تلقائياً', 2000);
}

/**
 * Setup form progress tracking
 */
function setupFormProgress() {
    const progressForms = document.querySelectorAll('[data-form-progress]');

    progressForms.forEach(form => {
        const progressBar = form.querySelector('.form-progress-bar');
        if (progressBar) {
            updateFormProgress(form, progressBar);

            form.addEventListener('input', () => {
                updateFormProgress(form, progressBar);
            });
        }
    });
}

/**
 * Update form progress
 */
function updateFormProgress(form, progressBar) {
    const requiredFields = form.querySelectorAll('[required]');
    const filledFields = Array.from(requiredFields).filter(field => {
        return field.value.trim() !== '';
    });

    const progress = (filledFields.length / requiredFields.length) * 100;
    progressBar.style.width = `${progress}%`;
    progressBar.setAttribute('aria-valuenow', progress);

    // Update progress text
    const progressText = form.querySelector('.form-progress-text');
    if (progressText) {
        progressText.textContent = `${Math.round(progress)}% مكتمل`;
    }
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function (event) {
        // Ctrl/Cmd + S: Save form
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            const activeForm = document.querySelector('form:focus-within');
            if (activeForm) {
                activeForm.dispatchEvent(new Event('submit'));
            }
        }

        // Ctrl/Cmd + N: New item
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            const newButton = document.querySelector('[data-shortcut="new"]');
            if (newButton) {
                newButton.click();
            }
        }

        // Escape: Close modals
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
}

/**
 * Setup progress tracking for long operations
 */
function setupProgressTracking() {
    // Track form submissions
    document.addEventListener('submit', function (event) {
        const form = event.target;
        if (form.dataset.trackProgress) {
            showProgressModal('جاري المعالجة...');
        }
    });

    // Track AJAX requests
    const originalFetch = window.fetch;
    window.fetch = function (...args) {
        const request = originalFetch.apply(this, args);

        // Show loading for long requests
        const loadingTimeout = setTimeout(() => {
            showLoadingIndicator();
        }, 1000);

        request.finally(() => {
            clearTimeout(loadingTimeout);
            hideLoadingIndicator();
        });

        return request;
    };
}

/**
 * Setup performance monitoring
 */
function setupPerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', function () {
        const loadTime = performance.now();
        console.log(`📊 Page loaded in ${loadTime.toFixed(2)}ms`);

        // Send performance data to server (optional)
        if (loadTime > 3000) { // If load time > 3 seconds
            console.warn('⚠️ Slow page load detected');
        }
    });

    // Monitor memory usage
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                console.warn('⚠️ High memory usage detected');
            }
        }, 30000); // Check every 30 seconds
    }
}

/**
 * Enhanced notification system
 */
function showSuccessNotification(message, duration = 5000) {
    showNotification(message, 'success', duration);
}

function showErrorNotification(message, duration = 8000) {
    showNotification(message, 'danger', duration);
}

function showWarningNotification(message, duration = 6000) {
    showNotification(message, 'warning', duration);
}

function showInfoNotification(message, duration = 5000) {
    showNotification(message, 'info', duration);
}

function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

/**
 * Show/hide loading indicators
 */
function showLoadingIndicator() {
    let loader = document.getElementById('global-loader');
    if (!loader) {
        loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
        loader.style.cssText = `
            background: rgba(255,255,255,0.8);
            z-index: 9998;
            backdrop-filter: blur(2px);
        `;
        loader.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        `;
        document.body.appendChild(loader);
    }
    loader.style.display = 'flex';
}

function hideLoadingIndicator() {
    const loader = document.getElementById('global-loader');
    if (loader) {
        loader.style.display = 'none';
    }
}

/**
 * Show progress modal
 */
function showProgressModal(message) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'progress-modal';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري المعالجة...</span>
                    </div>
                    <p class="mb-0">${message}</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const progressModal = new bootstrap.Modal(modal);
    progressModal.show();

    return progressModal;
}

// Initialize enhanced features after DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    setupPrintButtons();
    setupExportButtons();
});