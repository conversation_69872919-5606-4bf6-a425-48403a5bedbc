#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة تحليل البيانات وإنشاء الرسوم البيانية
"""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import json

class SmartBizAnalytics:
    """
    فئة لتحليل البيانات وإنشاء الرسوم البيانية للتقارير
    """
    
    @staticmethod
    def generate_financial_chart(transactions, start_date=None, end_date=None, chart_type='bar'):
        """
        إنشاء رسم بياني للتقرير المالي
        
        المعلمات:
            transactions: قائمة المعاملات
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            chart_type: نوع الرسم البياني ('bar', 'pie', 'line')
            
        العائد:
            JSON يحتوي على بيانات الرسم البياني
        """
        # تحويل التواريخ إلى كائنات datetime إذا كانت سلاسل نصية
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        # تصفية المعاملات حسب التاريخ
        filtered_transactions = transactions
        if start_date:
            filtered_transactions = [t for t in filtered_transactions if t.date >= start_date]
        if end_date:
            filtered_transactions = [t for t in filtered_transactions if t.date <= end_date]
        
        # تصنيف المعاملات حسب النوع والفئة
        income_by_category = {}
        expense_by_category = {}
        
        for transaction in filtered_transactions:
            if transaction.type == 'income':
                category = transaction.category or 'غير مصنف'
                if category not in income_by_category:
                    income_by_category[category] = 0
                income_by_category[category] += transaction.amount
            elif transaction.type == 'expense':
                category = transaction.category or 'غير مصنف'
                if category not in expense_by_category:
                    expense_by_category[category] = 0
                expense_by_category[category] += transaction.amount
        
        # إنشاء DataFrame للدخل والمصروفات
        income_df = pd.DataFrame([
            {'category': cat, 'amount': amount, 'type': 'دخل'}
            for cat, amount in income_by_category.items()
        ])
        
        expense_df = pd.DataFrame([
            {'category': cat, 'amount': amount, 'type': 'مصروف'}
            for cat, amount in expense_by_category.items()
        ])
        
        # دمج البيانات
        df = pd.concat([income_df, expense_df])
        
        # إنشاء الرسم البياني المناسب
        if chart_type == 'bar':
            fig = px.bar(
                df, 
                x='category', 
                y='amount', 
                color='type',
                title='تحليل الدخل والمصروفات حسب الفئة',
                labels={'category': 'الفئة', 'amount': 'المبلغ', 'type': 'النوع'},
                color_discrete_map={'دخل': '#28a745', 'مصروف': '#dc3545'}
            )
        elif chart_type == 'pie':
            # إنشاء رسمين بيانيين دائريين
            fig = make_subplots(rows=1, cols=2, specs=[[{'type': 'domain'}, {'type': 'domain'}]],
                              subplot_titles=['الدخل حسب الفئة', 'المصروفات حسب الفئة'])
            
            fig.add_trace(go.Pie(
                labels=list(income_by_category.keys()),
                values=list(income_by_category.values()),
                name='الدخل'
            ), 1, 1)
            
            fig.add_trace(go.Pie(
                labels=list(expense_by_category.keys()),
                values=list(expense_by_category.values()),
                name='المصروفات'
            ), 1, 2)
            
            fig.update_layout(title_text='تحليل الدخل والمصروفات')
        elif chart_type == 'line':
            # تجميع المعاملات حسب التاريخ
            transactions_by_date = {}
            
            for transaction in filtered_transactions:
                date_str = transaction.date.strftime('%Y-%m-%d')
                if date_str not in transactions_by_date:
                    transactions_by_date[date_str] = {'income': 0, 'expense': 0}
                
                if transaction.type == 'income':
                    transactions_by_date[date_str]['income'] += transaction.amount
                elif transaction.type == 'expense':
                    transactions_by_date[date_str]['expense'] += transaction.amount
            
            # تحويل إلى DataFrame
            dates = []
            incomes = []
            expenses = []
            
            for date_str, values in sorted(transactions_by_date.items()):
                dates.append(date_str)
                incomes.append(values['income'])
                expenses.append(values['expense'])
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=dates,
                y=incomes,
                mode='lines+markers',
                name='الدخل',
                line=dict(color='#28a745', width=2)
            ))
            fig.add_trace(go.Scatter(
                x=dates,
                y=expenses,
                mode='lines+markers',
                name='المصروفات',
                line=dict(color='#dc3545', width=2)
            ))
            
            fig.update_layout(
                title='تحليل الدخل والمصروفات عبر الزمن',
                xaxis_title='التاريخ',
                yaxis_title='المبلغ'
            )
        
        # تحديث تخطيط الرسم البياني
        fig.update_layout(
            font=dict(family='Arial, sans-serif', size=14),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=50, r=50, t=80, b=50),
            template='plotly_white'
        )
        
        # تحويل الرسم البياني إلى JSON
        chart_json = fig.to_json()
        
        return chart_json
    
    @staticmethod
    def generate_invoice_chart(invoices, chart_type='pie'):
        """
        إنشاء رسم بياني لتحليل الفواتير
        
        المعلمات:
            invoices: قائمة الفواتير
            chart_type: نوع الرسم البياني ('pie', 'bar')
            
        العائد:
            JSON يحتوي على بيانات الرسم البياني
        """
        # تصنيف الفواتير حسب الحالة
        status_counts = {}
        status_amounts = {}
        
        for invoice in invoices:
            status = invoice.status
            if status not in status_counts:
                status_counts[status] = 0
                status_amounts[status] = 0
            
            status_counts[status] += 1
            status_amounts[status] += invoice.total_amount
        
        # تعيين الألوان حسب الحالة
        status_colors = {
            'paid': '#28a745',  # أخضر
            'unpaid': '#dc3545',  # أحمر
            'overdue': '#ffc107',  # أصفر
            'draft': '#6c757d',  # رمادي
            'cancelled': '#343a40',  # أسود
            'partially_paid': '#17a2b8'  # أزرق
        }
        
        if chart_type == 'pie':
            # إنشاء رسمين بيانيين دائريين
            fig = make_subplots(rows=1, cols=2, specs=[[{'type': 'domain'}, {'type': 'domain'}]],
                              subplot_titles=['عدد الفواتير حسب الحالة', 'مبالغ الفواتير حسب الحالة'])
            
            # رسم بياني دائري لعدد الفواتير
            fig.add_trace(go.Pie(
                labels=list(status_counts.keys()),
                values=list(status_counts.values()),
                name='عدد الفواتير',
                marker=dict(colors=[status_colors.get(status, '#007bff') for status in status_counts.keys()])
            ), 1, 1)
            
            # رسم بياني دائري لمبالغ الفواتير
            fig.add_trace(go.Pie(
                labels=list(status_amounts.keys()),
                values=list(status_amounts.values()),
                name='مبالغ الفواتير',
                marker=dict(colors=[status_colors.get(status, '#007bff') for status in status_amounts.keys()])
            ), 1, 2)
            
            fig.update_layout(title_text='تحليل الفواتير حسب الحالة')
        elif chart_type == 'bar':
            # إنشاء DataFrame للفواتير
            status_df = pd.DataFrame([
                {'status': status, 'count': count, 'amount': status_amounts[status]}
                for status, count in status_counts.items()
            ])
            
            # إنشاء رسم بياني شريطي
            fig = make_subplots(rows=1, cols=2, subplot_titles=['عدد الفواتير حسب الحالة', 'مبالغ الفواتير حسب الحالة'])
            
            fig.add_trace(go.Bar(
                x=status_df['status'],
                y=status_df['count'],
                name='عدد الفواتير',
                marker=dict(color=[status_colors.get(status, '#007bff') for status in status_df['status']])
            ), 1, 1)
            
            fig.add_trace(go.Bar(
                x=status_df['status'],
                y=status_df['amount'],
                name='مبالغ الفواتير',
                marker=dict(color=[status_colors.get(status, '#007bff') for status in status_df['status']])
            ), 1, 2)
            
            fig.update_layout(
                title_text='تحليل الفواتير حسب الحالة',
                xaxis_title='الحالة',
                yaxis_title='العدد'
            )
        
        # تحديث تخطيط الرسم البياني
        fig.update_layout(
            font=dict(family='Arial, sans-serif', size=14),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=50, r=50, t=80, b=50),
            template='plotly_white'
        )
        
        # تحويل الرسم البياني إلى JSON
        chart_json = fig.to_json()
        
        return chart_json
    
    @staticmethod
    def generate_client_chart(clients, invoices, transactions, chart_type='bar'):
        """
        إنشاء رسم بياني لتحليل العملاء
        
        المعلمات:
            clients: قائمة العملاء
            invoices: قائمة الفواتير
            transactions: قائمة المعاملات
            chart_type: نوع الرسم البياني ('bar', 'pie')
            
        العائد:
            JSON يحتوي على بيانات الرسم البياني
        """
        # تجميع البيانات حسب العميل
        client_data = {}
        
        for client in clients:
            client_id = client.id
            client_name = client.name
            
            if client_name not in client_data:
                client_data[client_name] = {
                    'invoiced': 0,
                    'paid': 0,
                    'income': 0
                }
            
            # حساب مبالغ الفواتير
            client_invoices = [inv for inv in invoices if inv.client_id == client_id]
            for invoice in client_invoices:
                client_data[client_name]['invoiced'] += invoice.total_amount
                client_data[client_name]['paid'] += invoice.amount_paid
            
            # حساب الدخل من المعاملات
            client_transactions = [t for t in transactions if t.client_id == client_id and t.type == 'income']
            for transaction in client_transactions:
                client_data[client_name]['income'] += transaction.amount
        
        # تحويل البيانات إلى DataFrame
        data = []
        for client_name, values in client_data.items():
            data.append({
                'client': client_name,
                'invoiced': values['invoiced'],
                'paid': values['paid'],
                'income': values['income']
            })
        
        df = pd.DataFrame(data)
        
        # ترتيب البيانات تنازلياً حسب المبلغ المفوتر
        df = df.sort_values('invoiced', ascending=False)
        
        # اختيار أفضل 10 عملاء
        top_clients = df.head(10)
        
        if chart_type == 'bar':
            # إنشاء رسم بياني شريطي
            fig = go.Figure()
            
            fig.add_trace(go.Bar(
                x=top_clients['client'],
                y=top_clients['invoiced'],
                name='المبلغ المفوتر',
                marker=dict(color='#007bff')
            ))
            
            fig.add_trace(go.Bar(
                x=top_clients['client'],
                y=top_clients['paid'],
                name='المبلغ المدفوع',
                marker=dict(color='#28a745')
            ))
            
            fig.add_trace(go.Bar(
                x=top_clients['client'],
                y=top_clients['income'],
                name='الدخل',
                marker=dict(color='#17a2b8')
            ))
            
            fig.update_layout(
                title='أفضل 10 عملاء حسب المبلغ المفوتر',
                xaxis_title='العميل',
                yaxis_title='المبلغ',
                barmode='group'
            )
        elif chart_type == 'pie':
            # إنشاء رسم بياني دائري للمبلغ المفوتر
            fig = go.Figure(data=[go.Pie(
                labels=top_clients['client'],
                values=top_clients['invoiced'],
                hole=.3,
                title='المبلغ المفوتر حسب العميل'
            )])
            
            fig.update_layout(title_text='توزيع المبلغ المفوتر حسب العميل')
        
        # تحديث تخطيط الرسم البياني
        fig.update_layout(
            font=dict(family='Arial, sans-serif', size=14),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=50, r=50, t=80, b=50),
            template='plotly_white'
        )
        
        # تحويل الرسم البياني إلى JSON
        chart_json = fig.to_json()
        
        return chart_json
    
    @staticmethod
    def generate_project_chart(projects, invoices, transactions, chart_type='bar'):
        """
        إنشاء رسم بياني لتحليل المشاريع
        
        المعلمات:
            projects: قائمة المشاريع
            invoices: قائمة الفواتير
            transactions: قائمة المعاملات
            chart_type: نوع الرسم البياني ('bar', 'scatter')
            
        العائد:
            JSON يحتوي على بيانات الرسم البياني
        """
        # تجميع البيانات حسب المشروع
        project_data = {}
        
        for project in projects:
            project_id = project.id
            project_name = project.name
            
            if project_name not in project_data:
                project_data[project_name] = {
                    'income': 0,
                    'expense': 0,
                    'profit': 0,
                    'budget': project.budget or 0
                }
            
            # حساب الدخل من الفواتير
            project_invoices = [inv for inv in invoices if inv.project_id == project_id]
            for invoice in project_invoices:
                project_data[project_name]['income'] += invoice.total_amount
            
            # حساب الدخل والمصروفات من المعاملات
            project_transactions = [t for t in transactions if t.project_id == project_id]
            for transaction in project_transactions:
                if transaction.type == 'income':
                    project_data[project_name]['income'] += transaction.amount
                elif transaction.type == 'expense':
                    project_data[project_name]['expense'] += transaction.amount
            
            # حساب الربح
            project_data[project_name]['profit'] = project_data[project_name]['income'] - project_data[project_name]['expense']
        
        # تحويل البيانات إلى DataFrame
        data = []
        for project_name, values in project_data.items():
            # حساب هامش الربح
            profit_margin = 0
            if values['income'] > 0:
                profit_margin = (values['profit'] / values['income']) * 100
            
            data.append({
                'project': project_name,
                'income': values['income'],
                'expense': values['expense'],
                'profit': values['profit'],
                'budget': values['budget'],
                'profit_margin': profit_margin
            })
        
        df = pd.DataFrame(data)
        
        # ترتيب البيانات تنازلياً حسب الربح
        df = df.sort_values('profit', ascending=False)
        
        # اختيار أفضل 10 مشاريع
        top_projects = df.head(10)
        
        if chart_type == 'bar':
            # إنشاء رسم بياني شريطي
            fig = go.Figure()
            
            fig.add_trace(go.Bar(
                x=top_projects['project'],
                y=top_projects['income'],
                name='الدخل',
                marker=dict(color='#28a745')
            ))
            
            fig.add_trace(go.Bar(
                x=top_projects['project'],
                y=top_projects['expense'],
                name='المصروفات',
                marker=dict(color='#dc3545')
            ))
            
            fig.add_trace(go.Bar(
                x=top_projects['project'],
                y=top_projects['profit'],
                name='الربح',
                marker=dict(color='#007bff')
            ))
            
            fig.update_layout(
                title='أفضل 10 مشاريع حسب الربح',
                xaxis_title='المشروع',
                yaxis_title='المبلغ',
                barmode='group'
            )
        elif chart_type == 'scatter':
            # إنشاء رسم بياني مبعثر
            fig = px.scatter(
                top_projects,
                x='income',
                y='profit',
                size='expense',
                color='profit_margin',
                hover_name='project',
                size_max=60,
                color_continuous_scale=px.colors.sequential.Viridis
            )
            
            fig.update_layout(
                title='تحليل المشاريع (الدخل مقابل الربح)',
                xaxis_title='الدخل',
                yaxis_title='الربح',
                coloraxis_colorbar=dict(title='هامش الربح %')
            )
        
        # تحديث تخطيط الرسم البياني
        fig.update_layout(
            font=dict(family='Arial, sans-serif', size=14),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=50, r=50, t=80, b=50),
            template='plotly_white'
        )
        
        # تحويل الرسم البياني إلى JSON
        chart_json = fig.to_json()
        
        return chart_json
    
    @staticmethod
    def generate_time_series_chart(transactions, period='monthly', start_date=None, end_date=None):
        """
        إنشاء رسم بياني للسلسلة الزمنية للمعاملات
        
        المعلمات:
            transactions: قائمة المعاملات
            period: الفترة الزمنية ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        العائد:
            JSON يحتوي على بيانات الرسم البياني
        """
        # تحويل التواريخ إلى كائنات datetime إذا كانت سلاسل نصية
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        # تصفية المعاملات حسب التاريخ
        filtered_transactions = transactions
        if start_date:
            filtered_transactions = [t for t in filtered_transactions if t.date >= start_date]
        if end_date:
            filtered_transactions = [t for t in filtered_transactions if t.date <= end_date]
        
        # تحديد دالة التجميع حسب الفترة
        if period == 'daily':
            def group_key(date):
                return date.strftime('%Y-%m-%d')
            date_format = '%Y-%m-%d'
        elif period == 'weekly':
            def group_key(date):
                # الأسبوع يبدأ من الاثنين (0)
                return date.strftime('%Y-%U')
            date_format = 'Week %U, %Y'
        elif period == 'monthly':
            def group_key(date):
                return date.strftime('%Y-%m')
            date_format = '%b %Y'
        elif period == 'quarterly':
            def group_key(date):
                quarter = (date.month - 1) // 3 + 1
                return f"{date.year}-Q{quarter}"
            date_format = 'Q%q %Y'
        elif period == 'yearly':
            def group_key(date):
                return str(date.year)
            date_format = '%Y'
        
        # تجميع المعاملات حسب الفترة
        income_by_period = {}
        expense_by_period = {}
        
        for transaction in filtered_transactions:
            period_key = group_key(transaction.date)
            
            if transaction.type == 'income':
                if period_key not in income_by_period:
                    income_by_period[period_key] = 0
                income_by_period[period_key] += transaction.amount
            elif transaction.type == 'expense':
                if period_key not in expense_by_period:
                    expense_by_period[period_key] = 0
                expense_by_period[period_key] += transaction.amount
        
        # إنشاء قائمة بجميع الفترات
        all_periods = sorted(set(list(income_by_period.keys()) + list(expense_by_period.keys())))
        
        # تحضير البيانات للرسم البياني
        periods = []
        incomes = []
        expenses = []
        profits = []
        
        for period_key in all_periods:
            periods.append(period_key)
            income = income_by_period.get(period_key, 0)
            expense = expense_by_period.get(period_key, 0)
            profit = income - expense
            
            incomes.append(income)
            expenses.append(expense)
            profits.append(profit)
        
        # إنشاء الرسم البياني
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=periods,
            y=incomes,
            mode='lines+markers',
            name='الدخل',
            line=dict(color='#28a745', width=2)
        ))
        
        fig.add_trace(go.Scatter(
            x=periods,
            y=expenses,
            mode='lines+markers',
            name='المصروفات',
            line=dict(color='#dc3545', width=2)
        ))
        
        fig.add_trace(go.Scatter(
            x=periods,
            y=profits,
            mode='lines+markers',
            name='الربح',
            line=dict(color='#007bff', width=2)
        ))
        
        # إضافة خط الصفر
        fig.add_shape(
            type="line",
            x0=periods[0],
            y0=0,
            x1=periods[-1],
            y1=0,
            line=dict(color="#6c757d", width=1, dash="dash")
        )
        
        # تحديث تخطيط الرسم البياني
        fig.update_layout(
            title=f'تحليل الدخل والمصروفات والربح ({period})',
            xaxis_title='الفترة',
            yaxis_title='المبلغ',
            font=dict(family='Arial, sans-serif', size=14),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=50, r=50, t=80, b=50),
            template='plotly_white'
        )
        
        # تحويل الرسم البياني إلى JSON
        chart_json = fig.to_json()
        
        return chart_json
    
    @staticmethod
    def calculate_financial_metrics(transactions, invoices, start_date=None, end_date=None):
        """
        حساب المقاييس المالية
        
        المعلمات:
            transactions: قائمة المعاملات
            invoices: قائمة الفواتير
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        العائد:
            قاموس يحتوي على المقاييس المالية
        """
        # تحويل التواريخ إلى كائنات datetime إذا كانت سلاسل نصية
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        # تصفية المعاملات حسب التاريخ
        filtered_transactions = transactions
        if start_date:
            filtered_transactions = [t for t in filtered_transactions if t.date >= start_date]
        if end_date:
            filtered_transactions = [t for t in filtered_transactions if t.date <= end_date]
        
        # تصفية الفواتير حسب التاريخ
        filtered_invoices = invoices
        if start_date:
            filtered_invoices = [inv for inv in filtered_invoices if inv.issue_date >= start_date]
        if end_date:
            filtered_invoices = [inv for inv in filtered_invoices if inv.issue_date <= end_date]
        
        # حساب إجمالي الدخل والمصروفات
        total_income = sum(t.amount for t in filtered_transactions if t.type == 'income')
        total_expense = sum(t.amount for t in filtered_transactions if t.type == 'expense')
        
        # حساب إجمالي الفواتير والمدفوعات
        total_invoiced = sum(inv.total_amount for inv in filtered_invoices)
        total_paid = sum(inv.amount_paid for inv in filtered_invoices)
        total_due = sum(inv.amount_due for inv in filtered_invoices)
        
        # حساب الربح وهامش الربح
        net_profit = total_income - total_expense
        profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
        
        # حساب نسبة التحصيل
        collection_rate = (total_paid / total_invoiced * 100) if total_invoiced > 0 else 0
        
        # حساب متوسط قيمة الفاتورة
        avg_invoice_value = total_invoiced / len(filtered_invoices) if filtered_invoices else 0
        
        # حساب عدد الفواتير حسب الحالة
        invoice_status_counts = {}
        for invoice in filtered_invoices:
            status = invoice.status
            if status not in invoice_status_counts:
                invoice_status_counts[status] = 0
            invoice_status_counts[status] += 1
        
        # تجميع المقاييس
        metrics = {
            'total_income': total_income,
            'total_expense': total_expense,
            'net_profit': net_profit,
            'profit_margin': profit_margin,
            'total_invoiced': total_invoiced,
            'total_paid': total_paid,
            'total_due': total_due,
            'collection_rate': collection_rate,
            'avg_invoice_value': avg_invoice_value,
            'invoice_status_counts': invoice_status_counts,
            'transaction_count': len(filtered_transactions),
            'invoice_count': len(filtered_invoices)
        }
        
        return metrics
    
    @staticmethod
    def render_chart_to_html(chart_json, div_id='chart', width='100%', height='500px'):
        """
        تحويل JSON الرسم البياني إلى HTML
        
        المعلمات:
            chart_json: JSON الرسم البياني
            div_id: معرف عنصر div
            width: عرض الرسم البياني
            height: ارتفاع الرسم البياني
            
        العائد:
            HTML للرسم البياني
        """
        html = f'''
        <div id="{div_id}" style="width:{width};height:{height};"></div>
        <script>
            var plotlyData = {chart_json};
            Plotly.newPlot('{div_id}', plotlyData.data, plotlyData.layout);
        </script>
        '''
        
        return html