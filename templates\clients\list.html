{% extends "base.html" %}

{% block title %}قائمة العملاء{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">قائمة العملاء</h1>
            <p class="text-muted">إدارة جميع العملاء</p>
        </div>
        <div>
            <a href="{{ url_for('clients.add_client') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي العملاء</h6>
                            <h4 class="mb-0">{{ clients|length }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">العملاء النشطون</h6>
                            <h4 class="mb-0">{{ active_clients_count or 0 }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">عملاء جدد هذا الشهر</h6>
                            <h4 class="mb-0">{{ new_clients_count or 0 }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الإيرادات</h6>
                            <h4 class="mb-0">{{ total_revenue|format_currency }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>البحث والفلترة
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ search_query or '' }}"
                        placeholder="البحث بالاسم أو البريد الإلكتروني">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if not status_filter or status_filter=='all' %}selected{% endif %}>جميع
                            الحالات</option>
                        <option value="active" {% if status_filter=='active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter=='inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="city" class="form-label">المدينة</label>
                    <select class="form-select" id="city" name="city">
                        <option value="">جميع المدن</option>
                        {% for city in cities %}
                        <option value="{{ city }}" {% if city_filter==city %}selected{% endif %}>{{ city }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="name" {% if sort_by=='name' %}selected{% endif %}>الاسم</option>
                        <option value="created_at" {% if sort_by=='created_at' %}selected{% endif %}>تاريخ الإضافة
                        </option>
                        <option value="last_invoice" {% if sort_by=='last_invoice' %}selected{% endif %}>آخر فاتورة
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>العملاء ({{ clients|length }})
            </h5>
        </div>
        <div class="card-body p-0">
            {% if clients %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>المدينة</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبلغ</th>
                            <th>آخر فاتورة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        {% if client.profile_image %}
                                        <img src="{{ client.profile_image }}" class="rounded-circle" width="40"
                                            height="40" alt="{{ client.name }}">
                                        {% else %}
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                            style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">{{ client.name[0].upper() }}</span>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <a href="{{ url_for('clients.view_client', client_id=client.id) }}"
                                            class="text-decoration-none">
                                            <strong>{{ client.name }}</strong>
                                        </a>
                                        {% if client.company_name %}
                                        <br><small class="text-muted">{{ client.company_name }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if client.email %}
                                <a href="mailto:{{ client.email }}" class="text-decoration-none">{{ client.email }}</a>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if client.phone %}
                                <a href="tel:{{ client.phone }}" class="text-decoration-none">{{ client.phone }}</a>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ client.city or 'غير محدد' }}</td>
                            <td>
                                <span class="badge bg-info">{{ client.invoices_count or 0 }}</span>
                            </td>
                            <td>{{ client.total_amount|format_currency }}</td>
                            <td>
                                {% if client.last_invoice_date %}
                                {{ client.last_invoice_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                <span class="text-muted">لا توجد فواتير</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if client.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('clients.view_client', client_id=client.id) }}"
                                        class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('clients.edit_client', client_id=client.id) }}"
                                        class="btn btn-outline-secondary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('invoices.add_invoice') }}?client_id={{ client.id }}"
                                        class="btn btn-outline-success" title="إضافة فاتورة">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger"
                                        onclick="confirmDelete('{{ client.id }}', '{{ client.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد عملاء</h5>
                <p class="text-muted">لم يتم العثور على أي عملاء تطابق معايير البحث.</p>
                <a href="{{ url_for('clients.add_client') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف العميل <strong id="clientName"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: سيتم حذف جميع الفواتير والمعاملات المرتبطة بهذا العميل.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function confirmDelete(clientId, clientName) {
        document.getElementById('clientName').textContent = clientName;
        document.getElementById('deleteForm').action = `/clients/${clientId}/delete`;

        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Auto-submit search form on filter change
    document.addEventListener('DOMContentLoaded', function () {
        const filterSelects = document.querySelectorAll('#status, #city, #sort_by');
        filterSelects.forEach(function (select) {
            select.addEventListener('change', function () {
                this.form.submit();
            });
        });
    });
</script>
{% endblock %}