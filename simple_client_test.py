#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط لإضافة عميل
Simple client addition test
"""

from app import create_app
from extensions import db
from models.client import Client
from models.user import User

def simple_client_test():
    """اختبار بسيط لإضافة عميل"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 اختبار إضافة عميل بسيط...")
            
            # التحقق من وجود مستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين في النظام")
                print("🔄 إنشاء مستخدم تجريبي...")
                
                # إنشاء مستخدم تجريبي
                test_user = User(
                    email='<EMAIL>',
                    password='password123',
                    first_name='مدير',
                    last_name='النظام',
                    is_active=True
                )
                db.session.add(test_user)
                db.session.commit()
                user = test_user
                print(f"✅ تم إنشاء مستخدم تجريبي: {user.email}")
            
            print(f"✅ المستخدم: {user.email} (ID: {user.id})")
            
            # إنشاء عميل بسيط
            client_data = {
                'user_id': user.id,
                'name': 'عميل تجريبي بسيط',
                'email': '<EMAIL>',
                'phone': '+966501111111',
                'is_active': True
            }
            
            print("🔄 إنشاء عميل جديد...")
            new_client = Client(**client_data)
            
            print("🔄 إضافة العميل إلى قاعدة البيانات...")
            db.session.add(new_client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل بنجاح! ID: {new_client.id}")
            print(f"   الاسم: {new_client.name}")
            print(f"   البريد: {new_client.email}")
            print(f"   الهاتف: {new_client.phone}")
            print(f"   تاريخ الإنشاء: {new_client.created_at}")
            
            # التحقق من العميل
            saved_client = Client.query.get(new_client.id)
            if saved_client:
                print("✅ تم التحقق من حفظ العميل في قاعدة البيانات")
                
                # عرض جميع العملاء
                all_clients = Client.query.all()
                print(f"\n📊 إجمالي العملاء في النظام: {len(all_clients)}")
                for client in all_clients:
                    print(f"  - {client.name} ({client.email})")
                
                return True
            else:
                print("❌ فشل في حفظ العميل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 بدء الاختبار البسيط...")
    
    success = simple_client_test()
    
    if success:
        print("\n🎉 الاختبار نجح!")
        print("✅ وظيفة إضافة العملاء تعمل بشكل صحيح")
    else:
        print("\n❌ الاختبار فشل")
        print("⚠️ هناك مشكلة في وظيفة إضافة العملاء")
    
    input("\nاضغط Enter للخروج...")
