#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
الصفحة الرئيسية والواجهات العامة
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import logging

# استيراد قاعدة البيانات
from app import db

# استيراد النماذج
from models.user import User
from models.project import Project
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction

# استيراد الوكيل الذكي
from ai_agent.agent import SmartBizAgent
# from ai_agent.analytics import SmartBizAnalytics

# إنشاء مخطط Blueprint
main_bp = Blueprint('main', __name__)

# إعداد السجل
logger = logging.getLogger(__name__)

@main_bp.route('/')
def index():
    """
الصفحة الرئيسية للمستخدمين غير المسجلين
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """
لوحة التحكم الرئيسية
    """
    try:
        # الحصول على إحصائيات عامة
        today = datetime.now().date()
        start_of_month = datetime(today.year, today.month, 1).date()
        end_of_month = (datetime(today.year, today.month + 1, 1) - timedelta(days=1)).date() if today.month < 12 else datetime(today.year, 12, 31).date()
        
        # عدد العملاء والمشاريع والفواتير
        clients_count = Client.query.filter_by(user_id=current_user.id).count()
        projects_count = Project.query.filter_by(user_id=current_user.id).count()
        invoices_count = Invoice.query.filter_by(user_id=current_user.id).count()
        
        # الفواتير غير المدفوعة
        unpaid_invoices = Invoice.query.filter_by(
            user_id=current_user.id, 
            status='unpaid'
        ).order_by(Invoice.due_date.asc()).limit(5).all()
        
        # إجمالي المبيعات والمدفوعات للشهر الحالي
        monthly_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_of_month,
            Invoice.issue_date <= end_of_month
        ).all()
        
        monthly_sales = sum(invoice.total_amount for invoice in monthly_invoices)
        
        monthly_payments = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_of_month,
            Transaction.date <= end_of_month,
            Transaction.transaction_type == 'income'
        ).with_entities(Transaction.amount).all()
        
        monthly_payments_total = sum(payment.amount for payment in monthly_payments)
        
        # أحدث المعاملات
        recent_transactions = Transaction.query.filter_by(
            user_id=current_user.id
        ).order_by(Transaction.date.desc()).limit(5).all()
        
        # أحدث العملاء
        recent_clients = Client.query.filter_by(
            user_id=current_user.id
        ).order_by(Client.created_at.desc()).limit(5).all()
        
        # تحميل الرسوم البيانية
        try:
            # analytics = SmartBizAnalytics(db)
            # sales_chart = analytics.generate_sales_chart(current_user.id)
            # cash_flow_chart = analytics.generate_cash_flow_chart(current_user.id)
            sales_chart = None
            cash_flow_chart = None
        except Exception as e:
            logger.error(f"Error generating charts: {str(e)}")
            sales_chart = None
            cash_flow_chart = None
        
        # تحميل توصيات الوكيل الذكي
        try:
            agent = SmartBizAgent(db)
            insights = agent.get_business_insights(current_user.id)
        except Exception as e:
            logger.error(f"Error loading AI insights: {str(e)}")
            insights = ["لا توجد توصيات متاحة حاليًا."]

        # حساب عدد الإشعارات
        try:
            today = datetime.now().date()
            upcoming_due_date = today + timedelta(days=7)

            upcoming_invoices_count = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.status == 'unpaid',
                Invoice.due_date <= upcoming_due_date,
                Invoice.due_date >= today
            ).count()

            overdue_invoices_count = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.status == 'unpaid',
                Invoice.due_date < today
            ).count()

            notifications_count = upcoming_invoices_count + overdue_invoices_count
        except Exception as e:
            logger.error(f"Error calculating notifications: {str(e)}")
            notifications_count = 0

        # إحصائيات للقالب
        try:
            # حساب إجمالي الإيرادات
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
                Invoice.user_id == current_user.id,
                Invoice.status == 'paid'
            ).scalar() or 0

            # حساب إجمالي الفواتير المدفوعة وغير المدفوعة
            paid_invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.status == 'paid'
            ).count()

            unpaid_invoices_count = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.status == 'unpaid'
            ).count()

            # حساب المشاريع المكتملة وقيد التنفيذ
            completed_projects = Project.query.filter(
                Project.user_id == current_user.id,
                Project.status == 'completed'
            ).count()

            in_progress_projects = Project.query.filter(
                Project.user_id == current_user.id,
                Project.status == 'in_progress'
            ).count()

            # حساب العملاء الجدد هذا الشهر
            start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            new_clients = Client.query.filter(
                Client.user_id == current_user.id,
                Client.created_at >= start_of_month
            ).count()

            stats = {
                'total_revenue': total_revenue,
                'revenue_change': 0,  # يمكن حسابها لاحقاً
                'total_invoices': invoices_count,
                'paid_invoices': paid_invoices,
                'unpaid_invoices': unpaid_invoices_count,
                'total_clients': clients_count,
                'new_clients': new_clients,
                'total_projects': projects_count,
                'completed_projects': completed_projects,
                'in_progress_projects': in_progress_projects
            }
        except Exception as e:
            logger.error(f"Error calculating stats: {str(e)}")
            stats = {
                'total_revenue': 0,
                'revenue_change': 0,
                'total_invoices': 0,
                'paid_invoices': 0,
                'unpaid_invoices': 0,
                'total_clients': 0,
                'new_clients': 0,
                'total_projects': 0,
                'completed_projects': 0,
                'in_progress_projects': 0
            }

        return render_template(
            'dashboard/index.html',
            clients_count=clients_count,
            projects_count=projects_count,
            invoices_count=invoices_count,
            unpaid_invoices=unpaid_invoices,
            monthly_sales=monthly_sales,
            monthly_payments=monthly_payments_total,
            recent_transactions=recent_transactions,
            recent_clients=recent_clients,
            sales_chart=sales_chart,
            cash_flow_chart=cash_flow_chart,
            insights=insights,
            notifications_count=notifications_count,
            stats=stats
        )
        
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم. يرجى المحاولة مرة أخرى.', 'danger')
        # إرجاع قيم افتراضية في حالة الخطأ
        return render_template(
            'dashboard/index.html',
            clients_count=0,
            projects_count=0,
            invoices_count=0,
            unpaid_invoices=0,
            monthly_sales=0,
            monthly_payments=0,
            recent_transactions=[],
            recent_clients=[],
            sales_chart=None,
            cash_flow_chart=None,
            insights=[],
            notifications_count=0,
            stats={
                'total_revenue': 0,
                'revenue_change': 0,
                'total_invoices': 0,
                'paid_invoices': 0,
                'unpaid_invoices': 0,
                'total_clients': 0,
                'new_clients': 0,
                'total_projects': 0,
                'completed_projects': 0,
                'in_progress_projects': 0
            }
        )

@main_bp.route('/profile')
@login_required
def profile():
    """
صفحة الملف الشخصي للمستخدم
    """
    return render_template('profile.html')

@main_bp.route('/settings')
@login_required
def settings():
    """
صفحة إعدادات الحساب
    """
    return render_template('settings.html')

@main_bp.route('/search')
@login_required
def search():
    """
صفحة البحث
    """
    query = request.args.get('q', '')
    if not query or len(query) < 3:
        flash('يرجى إدخال مصطلح بحث لا يقل عن 3 أحرف.', 'warning')
        return redirect(url_for('main.dashboard'))
    
    try:
        # البحث في العملاء
        clients = Client.query.filter(
            Client.user_id == current_user.id,
            (Client.name.ilike(f'%{query}%') | 
             Client.email.ilike(f'%{query}%') | 
             Client.phone.ilike(f'%{query}%'))
        ).all()
        
        # البحث في المشاريع
        projects = Project.query.filter(
            Project.user_id == current_user.id,
            (Project.name.ilike(f'%{query}%') | 
             Project.description.ilike(f'%{query}%'))
        ).all()
        
        # البحث في الفواتير
        invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            (Invoice.invoice_number.ilike(f'%{query}%') | 
             Invoice.client_notes.ilike(f'%{query}%'))
        ).all()
        
        # البحث في المعاملات
        transactions = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            (Transaction.description.ilike(f'%{query}%'))
        ).all()
        
        return render_template(
            'search_results.html',
            query=query,
            clients=clients,
            projects=projects,
            invoices=invoices,
            transactions=transactions
        )
        
    except Exception as e:
        logger.error(f"Error during search: {str(e)}")
        flash('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('main.dashboard'))

@main_bp.route('/api/stats/summary')
@login_required
def api_stats_summary():
    """
واجهة برمجة التطبيقات API للإحصائيات الموجزة
    """
    try:
        # الحصول على إحصائيات عامة
        today = datetime.now().date()
        start_of_month = datetime(today.year, today.month, 1).date()
        end_of_month = (datetime(today.year, today.month + 1, 1) - timedelta(days=1)).date() if today.month < 12 else datetime(today.year, 12, 31).date()
        
        # عدد العملاء والمشاريع والفواتير
        clients_count = Client.query.filter_by(user_id=current_user.id).count()
        projects_count = Project.query.filter_by(user_id=current_user.id).count()
        invoices_count = Invoice.query.filter_by(user_id=current_user.id).count()
        unpaid_invoices_count = Invoice.query.filter_by(user_id=current_user.id, status='unpaid').count()
        
        # إجمالي المبيعات والمدفوعات للشهر الحالي
        monthly_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_of_month,
            Invoice.issue_date <= end_of_month
        ).all()
        
        monthly_sales = sum(invoice.total_amount for invoice in monthly_invoices)
        
        monthly_payments = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_of_month,
            Transaction.date <= end_of_month,
            Transaction.transaction_type == 'income'
        ).with_entities(Transaction.amount).all()
        
        monthly_payments_total = sum(payment.amount for payment in monthly_payments)
        
        # إجمالي المبيعات والمدفوعات للسنة الحالية
        start_of_year = datetime(today.year, 1, 1).date()
        end_of_year = datetime(today.year, 12, 31).date()
        
        yearly_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_of_year,
            Invoice.issue_date <= end_of_year
        ).all()
        
        yearly_sales = sum(invoice.total_amount for invoice in yearly_invoices)
        
        yearly_payments = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_of_year,
            Transaction.date <= end_of_year,
            Transaction.transaction_type == 'income'
        ).with_entities(Transaction.amount).all()
        
        yearly_payments_total = sum(payment.amount for payment in yearly_payments)
        
        return jsonify({
            'status': 'success',
            'data': {
                'clients_count': clients_count,
                'projects_count': projects_count,
                'invoices_count': invoices_count,
                'unpaid_invoices_count': unpaid_invoices_count,
                'monthly_sales': monthly_sales,
                'monthly_payments': monthly_payments_total,
                'yearly_sales': yearly_sales,
                'yearly_payments': yearly_payments_total
            }
        })
        
    except Exception as e:
        logger.error(f"API Error - stats summary: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الإحصائيات'
        }), 500

@main_bp.route('/api/notifications')
@login_required
def api_notifications():
    """
واجهة برمجة التطبيقات API للإشعارات
    """
    try:
        # الحصول على الفواتير المستحقة قريبًا
        today = datetime.now().date()
        upcoming_due_date = today + timedelta(days=7)  # الفواتير المستحقة خلال 7 أيام
        
        upcoming_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.status == 'unpaid',
            Invoice.due_date <= upcoming_due_date,
            Invoice.due_date >= today
        ).order_by(Invoice.due_date.asc()).all()
        
        # الفواتير المتأخرة
        overdue_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.status == 'unpaid',
            Invoice.due_date < today
        ).order_by(Invoice.due_date.asc()).all()
        
        # تحويل البيانات إلى تنسيق JSON
        notifications = []
        
        for invoice in upcoming_invoices:
            days_remaining = (invoice.due_date - today).days
            client = Client.query.get(invoice.client_id)
            notifications.append({
                'type': 'upcoming_invoice',
                'title': f'فاتورة مستحقة قريبًا',
                'message': f'الفاتورة رقم {invoice.invoice_number} للعميل {client.name} مستحقة خلال {days_remaining} أيام',
                'date': invoice.due_date.strftime('%Y-%m-%d'),
                'link': url_for('invoices.view_invoice', invoice_id=invoice.id),
                'priority': 'medium'
            })
        
        for invoice in overdue_invoices:
            days_overdue = (today - invoice.due_date).days
            client = Client.query.get(invoice.client_id)
            notifications.append({
                'type': 'overdue_invoice',
                'title': f'فاتورة متأخرة',
                'message': f'الفاتورة رقم {invoice.invoice_number} للعميل {client.name} متأخرة بـ {days_overdue} أيام',
                'date': invoice.due_date.strftime('%Y-%m-%d'),
                'link': url_for('invoices.view_invoice', invoice_id=invoice.id),
                'priority': 'high'
            })
        
        # ترتيب الإشعارات حسب الأولوية والتاريخ
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        notifications.sort(key=lambda x: (priority_order.get(x['priority'], 3), x['date']))
        
        return jsonify({
            'status': 'success',
            'data': {
                'count': len(notifications),
                'notifications': notifications
            }
        })
        
    except Exception as e:
        logger.error(f"API Error - notifications: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الإشعارات'
        }), 500

@main_bp.route('/api/recent-activity')
@login_required
def api_recent_activity():
    """
واجهة برمجة التطبيقات API للأنشطة الأخيرة
    """
    try:
        # الحصول على أحدث المعاملات
        recent_transactions = Transaction.query.filter_by(
            user_id=current_user.id
        ).order_by(Transaction.date.desc()).limit(10).all()
        
        # الحصول على أحدث الفواتير
        recent_invoices = Invoice.query.filter_by(
            user_id=current_user.id
        ).order_by(Invoice.issue_date.desc()).limit(10).all()
        
        # دمج وترتيب الأنشطة
        activities = []
        
        for transaction in recent_transactions:
            client = Client.query.get(transaction.client_id) if transaction.client_id else None
            client_name = client.name if client else 'غير محدد'
            
            activities.append({
                'type': 'transaction',
                'date': transaction.date,
                'description': f"{'دفعة واردة' if transaction.transaction_type == 'income' else 'مصروفات'}: {transaction.description}",
                'amount': transaction.amount,
                'entity': client_name,
                'link': url_for('transactions.view_transaction', transaction_id=transaction.id)
            })
        
        for invoice in recent_invoices:
            client = Client.query.get(invoice.client_id)
            activities.append({
                'type': 'invoice',
                'date': invoice.issue_date,
                'description': f"فاتورة جديدة: {invoice.invoice_number}",
                'amount': invoice.total_amount,
                'entity': client.name,
                'link': url_for('invoices.view_invoice', invoice_id=invoice.id)
            })
        
        # ترتيب الأنشطة حسب التاريخ
        activities.sort(key=lambda x: x['date'], reverse=True)
        
        # تحويل التواريخ إلى نصوص
        for activity in activities:
            activity['date'] = activity['date'].strftime('%Y-%m-%d')
        
        return jsonify({
            'status': 'success',
            'data': activities[:10]  # إرجاع أحدث 10 أنشطة فقط
        })
        
    except Exception as e:
        logger.error(f"API Error - recent activity: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الأنشطة الأخيرة'
        }), 500

@main_bp.errorhandler(404)
def page_not_found(e):
    """
صفحة الخطأ 404
    """
    return render_template('errors/404.html'), 404

@main_bp.errorhandler(500)
def internal_server_error(e):
    """
صفحة الخطأ 500
    """
    return render_template('errors/500.html'), 500