# SmartBiz Accounting - نظام المحاسبة الذكي
# Dockerfile للتطبيق

# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    APP_HOME=/app

# تعيين مجلد العمل
WORKDIR $APP_HOME

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    tcl8.6-dev \
    tk8.6-dev \
    python3-tk \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    tesseract-ocr \
    tesseract-ocr-ara \
    tesseract-ocr-eng \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgtk-3-0 \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير جذر
RUN groupadd -r smartbiz && useradd -r -g smartbiz smartbiz

# نسخ ملفات المتطلبات
COPY requirements.txt .
COPY requirements-dev.txt .

# ترقية pip وتثبيت المتطلبات
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p logs uploads static/uploads media && \
    chown -R smartbiz:smartbiz $APP_HOME

# تعيين الأذونات
RUN chmod +x app.py

# التبديل إلى المستخدم غير الجذر
USER smartbiz

# تعيين متغيرات البيئة للتطبيق
ENV FLASK_APP=app.py \
    FLASK_ENV=production \
    PYTHONPATH=$APP_HOME

# كشف المنفذ
EXPOSE 5000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# أمر تشغيل التطبيق
CMD ["python", "app.py"]

# إضافة تسميات للصورة
LABEL maintainer="SmartBiz Team" \
      version="1.0.0" \
      description="SmartBiz Accounting - نظام المحاسبة الذكي" \
      org.opencontainers.image.title="SmartBiz Accounting" \
      org.opencontainers.image.description="نظام محاسبة ذكي مدعوم بالذكاء الاصطناعي" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="SmartBiz Team" \
      org.opencontainers.image.licenses="MIT"
