# SmartBiz Accounting - نظام المحاسبة الذكي
# Makefile لإدارة المشروع

.PHONY: help install install-dev run test lint format clean docker-build docker-run docker-stop backup restore

# المتغيرات
PYTHON := python3
PIP := pip3
FLASK := flask
DOCKER := docker
DOCKER_COMPOSE := docker-compose

# الألوان للإخراج
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# الهدف الافتراضي
help: ## عرض قائمة الأوامر المتاحة
	@echo "$(BLUE)SmartBiz Accounting - نظام المحاسبة الذكي$(NC)"
	@echo "$(BLUE)======================================$(NC)"
	@echo ""
	@echo "$(GREEN)الأوامر المتاحة:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# إعداد البيئة
install: ## تثبيت المتطلبات الأساسية
	@echo "$(GREEN)تثبيت المتطلبات الأساسية...$(NC)"
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements.txt
	@echo "$(GREEN)تم تثبيت المتطلبات بنجاح!$(NC)"

install-dev: ## تثبيت متطلبات التطوير
	@echo "$(GREEN)تثبيت متطلبات التطوير...$(NC)"
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements-dev.txt
	@echo "$(GREEN)تم تثبيت متطلبات التطوير بنجاح!$(NC)"

# تشغيل التطبيق
run: ## تشغيل التطبيق في وضع التطوير
	@echo "$(GREEN)تشغيل التطبيق...$(NC)"
	$(PYTHON) app.py

run-prod: ## تشغيل التطبيق في وضع الإنتاج
	@echo "$(GREEN)تشغيل التطبيق في وضع الإنتاج...$(NC)"
	gunicorn --bind 0.0.0.0:5000 --workers 4 app:app

# قاعدة البيانات
db-init: ## تهيئة قاعدة البيانات
	@echo "$(GREEN)تهيئة قاعدة البيانات...$(NC)"
	$(FLASK) db init

db-migrate: ## إنشاء ملف هجرة جديد
	@echo "$(GREEN)إنشاء ملف هجرة...$(NC)"
	$(FLASK) db migrate -m "$(msg)"

db-upgrade: ## تطبيق الهجرات على قاعدة البيانات
	@echo "$(GREEN)تطبيق الهجرات...$(NC)"
	$(FLASK) db upgrade

db-downgrade: ## التراجع عن آخر هجرة
	@echo "$(YELLOW)التراجع عن آخر هجرة...$(NC)"
	$(FLASK) db downgrade

# الاختبارات
test: ## تشغيل جميع الاختبارات
	@echo "$(GREEN)تشغيل الاختبارات...$(NC)"
	pytest tests/ -v

test-coverage: ## تشغيل الاختبارات مع تقرير التغطية
	@echo "$(GREEN)تشغيل الاختبارات مع تقرير التغطية...$(NC)"
	pytest tests/ --cov=. --cov-report=html --cov-report=term

test-unit: ## تشغيل اختبارات الوحدة فقط
	@echo "$(GREEN)تشغيل اختبارات الوحدة...$(NC)"
	pytest tests/unit/ -v

test-integration: ## تشغيل اختبارات التكامل فقط
	@echo "$(GREEN)تشغيل اختبارات التكامل...$(NC)"
	pytest tests/integration/ -v

# جودة الكود
lint: ## فحص جودة الكود
	@echo "$(GREEN)فحص جودة الكود...$(NC)"
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

format: ## تنسيق الكود
	@echo "$(GREEN)تنسيق الكود...$(NC)"
	black .
	isort .

format-check: ## فحص تنسيق الكود دون تعديل
	@echo "$(GREEN)فحص تنسيق الكود...$(NC)"
	black --check .
	isort --check-only .

security: ## فحص الأمان
	@echo "$(GREEN)فحص الأمان...$(NC)"
	bandit -r . -x tests/
	safety check

# Docker
docker-build: ## بناء صورة Docker
	@echo "$(GREEN)بناء صورة Docker...$(NC)"
	$(DOCKER) build -t smartbiz-accounting .

docker-run: ## تشغيل التطبيق باستخدام Docker Compose
	@echo "$(GREEN)تشغيل التطبيق باستخدام Docker...$(NC)"
	$(DOCKER_COMPOSE) up -d

docker-stop: ## إيقاف حاويات Docker
	@echo "$(YELLOW)إيقاف حاويات Docker...$(NC)"
	$(DOCKER_COMPOSE) down

docker-logs: ## عرض سجلات Docker
	@echo "$(GREEN)عرض سجلات Docker...$(NC)"
	$(DOCKER_COMPOSE) logs -f

docker-clean: ## تنظيف صور وحاويات Docker
	@echo "$(YELLOW)تنظيف Docker...$(NC)"
	$(DOCKER) system prune -f
	$(DOCKER) volume prune -f

# النسخ الاحتياطي والاستعادة
backup: ## إنشاء نسخة احتياطية من قاعدة البيانات
	@echo "$(GREEN)إنشاء نسخة احتياطية...$(NC)"
	mkdir -p backups
	$(DOCKER_COMPOSE) exec db pg_dump -U smartbiz smartbiz_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql

restore: ## استعادة قاعدة البيانات من نسخة احتياطية
	@echo "$(YELLOW)استعادة قاعدة البيانات...$(NC)"
	@echo "$(RED)تحذير: هذا سيحذف البيانات الحالية!$(NC)"
	@read -p "هل أنت متأكد؟ (y/N): " confirm && [ "$$confirm" = "y" ]
	$(DOCKER_COMPOSE) exec db psql -U smartbiz -d smartbiz_db < $(file)

# التنظيف
clean: ## تنظيف الملفات المؤقتة
	@echo "$(GREEN)تنظيف الملفات المؤقتة...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf dist/
	rm -rf build/

clean-logs: ## تنظيف ملفات السجل
	@echo "$(GREEN)تنظيف ملفات السجل...$(NC)"
	rm -rf logs/*.log
	rm -rf *.log

# التطوير
dev-setup: install-dev db-init db-upgrade ## إعداد بيئة التطوير الكاملة
	@echo "$(GREEN)تم إعداد بيئة التطوير بنجاح!$(NC)"

dev-reset: clean db-downgrade db-upgrade ## إعادة تعيين بيئة التطوير
	@echo "$(GREEN)تم إعادة تعيين بيئة التطوير!$(NC)"

# الإنتاج
deploy: ## نشر التطبيق
	@echo "$(GREEN)نشر التطبيق...$(NC)"
	$(DOCKER_COMPOSE) -f docker-compose.yml -f docker-compose.prod.yml up -d

deploy-stop: ## إيقاف التطبيق المنشور
	@echo "$(YELLOW)إيقاف التطبيق المنشور...$(NC)"
	$(DOCKER_COMPOSE) -f docker-compose.yml -f docker-compose.prod.yml down

# المراقبة
monitor: ## تشغيل أدوات المراقبة
	@echo "$(GREEN)تشغيل أدوات المراقبة...$(NC)"
	$(DOCKER_COMPOSE) --profile monitoring up -d

monitor-stop: ## إيقاف أدوات المراقبة
	@echo "$(YELLOW)إيقاف أدوات المراقبة...$(NC)"
	$(DOCKER_COMPOSE) --profile monitoring down

# البحث
search: ## تشغيل خدمات البحث
	@echo "$(GREEN)تشغيل خدمات البحث...$(NC)"
	$(DOCKER_COMPOSE) --profile search up -d

search-stop: ## إيقاف خدمات البحث
	@echo "$(YELLOW)إيقاف خدمات البحث...$(NC)"
	$(DOCKER_COMPOSE) --profile search down

# التحديث
update: ## تحديث المتطلبات
	@echo "$(GREEN)تحديث المتطلبات...$(NC)"
	$(PIP) install --upgrade -r requirements.txt

update-dev: ## تحديث متطلبات التطوير
	@echo "$(GREEN)تحديث متطلبات التطوير...$(NC)"
	$(PIP) install --upgrade -r requirements-dev.txt

# المساعدة
docs: ## إنشاء التوثيق
	@echo "$(GREEN)إنشاء التوثيق...$(NC)"
	sphinx-build -b html docs/ docs/_build/

docs-serve: ## تشغيل خادم التوثيق
	@echo "$(GREEN)تشغيل خادم التوثيق...$(NC)"
	cd docs/_build/html && python -m http.server 8000

# الإحصائيات
stats: ## عرض إحصائيات المشروع
	@echo "$(BLUE)إحصائيات المشروع:$(NC)"
	@echo "$(GREEN)عدد ملفات Python:$(NC) $(shell find . -name "*.py" | wc -l)"
	@echo "$(GREEN)عدد أسطر الكود:$(NC) $(shell find . -name "*.py" -exec wc -l {} + | tail -1 | awk '{print $$1}')"
	@echo "$(GREEN)عدد ملفات HTML:$(NC) $(shell find . -name "*.html" | wc -l)"
	@echo "$(GREEN)عدد ملفات CSS:$(NC) $(shell find . -name "*.css" | wc -l)"
	@echo "$(GREEN)عدد ملفات JavaScript:$(NC) $(shell find . -name "*.js" | wc -l)"

# التحقق الشامل
check: lint format-check security test ## تشغيل جميع فحوصات الجودة
	@echo "$(GREEN)تم تشغيل جميع فحوصات الجودة بنجاح!$(NC)"

# الإعداد السريع
quick-start: install run ## إعداد وتشغيل سريع
	@echo "$(GREEN)تم الإعداد والتشغيل بنجاح!$(NC)"
