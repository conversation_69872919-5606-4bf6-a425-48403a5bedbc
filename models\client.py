# -*- coding: utf-8 -*-

"""
نموذج العميل
Client Model
"""

from datetime import datetime
from extensions import db
from models.payment_method import PaymentMethod

class Client(db.Model):
    """نموذج العميل"""

    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    company_name = db.Column(db.String(100))  # إضافة حقل اسم الشركة
    tax_number = db.Column(db.String(50))
    contact_person = db.Column(db.String(100))
    payment_method = db.Column(db.String(50))  # زيادة الحد الأقصى
    payment_method_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('payment_methods.id'))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_contact = db.Column(db.DateTime, default=datetime.utcnow)  # إضافة حقل آخر اتصال
    is_active = db.Column(db.Boolean, default=True)

    # مفتاح خارجي للمستخدم
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='client', lazy=True)
    
    def __repr__(self):
        return f'<Client {self.name}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'company_name': self.company_name,
            'tax_number': self.tax_number,
            'contact_person': self.contact_person,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_contact': self.last_contact.isoformat() if self.last_contact else None,
            'is_active': self.is_active
        }

    def calculate_total_sales(self):
        """حساب إجمالي المبيعات"""
        try:
            from models.invoice import Invoice
            total = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(client_id=self.id).scalar()
            return float(total) if total else 0.0
        except Exception:
            return 0.0

    def calculate_total_paid(self):
        """حساب إجمالي المبلغ المدفوع"""
        try:
            from models.invoice import Invoice
            total = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(
                client_id=self.id, status='paid'
            ).scalar()
            return float(total) if total else 0.0
        except Exception:
            return 0.0

    def calculate_total_unpaid(self):
        """حساب إجمالي المبلغ غير المدفوع"""
        try:
            from models.invoice import Invoice
            total = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(
                client_id=self.id, status='unpaid'
            ).scalar()
            return float(total) if total else 0.0
        except Exception:
            return 0.0

    def get_unpaid_invoices(self):
        """الحصول على الفواتير غير المدفوعة"""
        try:
            from models.invoice import Invoice
            return Invoice.query.filter_by(client_id=self.id, status='unpaid').all()
        except Exception:
            return []

    def get_payment_history(self):
        """الحصول على تاريخ المدفوعات"""
        try:
            from models.transaction import Transaction
            return Transaction.query.filter_by(client_id=self.id).order_by(Transaction.date.desc()).all()
        except Exception:
            return []

    def update_last_contact(self):
        """تحديث تاريخ آخر اتصال"""
        self.last_contact = datetime.utcnow()
        return self
