# -*- coding: utf-8 -*-

"""
نموذج العميل
Client Model
"""

from datetime import datetime
from extensions import db
from models.payment_method import PaymentMethod

class Client(db.Model):
    """نموذج العميل"""
    
    __tablename__ = 'clients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    contact_person = db.Column(db.String(100))
    payment_method = db.Column(db.String(20))
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_methods.id'))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # مفتاح خارجي للمستخدم
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='client', lazy=True)
    
    def __repr__(self):
        return f'<Client {self.name}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'tax_number': self.tax_number,
            'contact_person': self.contact_person,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active
        }
