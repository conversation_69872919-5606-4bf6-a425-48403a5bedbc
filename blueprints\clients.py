#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة إدارة العملاء
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime
import logging
import json

# استيراد النماذج وقاعدة البيانات
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction
from extensions import db

# إنشاء مخطط Blueprint
clients_bp = Blueprint('clients', __name__, url_prefix='/clients')

# إعداد السجل
logger = logging.getLogger(__name__)

@clients_bp.route('/')
@login_required
def list_clients():
    """
قائمة العملاء
    """
    try:
        # الحصول على جميع العملاء للمستخدم الحالي
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()

        # حساب إجماليات لكل عميل
        total_revenue = 0
        active_clients_count = 0
        new_clients_count = 0

        for client in clients:
            client.total_sales = client.calculate_total_sales() if hasattr(client, 'calculate_total_sales') else 0
            client.total_paid = client.calculate_total_paid() if hasattr(client, 'calculate_total_paid') else 0
            client.total_unpaid = client.calculate_total_unpaid() if hasattr(client, 'calculate_total_unpaid') else 0
            client.invoices_count = Invoice.query.filter_by(client_id=client.id).count()
            client.total_amount = client.total_sales

            # حساب آخر فاتورة
            last_invoice = Invoice.query.filter_by(client_id=client.id).order_by(Invoice.issue_date.desc()).first()
            client.last_invoice_date = last_invoice.issue_date if last_invoice else None

            total_revenue += client.total_sales
            if getattr(client, 'is_active', True):
                active_clients_count += 1

            # عملاء جدد (آخر 30 يوم)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.now() - timedelta(days=30)
            if client.created_at and client.created_at >= thirty_days_ago:
                new_clients_count += 1

        # جمع المدن الفريدة
        cities = list(set([client.city for client in clients if client.city]))

        return render_template(
            'clients/list.html',
            clients=clients,
            total_revenue=total_revenue,
            active_clients_count=active_clients_count,
            new_clients_count=new_clients_count,
            cities=cities,
            search_query=request.args.get('search', ''),
            status_filter=request.args.get('status', 'all'),
            city_filter=request.args.get('city', ''),
            sort_by=request.args.get('sort_by', 'name')
        )

    except Exception as e:
        logger.error(f"Error loading clients list: {str(e)}")
        flash('حدث خطأ أثناء تحميل قائمة العملاء. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template(
            'clients/list.html',
            clients=[],
            total_revenue=0,
            active_clients_count=0,
            new_clients_count=0,
            cities=[],
            search_query='',
            status_filter='all',
            city_filter='',
            sort_by='name'
        )

@clients_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_client():
    """
إضافة عميل جديد
    """
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        company = request.form.get('company')
        payment_method = request.form.get('payment_method')
        notes = request.form.get('notes')
        
        if not name:
            flash('اسم العميل مطلوب.', 'danger')
            return render_template('clients/add.html')
        
        try:
            # إنشاء عميل جديد
            new_client = Client(
                user_id=current_user.id,
                name=name,
                email=email,
                phone=phone,
                address=address,
                company_name=company,
                payment_method=payment_method,
                notes=notes,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                last_contact=datetime.now()
            )
            
            db.session.add(new_client)
            db.session.commit()
            
            flash(f'تمت إضافة العميل {name} بنجاح.', 'success')
            return redirect(url_for('clients.view_client', client_id=new_client.id))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error adding client: {str(e)}")
            flash('حدث خطأ أثناء إضافة العميل. يرجى المحاولة مرة أخرى.', 'danger')
    
    return render_template('clients/add.html')

@clients_bp.route('/<int:client_id>')
@login_required
def view_client(client_id):
    """
عرض تفاصيل العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first_or_404()
        
        # الحصول على الفواتير والمعاملات
        invoices = Invoice.query.filter_by(client_id=client.id).order_by(Invoice.issue_date.desc()).all()
        transactions = Transaction.query.filter_by(client_id=client.id).order_by(Transaction.date.desc()).all()
        
        # حساب الإحصائيات
        total_sales = client.calculate_total_sales()
        total_paid = client.calculate_total_paid()
        total_unpaid = client.calculate_total_unpaid()
        unpaid_invoices = client.get_unpaid_invoices()
        payment_history = client.get_payment_history()
        
        return render_template(
            'clients/view.html',
            client=client,
            invoices=invoices,
            transactions=transactions,
            total_sales=total_sales,
            total_paid=total_paid,
            total_unpaid=total_unpaid,
            unpaid_invoices=unpaid_invoices,
            payment_history=payment_history
        )
        
    except Exception as e:
        logger.error(f"Error viewing client: {str(e)}")
        flash('حدث خطأ أثناء عرض تفاصيل العميل. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('clients.clients_list'))

@clients_bp.route('/<int:client_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(client_id):
    """
تعديل بيانات العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first_or_404()
        
        if request.method == 'POST':
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')
            address = request.form.get('address')
            company = request.form.get('company')
            payment_method = request.form.get('payment_method')
            notes = request.form.get('notes')
            
            if not name:
                flash('اسم العميل مطلوب.', 'danger')
                return render_template('clients/edit.html', client=client)
            
            try:
                # تحديث بيانات العميل
                client.name = name
                client.email = email
                client.phone = phone
                client.address = address
                client.company_name = company
                client.payment_method = payment_method
                client.notes = notes
                client.updated_at = datetime.now()
                
                db.session.commit()
                
                flash(f'تم تحديث بيانات العميل {name} بنجاح.', 'success')
                return redirect(url_for('clients.view_client', client_id=client.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating client: {str(e)}")
                flash('حدث خطأ أثناء تحديث بيانات العميل. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template('clients/edit.html', client=client)
        
    except Exception as e:
        logger.error(f"Error loading client for edit: {str(e)}")
        flash('حدث خطأ أثناء تحميل بيانات العميل. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('clients.clients_list'))

@clients_bp.route('/<int:client_id>/delete', methods=['POST'])
@login_required
def delete_client(client_id):
    """
حذف العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first_or_404()
        
        # التحقق من وجود فواتير أو معاملات مرتبطة
        invoices_count = Invoice.query.filter_by(client_id=client.id).count()
        transactions_count = Transaction.query.filter_by(client_id=client.id).count()
        
        if invoices_count > 0 or transactions_count > 0:
            flash('لا يمكن حذف العميل لأنه مرتبط بفواتير أو معاملات.', 'danger')
            return redirect(url_for('clients.view_client', client_id=client.id))
        
        # حذف العميل
        db.session.delete(client)
        db.session.commit()
        
        flash(f'تم حذف العميل {client.name} بنجاح.', 'success')
        return redirect(url_for('clients.clients_list'))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting client: {str(e)}")
        flash('حدث خطأ أثناء حذف العميل. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('clients.clients_list'))

@clients_bp.route('/<int:client_id>/update-contact', methods=['POST'])
@login_required
def update_last_contact(client_id):
    """
تحديث تاريخ آخر اتصال
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first_or_404()
        
        # تحديث تاريخ آخر اتصال
        client.update_last_contact()
        db.session.commit()
        
        flash(f'تم تحديث تاريخ آخر اتصال للعميل {client.name} بنجاح.', 'success')
        return redirect(url_for('clients.view_client', client_id=client.id))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating last contact: {str(e)}")
        flash('حدث خطأ أثناء تحديث تاريخ آخر اتصال. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('clients.view_client', client_id=client_id))

@clients_bp.route('/api/list')
@login_required
def api_clients_list():
    """
واجهة برمجة التطبيقات API لقائمة العملاء
    """
    try:
        # الحصول على جميع العملاء للمستخدم الحالي
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        
        # تحويل البيانات إلى تنسيق JSON
        clients_data = [client.to_dict() for client in clients]
        
        return jsonify({
            'status': 'success',
            'data': clients_data
        })
        
    except Exception as e:
        logger.error(f"API Error - clients list: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب قائمة العملاء'
        }), 500

@clients_bp.route('/api/<int:client_id>')
@login_required
def api_client_details(client_id):
    """
واجهة برمجة التطبيقات API لتفاصيل العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
        
        if not client:
            return jsonify({
                'status': 'error',
                'message': 'العميل غير موجود'
            }), 404
        
        # الحصول على الفواتير والمعاملات
        invoices = Invoice.query.filter_by(client_id=client.id).order_by(Invoice.issue_date.desc()).all()
        transactions = Transaction.query.filter_by(client_id=client.id).order_by(Transaction.date.desc()).all()
        
        # تحويل البيانات إلى تنسيق JSON
        client_data = client.to_dict()
        client_data['invoices'] = [invoice.to_dict() for invoice in invoices]
        client_data['transactions'] = [transaction.to_dict() for transaction in transactions]
        client_data['total_sales'] = client.calculate_total_sales()
        client_data['total_paid'] = client.calculate_total_paid()
        client_data['total_unpaid'] = client.calculate_total_unpaid()
        
        return jsonify({
            'status': 'success',
            'data': client_data
        })
        
    except Exception as e:
        logger.error(f"API Error - client details: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب تفاصيل العميل'
        }), 500

@clients_bp.route('/api/add', methods=['POST'])
@login_required
def api_add_client():
    """
واجهة برمجة التطبيقات API لإضافة عميل
    """
    try:
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'name' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # إنشاء عميل جديد
        new_client = Client(
            user_id=current_user.id,
            name=data.get('name'),
            email=data.get('email'),
            phone=data.get('phone'),
            address=data.get('address'),
            company_name=data.get('company'),
            payment_method=data.get('payment_method'),
            notes=data.get('notes'),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            last_contact=datetime.now()
        )
        
        db.session.add(new_client)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تمت إضافة العميل {new_client.name} بنجاح.',
            'data': new_client.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - add client: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إضافة العميل'
        }), 500

@clients_bp.route('/api/<int:client_id>/update', methods=['PUT'])
@login_required
def api_update_client(client_id):
    """
واجهة برمجة التطبيقات API لتحديث بيانات العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
        
        if not client:
            return jsonify({
                'status': 'error',
                'message': 'العميل غير موجود'
            }), 404
        
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'لم يتم توفير بيانات للتحديث'
            }), 400
        
        # تحديث بيانات العميل
        if 'name' in data:
            client.name = data['name']
        if 'email' in data:
            client.email = data['email']
        if 'phone' in data:
            client.phone = data['phone']
        if 'address' in data:
            client.address = data['address']
        if 'company' in data:
            client.company_name = data['company']
        if 'payment_method' in data:
            client.payment_method = data['payment_method']
        if 'notes' in data:
            client.notes = data['notes']
        
        client.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تم تحديث بيانات العميل {client.name} بنجاح.',
            'data': client.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - update client: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء تحديث بيانات العميل'
        }), 500

@clients_bp.route('/api/<int:client_id>/delete', methods=['DELETE'])
@login_required
def api_delete_client(client_id):
    """
واجهة برمجة التطبيقات API لحذف العميل
    """
    try:
        # الحصول على العميل
        client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
        
        if not client:
            return jsonify({
                'status': 'error',
                'message': 'العميل غير موجود'
            }), 404
        
        # التحقق من وجود فواتير أو معاملات مرتبطة
        invoices_count = Invoice.query.filter_by(client_id=client.id).count()
        transactions_count = Transaction.query.filter_by(client_id=client.id).count()
        
        if invoices_count > 0 or transactions_count > 0:
            return jsonify({
                'status': 'error',
                'message': 'لا يمكن حذف العميل لأنه مرتبط بفواتير أو معاملات'
            }), 400
        
        # حذف العميل
        db.session.delete(client)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تم حذف العميل {client.name} بنجاح.'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - delete client: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء حذف العميل'
        }), 500