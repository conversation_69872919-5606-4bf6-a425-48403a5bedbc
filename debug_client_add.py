#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشخيص مشكلة إضافة العملاء
Debug client addition issue
"""

from app import create_app
from extensions import db
from models.client import Client
from models.user import User
import traceback

def debug_client_creation():
    """تشخيص إنشاء عميل جديد"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 بدء تشخيص إضافة العميل...")
            
            # التحقق من وجود مستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين في النظام")
                return False
            
            print(f"✅ المستخدم: {user.email} (ID: {user.id})")
            
            # التحقق من بنية جدول العملاء
            print("\n🔍 فحص بنية جدول العملاء...")
            inspector = db.inspect(db.engine)
            columns = inspector.get_columns('clients')
            
            print("📋 الأعمدة الموجودة:")
            for col in columns:
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                print(f"  - {col['name']}: {col['type']} ({nullable})")
            
            # محاولة إنشاء عميل بسيط
            print("\n🔄 محاولة إنشاء عميل بسيط...")
            
            client_data = {
                'user_id': user.id,
                'name': 'عميل تجريبي للتشخيص',
                'email': '<EMAIL>',
                'phone': '+************',
                'company_name': 'شركة التشخيص',
                'is_active': True
            }
            
            print(f"📊 بيانات العميل: {client_data}")
            
            # إنشاء العميل
            new_client = Client(**client_data)
            print("✅ تم إنشاء كائن العميل")
            
            # إضافة إلى الجلسة
            db.session.add(new_client)
            print("✅ تم إضافة العميل إلى الجلسة")
            
            # حفظ في قاعدة البيانات
            db.session.commit()
            print(f"✅ تم حفظ العميل في قاعدة البيانات بـ ID: {new_client.id}")
            
            # التحقق من الحفظ
            saved_client = Client.query.get(new_client.id)
            if saved_client:
                print("✅ تم التحقق من حفظ العميل")
                print(f"   الاسم: {saved_client.name}")
                print(f"   البريد: {saved_client.email}")
                print(f"   الشركة: {saved_client.company_name}")
                
                # حذف العميل التجريبي
                db.session.delete(saved_client)
                db.session.commit()
                print("✅ تم حذف العميل التجريبي")
                
                return True
            else:
                print("❌ فشل في التحقق من حفظ العميل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في التشخيص: {e}")
            print(f"🔍 نوع الخطأ: {type(e).__name__}")
            print(f"📋 تفاصيل الخطأ:")
            traceback.print_exc()
            db.session.rollback()
            return False

def test_form_data_simulation():
    """محاكاة بيانات النموذج"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔄 محاكاة بيانات النموذج...")
            
            # محاكاة البيانات المرسلة من النموذج
            form_data = {
                'name': 'أحمد محمد علي',
                'email': '<EMAIL>',
                'phone': '+************',
                'address': 'الرياض، المملكة العربية السعودية',
                'company_name': 'شركة الأمل للتجارة',
                'payment_method': 'bank_transfer',
                'notes': 'عميل مهم',
                'is_active': 'on'  # كما يأتي من checkbox
            }
            
            print(f"📊 بيانات النموذج: {form_data}")
            
            # معالجة البيانات كما في blueprint
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين")
                return False
            
            name = form_data.get('name')
            email = form_data.get('email')
            phone = form_data.get('phone')
            address = form_data.get('address')
            company = form_data.get('company_name')
            payment_method = form_data.get('payment_method')
            notes = form_data.get('notes')
            is_active = True if form_data.get('is_active') else False
            
            print(f"📋 البيانات المعالجة:")
            print(f"   name: {name}")
            print(f"   email: {email}")
            print(f"   phone: {phone}")
            print(f"   address: {address}")
            print(f"   company: {company}")
            print(f"   payment_method: {payment_method}")
            print(f"   notes: {notes}")
            print(f"   is_active: {is_active}")
            
            # إنشاء العميل
            new_client = Client(
                user_id=user.id,
                name=name,
                email=email if email else None,
                phone=phone if phone else None,
                address=address if address else None,
                company_name=company if company else None,
                payment_method=payment_method if payment_method else None,
                notes=notes if notes else None,
                is_active=is_active
            )
            
            print("✅ تم إنشاء كائن العميل")
            
            db.session.add(new_client)
            db.session.commit()
            
            print(f"✅ تم حفظ العميل بـ ID: {new_client.id}")
            
            # حذف العميل التجريبي
            db.session.delete(new_client)
            db.session.commit()
            print("✅ تم حذف العميل التجريبي")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة النموذج: {e}")
            print(f"🔍 نوع الخطأ: {type(e).__name__}")
            print(f"📋 تفاصيل الخطأ:")
            traceback.print_exc()
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🚀 بدء تشخيص مشكلة إضافة العملاء...")
    
    success1 = debug_client_creation()
    success2 = test_form_data_simulation()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ لا توجد مشكلة في إنشاء العملاء")
        print("⚠️ المشكلة قد تكون في النموذج أو التوجيه")
    else:
        print("❌ هناك مشكلة في إنشاء العملاء")
        print("🔍 يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
