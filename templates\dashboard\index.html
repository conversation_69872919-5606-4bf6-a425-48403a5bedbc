{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-0">لوحة التحكم</h1>
            <p class="text-muted">مرحبًا، {{ current_user.name }}! هذه نظرة عامة على أعمالك.</p>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">الإيرادات</h5>
                        <div class="icon-circle bg-primary">
                            <i class="fas fa-dollar-sign text-white"></i>
                        </div>
                    </div>
                    <h2 class="mb-1">{{ stats.total_revenue|format_currency }}</h2>
                    <p class="text-{% if stats.revenue_change >= 0 %}success{% else %}danger{% endif %} mb-0">
                        <i class="fas fa-{% if stats.revenue_change >= 0 %}arrow-up{% else %}arrow-down{% endif %} me-1"></i>
                        {{ stats.revenue_change|abs }}% {% if stats.revenue_change >= 0 %}زيادة{% else %}انخفاض{% endif %}
                    </p>
                    <small class="text-muted">مقارنة بالشهر الماضي</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">الفواتير</h5>
                        <div class="icon-circle bg-warning">
                            <i class="fas fa-file-invoice text-white"></i>
                        </div>
                    </div>
                    <h2 class="mb-1">{{ stats.total_invoices }}</h2>
                    <div class="d-flex justify-content-between">
                        <span class="text-success">{{ stats.paid_invoices }} <small>مدفوعة</small></span>
                        <span class="text-danger">{{ stats.unpaid_invoices }} <small>غير مدفوعة</small></span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">العملاء</h5>
                        <div class="icon-circle bg-success">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                    <h2 class="mb-1">{{ stats.total_clients }}</h2>
                    <p class="text-{% if stats.new_clients > 0 %}success{% else %}muted{% endif %} mb-0">
                        {% if stats.new_clients > 0 %}
                            <i class="fas fa-user-plus me-1"></i> {{ stats.new_clients }} عملاء جدد
                        {% else %}
                            لا يوجد عملاء جدد
                        {% endif %}
                    </p>
                    <small class="text-muted">هذا الشهر</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">المشاريع</h5>
                        <div class="icon-circle bg-info">
                            <i class="fas fa-project-diagram text-white"></i>
                        </div>
                    </div>
                    <h2 class="mb-1">{{ stats.total_projects }}</h2>
                    <div class="progress mb-2" style="height: 6px;">
                        {% set completed_percent = (stats.completed_projects / stats.total_projects * 100) if stats.total_projects > 0 else 0 %}
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ completed_percent }}%;" 
                             aria-valuenow="{{ completed_percent }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-success">{{ stats.completed_projects }} <small>مكتملة</small></span>
                        <span class="text-warning">{{ stats.in_progress_projects }} <small>قيد التنفيذ</small></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4 mb-lg-0">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الإيرادات والمصروفات</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="revenueTimeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            هذا العام
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="revenueTimeDropdown">
                            <li><a class="dropdown-item" href="#">هذا الشهر</a></li>
                            <li><a class="dropdown-item active" href="#">هذا العام</a></li>
                            <li><a class="dropdown-item" href="#">العام الماضي</a></li>
                            <li><a class="dropdown-item" href="#">كل الوقت</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">توزيع الإيرادات</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="distributionTimeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            هذا العام
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="distributionTimeDropdown">
                            <li><a class="dropdown-item" href="#">هذا الشهر</a></li>
                            <li><a class="dropdown-item active" href="#">هذا العام</a></li>
                            <li><a class="dropdown-item" href="#">العام الماضي</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <canvas id="distributionChart" height="260"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity and Upcoming Tasks -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4 mb-lg-0">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">النشاط الأخير</h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for activity in recent_activities %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ activity.title }}</h6>
                                <small class="text-muted">{{ activity.time_ago }}</small>
                            </div>
                            <p class="mb-1">{{ activity.description }}</p>
                            <small class="text-muted">{{ activity.user }}</small>
                        </div>
                        {% else %}
                        <div class="list-group-item text-center py-4">
                            <p class="text-muted mb-0">لا يوجد نشاط حديث</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المهام القادمة</h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for task in upcoming_tasks %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="" id="task{{ loop.index }}">
                                    <label class="form-check-label" for="task{{ loop.index }}">
                                        {{ task.title }}
                                    </label>
                                </div>
                                <span class="badge bg-{{ task.priority_color }}">{{ task.priority }}</span>
                            </div>
                            <p class="mb-1 ms-4">{{ task.description }}</p>
                            <div class="d-flex justify-content-between align-items-center ms-4">
                                <small class="text-muted">تاريخ الاستحقاق: {{ task.due_date }}</small>
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-edit"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="list-group-item text-center py-4">
                            <p class="text-muted mb-0">لا توجد مهام قادمة</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                        <i class="fas fa-plus me-1"></i> إضافة مهمة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Invoices and Clients -->
    <div class="row">
        <div class="col-lg-8 mb-4 mb-lg-0">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الفواتير</h5>
                    <a href="{{ url_for('invoices.list_invoices') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td><a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}">#{{ invoice.number }}</a></td>
                                    <td>{{ invoice.client_name }}</td>
                                    <td>{{ invoice.date }}</td>
                                    <td>{{ invoice.amount|format_currency }}</td>
                                    <td>
                                        <span class="badge bg-{{ invoice.status_color }}">{{ invoice.status }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="btn btn-outline-secondary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}" class="btn btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-success" title="تسجيل دفعة">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">لا توجد فواتير حديثة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث العملاء</h5>
                    <a href="{{ url_for('clients.list_clients') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for client in recent_clients %}
                        <div class="list-group-item">
                            <div class="d-flex align-items-center">
                                <div class="client-avatar me-3">
                                    {% if client.avatar %}
                                    <img src="{{ client.avatar }}" alt="{{ client.name }}" class="rounded-circle" width="40" height="40">
                                    {% else %}
                                    <div class="avatar-placeholder rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        {{ client.name[:1] }}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="client-info">
                                    <h6 class="mb-0"><a href="{{ url_for('clients.view_client', client_id=client.id) }}">{{ client.name }}</a></h6>
                                    <small class="text-muted">{{ client.email }}</small>
                                </div>
                                <div class="ms-auto">
                                    <a href="{{ url_for('clients.view_client', client_id=client.id) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="list-group-item text-center py-4">
                            <p class="text-muted mb-0">لا يوجد عملاء حديثين</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('clients.add_client') }}" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-1"></i> إضافة عميل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTaskModalLabel">إضافة مهمة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="mb-3">
                        <label for="taskTitle" class="form-label">عنوان المهمة</label>
                        <input type="text" class="form-control" id="taskTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">وصف المهمة</label>
                        <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="taskDueDate" class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" class="form-control" id="taskDueDate" required>
                        </div>
                        <div class="col-md-6">
                            <label for="taskPriority" class="form-label">الأولوية</label>
                            <select class="form-select" id="taskPriority" required>
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="taskAssignee" class="form-label">تعيين إلى</label>
                        <select class="form-select" id="taskAssignee">
                            <option value="" selected>أنا</option>
                            <option value="1">أحمد محمد</option>
                            <option value="2">سارة أحمد</option>
                            <option value="3">محمد علي</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">إضافة المهمة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'الإيرادات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 32000, 35000, 37000, 33000, 40000],
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'المصروفات',
                    data: [8000, 12000, 10000, 15000, 14000, 18000, 17000, 19000, 20000, 22000, 21000, 24000],
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' }).format(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value, index, values) {
                                return new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR', maximumSignificantDigits: 3 }).format(value);
                            }
                        }
                    }
                }
            }
        });
        
        // Distribution Chart
        const distributionCtx = document.getElementById('distributionChart').getContext('2d');
        const distributionChart = new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['خدمات استشارية', 'تطوير برمجيات', 'تصميم', 'تدريب', 'أخرى'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(149, 165, 166, 0.8)'
                    ],
                    borderColor: [
                        'rgba(52, 152, 219, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(149, 165, 166, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${percentage}%`;
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
{% endblock %}