#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة معالجة الصوت والتعرف على الكلام
"""

import os
import tempfile
import wave
from datetime import datetime

# محاولة استيراد المكتبات مع التعامل مع الأخطاء
try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False

class VoiceProcessor:
    """
    فئة لمعالجة الصوت والتعرف على الكلام وتحويل النص إلى كلام
    """
    
    def __init__(self, language='ar-SA'):
        """
        تهيئة معالج الصوت
        
        المعلمات:
            language: اللغة الافتراضية ('ar-SA' للعربية، 'en-US' للإنجليزية)
        """
        self.language = language
        self.recognizer = None
        self.engine = None
        
        # تهيئة محرك التعرف على الكلام
        if SPEECH_RECOGNITION_AVAILABLE:
            try:
                self.recognizer = sr.Recognizer()
                # ضبط إعدادات التعرف على الكلام
                self.recognizer.energy_threshold = 300  # الحد الأدنى للطاقة الصوتية
                self.recognizer.dynamic_energy_threshold = True  # ضبط الحد تلقائيًا
                self.recognizer.pause_threshold = 0.8  # مدة الصمت التي تعتبر نهاية للكلام
            except Exception as e:
                print(f"تحذير: فشل تهيئة محرك التعرف على الكلام: {e}")
        
        # تهيئة محرك تحويل النص إلى كلام
        if PYTTSX3_AVAILABLE:
            try:
                self.engine = pyttsx3.init()
                # ضبط إعدادات محرك تحويل النص إلى كلام
                voices = self.engine.getProperty('voices')
                # محاولة تعيين صوت عربي أو إنجليزي حسب اللغة المحددة
                for voice in voices:
                    if language.startswith('ar') and ('arabic' in voice.name.lower() or 'ar' in voice.id.lower()):
                        self.engine.setProperty('voice', voice.id)
                        break
                    elif language.startswith('en') and ('english' in voice.name.lower() or 'en' in voice.id.lower()):
                        self.engine.setProperty('voice', voice.id)
                        break
                
                # ضبط سرعة الكلام (القيمة الافتراضية هي 200)
                self.engine.setProperty('rate', 180)
                # ضبط حجم الصوت (القيمة بين 0 و 1)
                self.engine.setProperty('volume', 0.8)
            except Exception as e:
                print(f"تحذير: فشل تهيئة محرك تحويل النص إلى كلام: {e}")
    
    def recognize_from_microphone(self, timeout=5, phrase_time_limit=None):
        """
        التعرف على الكلام من الميكروفون
        
        المعلمات:
            timeout: مدة الانتظار للكلام (بالثواني)
            phrase_time_limit: الحد الأقصى لمدة الكلام (بالثواني)
            
        العائد:
            النص المتعرف عليه أو رسالة خطأ
        """
        if not SPEECH_RECOGNITION_AVAILABLE or not self.recognizer:
            return "خطأ: مكتبة التعرف على الكلام غير متاحة. قم بتثبيت 'SpeechRecognition' باستخدام pip."
        
        try:
            with sr.Microphone() as source:
                print("جاري ضبط الضوضاء المحيطة... تحدث بعد الصوت.")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                print("جاري الاستماع...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
                print("تم التقاط الصوت، جاري التعرف على الكلام...")
            
            # محاولة التعرف على الكلام باستخدام خدمة Google
            text = self.recognizer.recognize_google(audio, language=self.language)
            return text
        except sr.WaitTimeoutError:
            return "خطأ: انتهت مهلة الانتظار دون سماع أي كلام."
        except sr.UnknownValueError:
            return "خطأ: لم يتمكن النظام من التعرف على الكلام."
        except sr.RequestError as e:
            return f"خطأ: حدث خطأ في طلب خدمة التعرف على الكلام: {e}"
        except Exception as e:
            return f"خطأ غير متوقع: {e}"
    
    def recognize_from_file(self, audio_file_path):
        """
        التعرف على الكلام من ملف صوتي
        
        المعلمات:
            audio_file_path: مسار الملف الصوتي
            
        العائد:
            النص المتعرف عليه أو رسالة خطأ
        """
        if not SPEECH_RECOGNITION_AVAILABLE or not self.recognizer:
            return "خطأ: مكتبة التعرف على الكلام غير متاحة. قم بتثبيت 'SpeechRecognition' باستخدام pip."
        
        if not os.path.exists(audio_file_path):
            return f"خطأ: الملف '{audio_file_path}' غير موجود."
        
        try:
            # التعرف على نوع الملف وقراءته
            if audio_file_path.lower().endswith('.wav'):
                with sr.AudioFile(audio_file_path) as source:
                    audio = self.recognizer.record(source)
            else:
                return "خطأ: تنسيق الملف غير مدعوم. يرجى استخدام ملفات WAV."
            
            # محاولة التعرف على الكلام باستخدام خدمة Google
            text = self.recognizer.recognize_google(audio, language=self.language)
            return text
        except sr.UnknownValueError:
            return "خطأ: لم يتمكن النظام من التعرف على الكلام في الملف."
        except sr.RequestError as e:
            return f"خطأ: حدث خطأ في طلب خدمة التعرف على الكلام: {e}"
        except Exception as e:
            return f"خطأ غير متوقع: {e}"
    
    def text_to_speech(self, text, output_file=None):
        """
        تحويل النص إلى كلام
        
        المعلمات:
            text: النص المراد تحويله إلى كلام
            output_file: مسار ملف الإخراج (اختياري)
            
        العائد:
            True إذا نجحت العملية، False إذا فشلت
        """
        if not PYTTSX3_AVAILABLE or not self.engine:
            print("خطأ: مكتبة تحويل النص إلى كلام غير متاحة. قم بتثبيت 'pyttsx3' باستخدام pip.")
            return False
        
        if not text:
            print("خطأ: النص فارغ.")
            return False
        
        try:
            if output_file:
                # حفظ الكلام في ملف
                self.engine.save_to_file(text, output_file)
                self.engine.runAndWait()
                return os.path.exists(output_file)
            else:
                # نطق الكلام مباشرة
                self.engine.say(text)
                self.engine.runAndWait()
                return True
        except Exception as e:
            print(f"خطأ في تحويل النص إلى كلام: {e}")
            return False
    
    def record_audio(self, output_file, duration=5):
        """
        تسجيل الصوت من الميكروفون

        المعلمات:
            output_file: مسار ملف الإخراج
            duration: مدة التسجيل بالثواني

        العائد:
            مسار الملف المسجل أو None في حالة الفشل
        """
        if not SPEECH_RECOGNITION_AVAILABLE:
            print("خطأ: مكتبة التعرف على الكلام غير متاحة. قم بتثبيت 'SpeechRecognition' باستخدام pip.")
            return None

        try:
            with sr.Microphone() as source:
                print(f"جاري التسجيل لمدة {duration} ثوان...")
                audio_data = self.recognizer.record(source, duration=duration)  # type: ignore
            
            # حفظ البيانات الصوتية في ملف WAV
            with open(output_file, "wb") as f:
                f.write(audio_data.get_wav_data())
            
            print(f"تم حفظ التسجيل في {output_file}")
            return output_file
        except Exception as e:
            print(f"خطأ في تسجيل الصوت: {e}")
            return None
    
    def transcribe_audio_to_file(self, audio_file_path, output_text_file):
        """
        تحويل ملف صوتي إلى نص وحفظه في ملف
        
        المعلمات:
            audio_file_path: مسار الملف الصوتي
            output_text_file: مسار ملف النص
            
        العائد:
            True إذا نجحت العملية، False إذا فشلت
        """
        text = self.recognize_from_file(audio_file_path)
        
        if text.startswith("خطأ"):
            print(text)
            return False
        
        try:
            with open(output_text_file, "w", encoding="utf-8") as f:
                f.write(text)
            
            print(f"تم حفظ النص في {output_text_file}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ النص: {e}")
            return False
    
    def dictate_text(self, output_file=None, timeout=None):
        """
        إملاء نص عن طريق التعرف على الكلام من الميكروفون
        
        المعلمات:
            output_file: مسار ملف الإخراج (اختياري)
            timeout: مدة الانتظار للكلام (بالثواني)
            
        العائد:
            النص المملى أو None في حالة الفشل
        """
        print("بدء الإملاء... قل 'انتهى' أو 'توقف' عندما تنتهي.")
        
        full_text = []
        continue_dictation = True
        
        while continue_dictation:
            text = self.recognize_from_microphone(timeout=timeout)
            
            if text.startswith("خطأ"):
                print(text)
                if "انتهت مهلة الانتظار" in text:
                    break
                continue
            
            print(f"تم التعرف على: {text}")
            
            # التحقق من كلمات التوقف
            stop_words = ["انتهى", "توقف", "stop", "end", "finish"]
            if any(word in text.lower() for word in stop_words):
                # إزالة كلمة التوقف من النص
                for word in stop_words:
                    text = text.lower().replace(word, "").strip()
                if text:  # إذا كان هناك نص متبقي بعد إزالة كلمة التوقف
                    full_text.append(text)
                continue_dictation = False
            else:
                full_text.append(text)
        
        result = " ".join(full_text)
        
        if output_file and result:
            try:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(result)
                print(f"تم حفظ النص المملى في {output_file}")
            except Exception as e:
                print(f"خطأ في حفظ النص المملى: {e}")
        
        return result if full_text else None
    
    def create_voice_note(self, note_text, output_dir="voice_notes"):
        """
        إنشاء ملاحظة صوتية من نص
        
        المعلمات:
            note_text: نص الملاحظة
            output_dir: مجلد الإخراج
            
        العائد:
            مسار ملف الملاحظة الصوتية أو None في حالة الفشل
        """
        if not PYTTSX3_AVAILABLE or not self.engine:
            print("خطأ: مكتبة تحويل النص إلى كلام غير متاحة. قم بتثبيت 'pyttsx3' باستخدام pip.")
            return None
        
        if not note_text:
            print("خطأ: نص الملاحظة فارغ.")
            return None
        
        # إنشاء مجلد الإخراج إذا لم يكن موجودًا
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # إنشاء اسم ملف فريد باستخدام الطابع الزمني
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"voice_note_{timestamp}.mp3")
        
        # تحويل النص إلى كلام وحفظه في ملف
        if self.text_to_speech(note_text, output_file):
            return output_file
        else:
            return None
    
    def convert_audio_format(self, input_file, output_format="wav"):
        """
        تحويل تنسيق ملف صوتي
        
        المعلمات:
            input_file: مسار ملف الإدخال
            output_format: تنسيق الإخراج (حاليًا يدعم فقط 'wav')
            
        العائد:
            مسار ملف الإخراج أو None في حالة الفشل
        """
        if not os.path.exists(input_file):
            print(f"خطأ: الملف '{input_file}' غير موجود.")
            return None
        
        if output_format.lower() != "wav":
            print("خطأ: التنسيق المطلوب غير مدعوم حاليًا. يدعم فقط تنسيق WAV.")
            return None
        
        try:
            # إنشاء اسم ملف الإخراج
            output_file = os.path.splitext(input_file)[0] + "." + output_format.lower()
            
            # استخدام مكتبة wave لتحويل الملف
            with wave.open(input_file, "rb") as wf:
                n_channels = wf.getnchannels()
                sample_width = wf.getsampwidth()
                frame_rate = wf.getframerate()
                n_frames = wf.getnframes()
                frames = wf.readframes(n_frames)
            
            with wave.open(output_file, "wb") as wf:
                wf.setnchannels(n_channels)
                wf.setsampwidth(sample_width)
                wf.setframerate(frame_rate)
                wf.writeframes(frames)
            
            print(f"تم تحويل الملف إلى {output_file}")
            return output_file
        except Exception as e:
            print(f"خطأ في تحويل تنسيق الملف الصوتي: {e}")
            return None
    
    def read_text_aloud(self, text_file_path):
        """
        قراءة ملف نصي بصوت عالٍ
        
        المعلمات:
            text_file_path: مسار ملف النص
            
        العائد:
            True إذا نجحت العملية، False إذا فشلت
        """
        if not os.path.exists(text_file_path):
            print(f"خطأ: الملف '{text_file_path}' غير موجود.")
            return False
        
        try:
            # قراءة محتوى الملف النصي
            with open(text_file_path, "r", encoding="utf-8") as f:
                text = f.read()
            
            # تحويل النص إلى كلام
            return self.text_to_speech(text)
        except Exception as e:
            print(f"خطأ في قراءة الملف النصي: {e}")
            return False
    
    def voice_command_processor(self, commands_dict):
        """
        معالج أوامر صوتية
        
        المعلمات:
            commands_dict: قاموس يحتوي على الأوامر والوظائف المرتبطة بها
            
        العائد:
            نتيجة تنفيذ الأمر أو رسالة خطأ
        """
        if not commands_dict:
            return "خطأ: قاموس الأوامر فارغ."
        
        print("جاري الاستماع للأمر الصوتي... تحدث الآن.")
        command_text = self.recognize_from_microphone()
        
        if command_text.startswith("خطأ"):
            return command_text
        
        print(f"الأمر المتعرف عليه: {command_text}")
        
        # البحث عن الأمر في القاموس
        for command_key, command_func in commands_dict.items():
            if command_key.lower() in command_text.lower():
                try:
                    result = command_func()
                    return result
                except Exception as e:
                    return f"خطأ في تنفيذ الأمر: {e}"
        
        return f"الأمر '{command_text}' غير معروف."
    
    def create_audio_response(self, text, output_file=None):
        """
        إنشاء استجابة صوتية من نص
        
        المعلمات:
            text: نص الاستجابة
            output_file: مسار ملف الإخراج (اختياري)
            
        العائد:
            مسار ملف الاستجابة الصوتية أو True في حالة النجاح بدون ملف
        """
        if not text:
            print("خطأ: نص الاستجابة فارغ.")
            return None
        
        if output_file:
            # تحويل النص إلى كلام وحفظه في ملف
            if self.text_to_speech(text, output_file):
                return output_file
            else:
                return None
        else:
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
            temp_file.close()
            
            # تحويل النص إلى كلام وحفظه في الملف المؤقت
            if self.text_to_speech(text, temp_file.name):
                return temp_file.name
            else:
                # حذف الملف المؤقت في حالة الفشل
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                return None