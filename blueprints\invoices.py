#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة إدارة الفواتير
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import logging
import json
import os
import tempfile
import uuid

# استيراد النماذج وقاعدة البيانات
from models.invoice import Invoice, InvoiceItem
from models.client import Client
from models.project import Project
from models.transaction import Transaction
from extensions import db

# إنشاء مخطط Blueprint
invoices_bp = Blueprint('invoices', __name__, url_prefix='/invoices')

# إعداد السجل
logger = logging.getLogger(__name__)

@invoices_bp.route('/')
@login_required
def list_invoices():
    """
قائمة الفواتير
    """
    try:
        # الحصول على جميع الفواتير للمستخدم الحالي
        invoices = Invoice.query.filter_by(user_id=current_user.id).order_by(Invoice.issue_date.desc()).all()
        
        # الحصول على العملاء والمشاريع للفلترة
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        status_filter = request.args.get('status')
        client_filter = request.args.get('client_id')
        project_filter = request.args.get('project_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if status_filter and status_filter != 'all':
            invoices = [invoice for invoice in invoices if invoice.status == status_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            invoices = [invoice for invoice in invoices if invoice.client_id == client_id]
        
        if project_filter and project_filter.isdigit():
            project_id = int(project_filter)
            invoices = [invoice for invoice in invoices if invoice.project_id == project_id]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                invoices = [invoice for invoice in invoices if invoice.issue_date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                invoices = [invoice for invoice in invoices if invoice.issue_date.date() <= date_to]
            except ValueError:
                pass
        
        # حساب الإجماليات
        total_amount = sum(invoice.total_amount for invoice in invoices)
        total_paid = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
        total_unpaid = sum(invoice.total_amount for invoice in invoices if invoice.status == 'unpaid')
        
        return render_template(
            'invoices/list.html',
            invoices=invoices,
            clients=clients,
            projects=projects,
            total_amount=total_amount,
            total_paid=total_paid,
            total_unpaid=total_unpaid,
            status_filter=status_filter,
            client_filter=client_filter,
            project_filter=project_filter,
            date_from=date_from,
            date_to=date_to
        )
        
    except Exception as e:
        logger.error(f"Error loading invoices list: {str(e)}")
        flash('حدث خطأ أثناء تحميل قائمة الفواتير. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('invoices/list.html', invoices=[])

@invoices_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_invoice():
    """
إضافة فاتورة جديدة
    """
    # الحصول على العملاء والمشاريع
    clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
    projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
    
    if request.method == 'POST':
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')
        invoice_number = request.form.get('invoice_number')
        issue_date = request.form.get('issue_date')
        due_date = request.form.get('due_date')
        status = request.form.get('status', 'unpaid')
        client_notes = request.form.get('client_notes')
        private_notes = request.form.get('private_notes')
        
        # التحقق من البيانات
        if not client_id or not invoice_number or not issue_date or not due_date:
            flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
            return render_template('invoices/add.html', clients=clients, projects=projects)
        
        try:
            # التحقق من وجود العميل
            client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
            if not client:
                flash('العميل غير موجود.', 'danger')
                return render_template('invoices/add.html', clients=clients, projects=projects)
            
            # التحقق من وجود المشروع إذا تم تحديده
            project = None
            if project_id:
                project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
                if not project:
                    flash('المشروع غير موجود.', 'danger')
                    return render_template('invoices/add.html', clients=clients, projects=projects)
            
            # التحقق من تنسيق التواريخ
            try:
                issue_date = datetime.strptime(issue_date, '%Y-%m-%d')
                due_date = datetime.strptime(due_date, '%Y-%m-%d')
            except ValueError:
                flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                return render_template('invoices/add.html', clients=clients, projects=projects)
            
            # التحقق من عدم تكرار رقم الفاتورة
            existing_invoice = Invoice.query.filter_by(user_id=current_user.id, invoice_number=invoice_number).first()
            if existing_invoice:
                flash('رقم الفاتورة مستخدم بالفعل. يرجى استخدام رقم آخر.', 'danger')
                return render_template('invoices/add.html', clients=clients, projects=projects)
            
            # إنشاء فاتورة جديدة
            new_invoice = Invoice(
                user_id=current_user.id,
                client_id=client_id,
                project_id=project_id if project_id else None,
                invoice_number=invoice_number,
                issue_date=issue_date,
                due_date=due_date,
                status=status,
                client_notes=client_notes,
                private_notes=private_notes,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # إضافة عناصر الفاتورة
            items_data = json.loads(request.form.get('items_data', '[]'))
            total_amount = 0
            
            for item_data in items_data:
                description = item_data.get('description')
                quantity = float(item_data.get('quantity', 0))
                unit_price = float(item_data.get('unit_price', 0))
                tax_rate = float(item_data.get('tax_rate', 0))
                
                item_total = quantity * unit_price
                tax_amount = item_total * (tax_rate / 100)
                item_total_with_tax = item_total + tax_amount
                
                new_item = InvoiceItem(
                    description=description,
                    quantity=quantity,
                    unit_price=unit_price,
                    tax_rate=tax_rate,
                    tax_amount=tax_amount,
                    total_amount=item_total_with_tax
                )
                
                new_invoice.items.append(new_item)
                total_amount += item_total_with_tax
            
            new_invoice.total_amount = total_amount
            
            db.session.add(new_invoice)
            db.session.commit()
            
            # تحديث تاريخ آخر اتصال للعميل
            client.update_last_contact()
            db.session.commit()
            
            flash(f'تمت إضافة الفاتورة رقم {invoice_number} بنجاح.', 'success')
            return redirect(url_for('invoices.view_invoice', invoice_id=new_invoice.id))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error adding invoice: {str(e)}")
            flash('حدث خطأ أثناء إضافة الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
    
    # إنشاء رقم فاتورة افتراضي
    default_invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    return render_template(
        'invoices/add.html',
        clients=clients,
        projects=projects,
        default_invoice_number=default_invoice_number,
        today=datetime.now().strftime('%Y-%m-%d'),
        due_date=(datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    )

@invoices_bp.route('/<int:invoice_id>')
@login_required
def view_invoice(invoice_id):
    """
عرض تفاصيل الفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العميل والمشروع
        client = Client.query.get(invoice.client_id)
        project = Project.query.get(invoice.project_id) if invoice.project_id else None
        
        # الحصول على المعاملات المرتبطة
        transactions = Transaction.query.filter_by(invoice_id=invoice.id).order_by(Transaction.date.desc()).all()
        
        # حساب المبلغ المدفوع والمتبقي
        paid_amount = sum(transaction.amount for transaction in transactions)
        remaining_amount = invoice.total_amount - paid_amount
        
        return render_template(
            'invoices/view.html',
            invoice=invoice,
            client=client,
            project=project,
            transactions=transactions,
            paid_amount=paid_amount,
            remaining_amount=remaining_amount
        )
        
    except Exception as e:
        logger.error(f"Error viewing invoice: {str(e)}")
        flash('حدث خطأ أثناء عرض تفاصيل الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.list_invoices'))

@invoices_bp.route('/<int:invoice_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_invoice(invoice_id):
    """
تعديل الفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العملاء والمشاريع
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
        
        if request.method == 'POST':
            client_id = request.form.get('client_id')
            project_id = request.form.get('project_id')
            invoice_number = request.form.get('invoice_number')
            issue_date = request.form.get('issue_date')
            due_date = request.form.get('due_date')
            status = request.form.get('status')
            client_notes = request.form.get('client_notes')
            private_notes = request.form.get('private_notes')
            
            # التحقق من البيانات
            if not client_id or not invoice_number or not issue_date or not due_date:
                flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
                return render_template('invoices/edit.html', invoice=invoice, clients=clients, projects=projects)
            
            try:
                # التحقق من وجود العميل
                client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                if not client:
                    flash('العميل غير موجود.', 'danger')
                    return render_template('invoices/edit.html', invoice=invoice, clients=clients, projects=projects)
                
                # التحقق من وجود المشروع إذا تم تحديده
                project = None
                if project_id:
                    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
                    if not project:
                        flash('المشروع غير موجود.', 'danger')
                        return render_template('invoices/edit.html', invoice=invoice, clients=clients, projects=projects)
                
                # التحقق من تنسيق التواريخ
                try:
                    issue_date = datetime.strptime(issue_date, '%Y-%m-%d')
                    due_date = datetime.strptime(due_date, '%Y-%m-%d')
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return render_template('invoices/edit.html', invoice=invoice, clients=clients, projects=projects)
                
                # التحقق من عدم تكرار رقم الفاتورة
                existing_invoice = Invoice.query.filter(
                    Invoice.user_id == current_user.id,
                    Invoice.invoice_number == invoice_number,
                    Invoice.id != invoice_id
                ).first()
                
                if existing_invoice:
                    flash('رقم الفاتورة مستخدم بالفعل. يرجى استخدام رقم آخر.', 'danger')
                    return render_template('invoices/edit.html', invoice=invoice, clients=clients, projects=projects)
                
                # تحديث بيانات الفاتورة
                invoice.client_id = client_id
                invoice.project_id = project_id if project_id else None
                invoice.invoice_number = invoice_number
                invoice.issue_date = issue_date
                invoice.due_date = due_date
                invoice.status = status
                invoice.client_notes = client_notes
                invoice.private_notes = private_notes
                invoice.updated_at = datetime.now()
                
                # تحديث عناصر الفاتورة
                items_data = json.loads(request.form.get('items_data', '[]'))
                
                # حذف العناصر الحالية
                for item in invoice.items:
                    db.session.delete(item)
                
                # إضافة العناصر الجديدة
                total_amount = 0
                
                for item_data in items_data:
                    description = item_data.get('description')
                    quantity = float(item_data.get('quantity', 0))
                    unit_price = float(item_data.get('unit_price', 0))
                    tax_rate = float(item_data.get('tax_rate', 0))
                    
                    item_total = quantity * unit_price
                    tax_amount = item_total * (tax_rate / 100)
                    item_total_with_tax = item_total + tax_amount
                    
                    new_item = InvoiceItem(
                        invoice_id=invoice.id,
                        description=description,
                        quantity=quantity,
                        unit_price=unit_price,
                        tax_rate=tax_rate,
                        tax_amount=tax_amount,
                        total_amount=item_total_with_tax
                    )
                    
                    db.session.add(new_item)
                    total_amount += item_total_with_tax
                
                invoice.total_amount = total_amount
                
                db.session.commit()
                
                flash(f'تم تحديث الفاتورة رقم {invoice_number} بنجاح.', 'success')
                return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating invoice: {str(e)}")
                flash('حدث خطأ أثناء تحديث الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template(
            'invoices/edit.html',
            invoice=invoice,
            clients=clients,
            projects=projects
        )
        
    except Exception as e:
        logger.error(f"Error loading invoice for edit: {str(e)}")
        flash('حدث خطأ أثناء تحميل بيانات الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.list_invoices'))

@invoices_bp.route('/<int:invoice_id>/delete', methods=['POST'])
@login_required
def delete_invoice(invoice_id):
    """
حذف الفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # التحقق من وجود معاملات مرتبطة
        transactions_count = Transaction.query.filter_by(invoice_id=invoice.id).count()
        
        if transactions_count > 0:
            flash('لا يمكن حذف الفاتورة لأنها مرتبطة بمعاملات.', 'danger')
            return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
        
        # حذف عناصر الفاتورة
        for item in invoice.items:
            db.session.delete(item)
        
        # حذف الفاتورة
        db.session.delete(invoice)
        db.session.commit()
        
        flash(f'تم حذف الفاتورة رقم {invoice.invoice_number} بنجاح.', 'success')
        return redirect(url_for('invoices.list_invoices'))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting invoice: {str(e)}")
        flash('حدث خطأ أثناء حذف الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.list_invoices'))

@invoices_bp.route('/<int:invoice_id>/add-payment', methods=['GET', 'POST'])
@login_required
def add_payment(invoice_id):
    """
إضافة دفعة للفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العميل
        client = Client.query.get(invoice.client_id)
        
        # حساب المبلغ المدفوع والمتبقي
        transactions = Transaction.query.filter_by(invoice_id=invoice.id).all()
        paid_amount = sum(transaction.amount for transaction in transactions)
        remaining_amount = invoice.total_amount - paid_amount
        
        if request.method == 'POST':
            amount = request.form.get('amount')
            date = request.form.get('date')
            payment_method = request.form.get('payment_method')
            description = request.form.get('description')
            
            if not amount or not date:
                flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
                return render_template(
                    'invoices/add_payment.html',
                    invoice=invoice,
                    client=client,
                    remaining_amount=remaining_amount
                )
            
            try:
                # التحقق من المبلغ
                amount = float(amount)
                if amount <= 0:
                    flash('يجب أن يكون المبلغ أكبر من صفر.', 'danger')
                    return render_template(
                        'invoices/add_payment.html',
                        invoice=invoice,
                        client=client,
                        remaining_amount=remaining_amount
                    )
                
                # التحقق من تنسيق التاريخ
                try:
                    payment_date = datetime.strptime(date, '%Y-%m-%d')
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return render_template(
                        'invoices/add_payment.html',
                        invoice=invoice,
                        client=client,
                        remaining_amount=remaining_amount
                    )
                
                # إنشاء معاملة جديدة
                new_transaction = Transaction(
                    user_id=current_user.id,
                    client_id=client.id,
                    project_id=invoice.project_id,
                    invoice_id=invoice.id,
                    amount=amount,
                    date=payment_date,
                    type='income',
                    payment_method=payment_method,
                    description=description or f'دفعة للفاتورة رقم {invoice.invoice_number}',
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                db.session.add(new_transaction)
                
                # تحديث حالة الفاتورة
                new_paid_amount = paid_amount + amount
                if new_paid_amount >= invoice.total_amount:
                    invoice.status = 'paid'
                elif new_paid_amount > 0:
                    invoice.status = 'partially_paid'
                
                invoice.updated_at = datetime.now()
                
                # تحديث تاريخ آخر اتصال للعميل
                client.update_last_contact()
                
                db.session.commit()
                
                flash(f'تمت إضافة دفعة بقيمة {amount} بنجاح.', 'success')
                return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error adding payment: {str(e)}")
                flash('حدث خطأ أثناء إضافة الدفعة. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template(
            'invoices/add_payment.html',
            invoice=invoice,
            client=client,
            remaining_amount=remaining_amount,
            today=datetime.now().strftime('%Y-%m-%d')
        )
        
    except Exception as e:
        logger.error(f"Error loading payment form: {str(e)}")
        flash('حدث خطأ أثناء تحميل نموذج الدفع. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.list_invoices'))

@invoices_bp.route('/<int:invoice_id>/send-reminder', methods=['POST'])
@login_required
def send_reminder(invoice_id):
    """
إرسال تذكير للعميل
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العميل
        client = Client.query.get(invoice.client_id)
        
        if invoice.status == 'paid':
            flash('الفاتورة مدفوعة بالفعل.', 'warning')
            return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
        
        if not client.email:
            flash('لا يمكن إرسال تذكير لأن العميل ليس لديه بريد إلكتروني.', 'danger')
            return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
        
        # TODO: تنفيذ إرسال البريد الإلكتروني
        
        # تحديث تاريخ آخر تذكير
        invoice.last_reminder_date = datetime.now()
        invoice.updated_at = datetime.now()
        
        # تحديث تاريخ آخر اتصال للعميل
        client.update_last_contact()
        
        db.session.commit()
        
        flash(f'تم إرسال تذكير للعميل {client.name} بنجاح.', 'success')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error sending reminder: {str(e)}")
        flash('حدث خطأ أثناء إرسال التذكير. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))

@invoices_bp.route('/<int:invoice_id>/download-pdf')
@login_required
def download_pdf(invoice_id):
    """
تنزيل الفاتورة بصيغة PDF
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first_or_404()
        
        # TODO: تنفيذ إنشاء ملف PDF
        
        # إنشاء ملف مؤقت للتجربة
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(b'This is a placeholder for the PDF file.')
            temp_file_path = temp_file.name
        
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=f"invoice_{invoice.invoice_number}.pdf"
        )
        
    except Exception as e:
        logger.error(f"Error downloading PDF: {str(e)}")
        flash('حدث خطأ أثناء تنزيل الفاتورة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))

@invoices_bp.route('/api/list')
@login_required
def api_invoices_list():
    """
واجهة برمجة التطبيقات API لقائمة الفواتير
    """
    try:
        # الحصول على جميع الفواتير للمستخدم الحالي
        invoices = Invoice.query.filter_by(user_id=current_user.id).order_by(Invoice.issue_date.desc()).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        status_filter = request.args.get('status')
        client_filter = request.args.get('client_id')
        project_filter = request.args.get('project_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if status_filter and status_filter != 'all':
            invoices = [invoice for invoice in invoices if invoice.status == status_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            invoices = [invoice for invoice in invoices if invoice.client_id == client_id]
        
        if project_filter and project_filter.isdigit():
            project_id = int(project_filter)
            invoices = [invoice for invoice in invoices if invoice.project_id == project_id]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                invoices = [invoice for invoice in invoices if invoice.issue_date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                invoices = [invoice for invoice in invoices if invoice.issue_date.date() <= date_to]
            except ValueError:
                pass
        
        # تحويل البيانات إلى تنسيق JSON
        invoices_data = [invoice.to_dict() for invoice in invoices]
        
        return jsonify({
            'status': 'success',
            'data': invoices_data
        })
        
    except Exception as e:
        logger.error(f"API Error - invoices list: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب قائمة الفواتير'
        }), 500

@invoices_bp.route('/api/<int:invoice_id>')
@login_required
def api_invoice_details(invoice_id):
    """
واجهة برمجة التطبيقات API لتفاصيل الفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
        
        if not invoice:
            return jsonify({
                'status': 'error',
                'message': 'الفاتورة غير موجودة'
            }), 404
        
        # الحصول على العميل والمشروع
        client = Client.query.get(invoice.client_id)
        project = Project.query.get(invoice.project_id) if invoice.project_id else None
        
        # الحصول على المعاملات المرتبطة
        transactions = Transaction.query.filter_by(invoice_id=invoice.id).order_by(Transaction.date.desc()).all()
        
        # تحويل البيانات إلى تنسيق JSON
        invoice_data = invoice.to_dict()
        invoice_data['client'] = client.to_dict() if client else None
        invoice_data['project'] = project.to_dict() if project else None
        invoice_data['transactions'] = [transaction.to_dict() for transaction in transactions]
        invoice_data['items'] = [item.to_dict() for item in invoice.items]
        
        # حساب المبلغ المدفوع والمتبقي
        paid_amount = sum(transaction.amount for transaction in transactions)
        remaining_amount = invoice.total_amount - paid_amount
        
        invoice_data['paid_amount'] = paid_amount
        invoice_data['remaining_amount'] = remaining_amount
        
        return jsonify({
            'status': 'success',
            'data': invoice_data
        })
        
    except Exception as e:
        logger.error(f"API Error - invoice details: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب تفاصيل الفاتورة'
        }), 500

@invoices_bp.route('/api/add', methods=['POST'])
@login_required
def api_add_invoice():
    """
واجهة برمجة التطبيقات API لإضافة فاتورة
    """
    try:
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'client_id' not in data or 'invoice_number' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # التحقق من وجود العميل
        client = Client.query.filter_by(id=data['client_id'], user_id=current_user.id).first()
        if not client:
            return jsonify({
                'status': 'error',
                'message': 'العميل غير موجود'
            }), 400
        
        # التحقق من وجود المشروع إذا تم تحديده
        project = None
        if 'project_id' in data and data['project_id']:
            project = Project.query.filter_by(id=data['project_id'], user_id=current_user.id).first()
            if not project:
                return jsonify({
                    'status': 'error',
                    'message': 'المشروع غير موجود'
                }), 400
        
        # التحقق من عدم تكرار رقم الفاتورة
        existing_invoice = Invoice.query.filter_by(user_id=current_user.id, invoice_number=data['invoice_number']).first()
        if existing_invoice:
            return jsonify({
                'status': 'error',
                'message': 'رقم الفاتورة مستخدم بالفعل'
            }), 400
        
        # إنشاء فاتورة جديدة
        new_invoice = Invoice(
            user_id=current_user.id,
            client_id=data['client_id'],
            project_id=data.get('project_id'),
            invoice_number=data['invoice_number'],
            issue_date=datetime.strptime(data['issue_date'], '%Y-%m-%d') if 'issue_date' in data else datetime.now(),
            due_date=datetime.strptime(data['due_date'], '%Y-%m-%d') if 'due_date' in data else (datetime.now() + timedelta(days=30)),
            status=data.get('status', 'unpaid'),
            client_notes=data.get('client_notes', ''),
            private_notes=data.get('private_notes', ''),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # إضافة عناصر الفاتورة
        items_data = data.get('items', [])
        total_amount = 0
        
        for item_data in items_data:
            description = item_data.get('description', '')
            quantity = float(item_data.get('quantity', 0))
            unit_price = float(item_data.get('unit_price', 0))
            tax_rate = float(item_data.get('tax_rate', 0))
            
            item_total = quantity * unit_price
            tax_amount = item_total * (tax_rate / 100)
            item_total_with_tax = item_total + tax_amount
            
            new_item = InvoiceItem(
                description=description,
                quantity=quantity,
                unit_price=unit_price,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                total_amount=item_total_with_tax
            )
            
            new_invoice.items.append(new_item)
            total_amount += item_total_with_tax
        
        new_invoice.total_amount = total_amount
        
        db.session.add(new_invoice)
        db.session.commit()
        
        # تحديث تاريخ آخر اتصال للعميل
        client.update_last_contact()
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تمت إضافة الفاتورة رقم {new_invoice.invoice_number} بنجاح.',
            'data': new_invoice.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - add invoice: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إضافة الفاتورة'
        }), 500

@invoices_bp.route('/api/<int:invoice_id>/update-status', methods=['PUT'])
@login_required
def api_update_invoice_status(invoice_id):
    """
واجهة برمجة التطبيقات API لتحديث حالة الفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
        
        if not invoice:
            return jsonify({
                'status': 'error',
                'message': 'الفاتورة غير موجودة'
            }), 404
        
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'status' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # التحقق من صحة الحالة
        valid_statuses = ['unpaid', 'paid', 'partially_paid', 'cancelled', 'overdue']
        if data['status'] not in valid_statuses:
            return jsonify({
                'status': 'error',
                'message': 'حالة الفاتورة غير صالحة'
            }), 400
        
        # تحديث حالة الفاتورة
        invoice.status = data['status']
        invoice.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تم تحديث حالة الفاتورة إلى {data["status"]} بنجاح.',
            'data': invoice.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - update invoice status: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء تحديث حالة الفاتورة'
        }), 500

@invoices_bp.route('/api/<int:invoice_id>/add-payment', methods=['POST'])
@login_required
def api_add_payment(invoice_id):
    """
واجهة برمجة التطبيقات API لإضافة دفعة للفاتورة
    """
    try:
        # الحصول على الفاتورة
        invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
        
        if not invoice:
            return jsonify({
                'status': 'error',
                'message': 'الفاتورة غير موجودة'
            }), 404
        
        # الحصول على العميل
        client = Client.query.get(invoice.client_id)
        
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'amount' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # التحقق من المبلغ
        amount = float(data['amount'])
        if amount <= 0:
            return jsonify({
                'status': 'error',
                'message': 'يجب أن يكون المبلغ أكبر من صفر'
            }), 400
        
        # حساب المبلغ المدفوع والمتبقي
        transactions = Transaction.query.filter_by(invoice_id=invoice.id).all()
        paid_amount = sum(transaction.amount for transaction in transactions)
        remaining_amount = invoice.total_amount - paid_amount
        
        if amount > remaining_amount:
            return jsonify({
                'status': 'error',
                'message': f'المبلغ أكبر من المبلغ المتبقي ({remaining_amount})'
            }), 400
        
        # إنشاء معاملة جديدة
        payment_date = datetime.strptime(data.get('date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d')
        
        new_transaction = Transaction(
            user_id=current_user.id,
            client_id=client.id,
            project_id=invoice.project_id,
            invoice_id=invoice.id,
            amount=amount,
            date=payment_date,
            type='income',
            payment_method=data.get('payment_method', 'bank_transfer'),
            description=data.get('description', f'دفعة للفاتورة رقم {invoice.invoice_number}'),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.session.add(new_transaction)
        
        # تحديث حالة الفاتورة
        new_paid_amount = paid_amount + amount
        if new_paid_amount >= invoice.total_amount:
            invoice.status = 'paid'
        elif new_paid_amount > 0:
            invoice.status = 'partially_paid'
        
        invoice.updated_at = datetime.now()
        
        # تحديث تاريخ آخر اتصال للعميل
        client.update_last_contact()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تمت إضافة دفعة بقيمة {amount} بنجاح.',
            'data': {
                'transaction': new_transaction.to_dict(),
                'invoice': invoice.to_dict(),
                'paid_amount': new_paid_amount,
                'remaining_amount': invoice.total_amount - new_paid_amount
            }
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - add payment: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إضافة الدفعة'
        }), 500