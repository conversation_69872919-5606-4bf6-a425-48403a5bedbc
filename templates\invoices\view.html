{% extends "base.html" %}

{% block title %}فاتورة رقم {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">فاتورة رقم {{ invoice.invoice_number }}</h1>
            <p class="text-muted">تفاصيل الفاتورة</p>
        </div>
        <div>
            <div class="btn-group">
                <a href="{{ url_for('invoices.list_invoices') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
                </a>
                <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                {% if invoice.status != 'paid' %}
                <a href="{{ url_for('invoices.add_payment', invoice_id=invoice.id) }}" class="btn btn-success">
                    <i class="fas fa-money-bill-wave me-2"></i>إضافة دفعة
                </a>
                {% endif %}
                <button class="btn btn-info" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Details -->
        <div class="col-lg-8">
            <!-- Invoice Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="text-primary">فاتورة رقم: {{ invoice.invoice_number }}</h4>
                            <p class="mb-1"><strong>تاريخ الإصدار:</strong> {{ invoice.issue_date.strftime('%Y-%m-%d') }}</p>
                            <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date.strftime('%Y-%m-%d') }}</p>
                            <p class="mb-0">
                                <strong>الحالة:</strong>
                                {% if invoice.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                {% elif invoice.status == 'unpaid' %}
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                {% elif invoice.status == 'partially_paid' %}
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                {% elif invoice.status == 'overdue' %}
                                    <span class="badge bg-dark">متأخرة</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h5>بيانات العميل</h5>
                            <p class="mb-1"><strong>{{ client.name }}</strong></p>
                            {% if client.email %}
                            <p class="mb-1">{{ client.email }}</p>
                            {% endif %}
                            {% if client.phone %}
                            <p class="mb-1">{{ client.phone }}</p>
                            {% endif %}
                            {% if client.address %}
                            <p class="mb-0">{{ client.address }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>عناصر الفاتورة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الوصف</th>
                                    <th width="15%">الكمية</th>
                                    <th width="15%">السعر</th>
                                    <th width="10%">الضريبة</th>
                                    <th width="15%">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.items %}
                                <tr>
                                    <td>{{ item.description }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price|format_currency }}</td>
                                    <td>{{ item.tax_rate }}%</td>
                                    <td>{{ item.total_amount|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-info">
                                <tr>
                                    <td colspan="4" class="text-end"><strong>الإجمالي الكلي:</strong></td>
                                    <td><strong>{{ invoice.total_amount|format_currency }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            {% if invoice.client_notes or invoice.private_notes %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>الملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    {% if invoice.client_notes %}
                    <div class="mb-3">
                        <h6>ملاحظات للعميل:</h6>
                        <p class="text-muted">{{ invoice.client_notes }}</p>
                    </div>
                    {% endif %}
                    {% if invoice.private_notes %}
                    <div>
                        <h6>ملاحظات خاصة:</h6>
                        <p class="text-muted">{{ invoice.private_notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Payment History -->
            {% if transactions %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>تاريخ المدفوعات
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ transaction.amount|format_currency }}</td>
                                    <td>{{ transaction.payment_method or 'غير محدد' }}</td>
                                    <td>{{ transaction.description }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Payment Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>ملخص المدفوعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-6">إجمالي الفاتورة:</div>
                        <div class="col-6 text-end"><strong>{{ invoice.total_amount|format_currency }}</strong></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">المبلغ المدفوع:</div>
                        <div class="col-6 text-end text-success"><strong>{{ paid_amount|format_currency }}</strong></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6"><strong>المبلغ المتبقي:</strong></div>
                        <div class="col-6 text-end">
                            <strong class="{% if remaining_amount > 0 %}text-danger{% else %}text-success{% endif %}">
                                {{ remaining_amount|format_currency }}
                            </strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Info -->
            {% if project %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-project-diagram me-2"></i>معلومات المشروع
                    </h5>
                </div>
                <div class="card-body">
                    <h6>{{ project.name }}</h6>
                    {% if project.description %}
                    <p class="text-muted small">{{ project.description }}</p>
                    {% endif %}
                    <p class="mb-1"><strong>الحالة:</strong> 
                        {% if project.status == 'active' %}
                            <span class="badge bg-success">نشط</span>
                        {% elif project.status == 'completed' %}
                            <span class="badge bg-primary">مكتمل</span>
                        {% elif project.status == 'on_hold' %}
                            <span class="badge bg-warning">معلق</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ project.status }}</span>
                        {% endif %}
                    </p>
                    {% if project.start_date %}
                    <p class="mb-1"><strong>تاريخ البداية:</strong> {{ project.start_date.strftime('%Y-%m-%d') }}</p>
                    {% endif %}
                    {% if project.end_date %}
                    <p class="mb-0"><strong>تاريخ النهاية:</strong> {{ project.end_date.strftime('%Y-%m-%d') }}</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if invoice.status != 'paid' %}
                        <a href="{{ url_for('invoices.add_payment', invoice_id=invoice.id) }}" class="btn btn-success">
                            <i class="fas fa-money-bill-wave me-2"></i>إضافة دفعة
                        </a>
                        {% endif %}
                        <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                        </a>
                        <button class="btn btn-info" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>طباعة الفاتورة
                        </button>
                        <button type="button" class="btn btn-danger" 
                                onclick="confirmDelete({{ invoice.id }}, '{{ invoice.invoice_number }}')">
                            <i class="fas fa-trash me-2"></i>حذف الفاتورة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف الفاتورة رقم <strong id="invoiceNumber"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: هذا الإجراء لا يمكن التراجع عنه.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(invoiceId, invoiceNumber) {
    document.getElementById('invoiceNumber').textContent = invoiceNumber;
    document.getElementById('deleteForm').action = `/invoices/${invoiceId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .card-header, .modal, nav, .sidebar {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
}
</style>
{% endblock %}
