{% extends "base.html" %}

{% block title %}إضافة مشروع جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة مشروع جديد</h1>
            <p class="text-muted">إنشاء مشروع جديد</p>
        </div>
        <div>
            <a href="{{ url_for('projects.list_projects') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <form method="POST">
        <div class="row">
            <!-- Main Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-project-diagram me-2"></i>المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المشروع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="">اختر العميل (اختياري)</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">وصف المشروع</label>
                                <textarea class="form-control" id="description" name="description" rows="4" 
                                          placeholder="وصف تفصيلي للمشروع"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline and Budget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>الجدول الزمني والميزانية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ today }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">تاريخ النهاية المتوقعة</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="budget" class="form-label">الميزانية</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           min="0" step="0.01" placeholder="0.00">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="estimated_hours" class="form-label">الساعات المقدرة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" 
                                           min="0" step="0.5" placeholder="0">
                                    <span class="input-group-text">ساعة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>تفاصيل إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <input type="text" class="form-control" id="category" name="category" 
                                       placeholder="مثل: تطوير ويب، تصميم، استشارات">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="requirements" class="form-label">المتطلبات</label>
                                <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                          placeholder="متطلبات المشروع والمواصفات"></textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="ملاحظات إضافية"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-toggle-on me-2"></i>الحالة والتقدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="status" class="form-label">حالة المشروع</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" selected>نشط</option>
                                <option value="on_hold">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="progress" class="form-label">نسبة التقدم (%)</label>
                            <input type="range" class="form-range" id="progress" name="progress" 
                                   min="0" max="100" value="0" oninput="updateProgressValue(this.value)">
                            <div class="d-flex justify-content-between">
                                <span>0%</span>
                                <span id="progressValue" class="fw-bold">0%</span>
                                <span>100%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Members -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>فريق العمل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="project_manager" class="form-label">مدير المشروع</label>
                            <input type="text" class="form-control" id="project_manager" name="project_manager" 
                                   placeholder="اسم مدير المشروع">
                        </div>
                        <div class="mb-3">
                            <label for="team_members" class="form-label">أعضاء الفريق</label>
                            <textarea class="form-control" id="team_members" name="team_members" rows="3" 
                                      placeholder="أسماء أعضاء الفريق (كل اسم في سطر منفصل)"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المشروع
                            </button>
                            <a href="{{ url_for('projects.list_projects') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateProgressValue(value) {
    document.getElementById('progressValue').textContent = value + '%';
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate name
        if (!nameInput.value.trim()) {
            isValid = false;
            nameInput.classList.add('is-invalid');
        } else {
            nameInput.classList.remove('is-invalid');
        }
        
        // Validate dates
        if (startDateInput.value && endDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            
            if (endDate <= startDate) {
                isValid = false;
                endDateInput.classList.add('is-invalid');
                alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            } else {
                endDateInput.classList.remove('is-invalid');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Real-time validation
    nameInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('is-invalid');
        }
    });
    
    // Date validation
    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(this.value);
            
            if (endDate <= startDate) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        }
    });
});
</script>
{% endblock %}
