{% extends "base.html" %}

{% block title %}{{ code }} - {{ title }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <div class="error-container">
                <h1 class="display-1 text-{{ color|default('primary') }}">{{ code }}</h1>
                <h2 class="mb-4">{{ title }}</h2>
                <p class="lead mb-4">{{ message }}</p>
                <div class="error-actions">
                    <a href="{{ url_for('main.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i> العودة للصفحة الرئيسية
                    </a>
                    <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg ms-2">
                        <i class="fas fa-arrow-left me-2"></i> العودة للصفحة السابقة
                    </a>
                </div>
                {% if details and current_user.is_admin %}
                <div class="mt-5 text-start">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">تفاصيل الخطأ (للمسؤولين فقط):</h5>
                        <hr>
                        <p class="mb-0">{{ details }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}