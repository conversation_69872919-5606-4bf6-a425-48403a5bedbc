{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">تسجيل الدخول</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting" height="80">
                    </div>
                    
                    <form method="POST" action="{{ url_for('auth.login') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                {{ form.remember_me(class="form-check-input") }}
                                <label class="form-check-label" for="remember_me">تذكرني</label>
                            </div>
                            <a href="{{ url_for('auth.reset_password_request') }}" class="text-primary">نسيت كلمة المرور؟</a>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">ليس لديك حساب؟ <a href="{{ url_for('auth.register') }}" class="text-primary">إنشاء حساب جديد</a></p>
                </div>
            </div>
            
            <!-- Social Login - معطل مؤقتاً -->
            <!--
            <div class="mt-4">
                <div class="separator text-muted mb-3">أو تسجيل الدخول باستخدام</div>
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-danger disabled">
                        <i class="fab fa-google me-2"></i> تسجيل الدخول باستخدام Google
                    </a>
                    <a href="#" class="btn btn-outline-primary disabled">
                        <i class="fab fa-facebook-f me-2"></i> تسجيل الدخول باستخدام Facebook
                    </a>
                </div>
            </div>
            -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const input = this.closest('.input-group').querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
    });
</script>
{% endblock %}