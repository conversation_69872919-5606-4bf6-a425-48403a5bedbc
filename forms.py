# -*- coding: utf-8 -*-

"""
نماذج Flask-WTF
"""

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Email, Length, EqualTo

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegisterForm(FlaskForm):
    """نموذج التسجيل"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), <PERSON><PERSON>()])
    first_name = StringField('الاسم الأول', validators=[DataRequired()])
    last_name = StringField('الاسم الأخير', validators=[DataRequired()])
    company_name = StringField('اسم الشركة')
    phone = StringField('رقم الهاتف')
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    accept_terms = BooleanField('أوافق على الشروط والأحكام', validators=[DataRequired()])
    submit = SubmitField('تسجيل')

class ResetPasswordRequestForm(FlaskForm):
    """نموذج طلب إعادة تعيين كلمة المرور"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    submit = SubmitField('إرسال رابط إعادة التعيين')

class ResetPasswordForm(FlaskForm):
    """نموذج إعادة تعيين كلمة المرور"""
    password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    submit = SubmitField('حفظ كلمة المرور الجديدة')
