#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة معالجة الصور والتعرف الضوئي على النصوص (OCR)
"""

import os
import cv2
import numpy as np
import pytesseract
import easyocr
from PIL import Image
import re
import tempfile

class OCRProcessor:
    """
    فئة لمعالجة الصور والتعرف الضوئي على النصوص (OCR)
    """
    
    def __init__(self, tesseract_cmd=None, languages=['en', 'ar']):
        """
        تهيئة معالج OCR
        
        المعلمات:
            tesseract_cmd: مسار برنامج Tesseract OCR (اختياري)
            languages: قائمة اللغات المدعومة
        """
        # تعيين مسار Tesseract إذا تم توفيره
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
        
        # تهيئة EasyOCR Reader
        self.reader = easyocr.Reader(languages)
        self.languages = languages
    
    def preprocess_image(self, image_path):
        """
        معالجة الصورة قبل التعرف الضوئي على النصوص
        
        المعلمات:
            image_path: مسار ملف الصورة
            
        العائد:
            الصورة المعالجة
        """
        # قراءة الصورة
        image = cv2.imread(image_path)
        
        # تحويل الصورة إلى اللون الرمادي
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # تطبيق فلتر Gaussian لتنعيم الصورة
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # تطبيق عتبة تكيفية
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # تطبيق عمليات مورفولوجية لإزالة الضوضاء
        kernel = np.ones((1, 1), np.uint8)
        opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        
        return opening
    
    def extract_text_tesseract(self, image_path, preprocess=True, lang='ara+eng'):
        """
        استخراج النص من الصورة باستخدام Tesseract OCR
        
        المعلمات:
            image_path: مسار ملف الصورة
            preprocess: معالجة الصورة قبل التعرف الضوئي (اختياري)
            lang: اللغات المستخدمة في التعرف الضوئي
            
        العائد:
            النص المستخرج من الصورة
        """
        # معالجة الصورة إذا تم تحديد ذلك
        if preprocess:
            image = self.preprocess_image(image_path)
            # حفظ الصورة المعالجة في ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            cv2.imwrite(temp_file.name, image)
            image_path = temp_file.name
        
        # استخراج النص باستخدام Tesseract
        text = pytesseract.image_to_string(Image.open(image_path), lang=lang)
        
        # حذف الملف المؤقت إذا تم إنشاؤه
        if preprocess:
            os.unlink(temp_file.name)
        
        return text
    
    def extract_text_easyocr(self, image_path, detail=0):
        """
        استخراج النص من الصورة باستخدام EasyOCR
        
        المعلمات:
            image_path: مسار ملف الصورة
            detail: مستوى التفاصيل في النتائج (0: النص فقط، 1: النص مع الإحداثيات)
            
        العائد:
            النص المستخرج من الصورة
        """
        # استخراج النص باستخدام EasyOCR
        results = self.reader.readtext(image_path)
        
        if detail == 0:
            # إرجاع النص فقط
            return '\n'.join([result[1] for result in results])
        else:
            # إرجاع النص مع الإحداثيات
            return results
    
    def extract_invoice_data(self, image_path, method='easyocr'):
        """
        استخراج بيانات الفاتورة من الصورة
        
        المعلمات:
            image_path: مسار ملف الصورة
            method: طريقة التعرف الضوئي ('tesseract' أو 'easyocr')
            
        العائد:
            قاموس يحتوي على بيانات الفاتورة
        """
        # استخراج النص من الصورة
        if method == 'tesseract':
            text = self.extract_text_tesseract(image_path)
        else:  # easyocr
            text = self.extract_text_easyocr(image_path)
        
        # استخراج بيانات الفاتورة من النص
        invoice_data = {
            'invoice_number': self._extract_invoice_number(text),
            'date': self._extract_date(text),
            'total_amount': self._extract_amount(text),
            'vendor': self._extract_vendor(text),
            'items': self._extract_items(text),
            'tax': self._extract_tax(text)
        }
        
        return invoice_data
    
    def _extract_invoice_number(self, text):
        """
        استخراج رقم الفاتورة من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            رقم الفاتورة
        """
        # أنماط مختلفة لرقم الفاتورة
        patterns = [
            r'(?:Invoice|فاتورة|رقم الفاتورة)[\s#:]*([A-Za-z0-9-]+)',
            r'(?:Invoice|فاتورة|رقم الفاتورة)[\s#:]*([\u0600-\u06FF\s0-9-]+)',
            r'(?:No|Number|رقم)[\s#:]*([A-Za-z0-9-]+)',
            r'(?:No|Number|رقم)[\s#:]*([\u0600-\u06FF\s0-9-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_date(self, text):
        """
        استخراج التاريخ من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            التاريخ
        """
        # أنماط مختلفة للتاريخ
        patterns = [
            r'(?:Date|تاريخ)[\s:]*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
            r'(?:Date|تاريخ)[\s:]*([0-9]{2,4}[/-][0-9]{1,2}[/-][0-9]{1,2})',
            r'([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_amount(self, text):
        """
        استخراج المبلغ الإجمالي من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            المبلغ الإجمالي
        """
        # أنماط مختلفة للمبلغ الإجمالي
        patterns = [
            r'(?:Total|الإجمالي|المجموع)[\s:]*(?:SAR|USD|AED|ر\.س\.|\$)?\s*([\d,\.]+)',
            r'(?:Amount|المبلغ)[\s:]*(?:SAR|USD|AED|ر\.س\.|\$)?\s*([\d,\.]+)',
            r'(?:SAR|USD|AED|ر\.س\.|\$)\s*([\d,\.]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                # تنظيف المبلغ وتحويله إلى رقم
                amount_str = match.group(1).strip().replace(',', '')
                try:
                    return float(amount_str)
                except ValueError:
                    pass
        
        return None
    
    def _extract_vendor(self, text):
        """
        استخراج اسم البائع من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            اسم البائع
        """
        # أنماط مختلفة لاسم البائع
        patterns = [
            r'(?:Vendor|Supplier|From|البائع|المورد|من)[\s:]*([A-Za-z\s]+)',
            r'(?:Vendor|Supplier|From|البائع|المورد|من)[\s:]*([\u0600-\u06FF\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # محاولة استخراج اسم البائع من أول سطر
        lines = text.split('\n')
        if lines and len(lines[0].strip()) > 0:
            return lines[0].strip()
        
        return None
    
    def _extract_items(self, text):
        """
        استخراج العناصر من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            قائمة العناصر
        """
        items = []
        
        # البحث عن قسم العناصر
        item_section_patterns = [
            r'(?:Items|Description|العناصر|الوصف).*?(?:Total|الإجمالي|المجموع)',
            r'(?:Qty|Quantity|الكمية).*?(?:Price|السعر).*?(?:Amount|المبلغ)'
        ]
        
        item_section = text
        for pattern in item_section_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                item_section = match.group(0)
                break
        
        # استخراج العناصر من قسم العناصر
        lines = item_section.split('\n')
        for line in lines:
            # تجاهل الأسطر الفارغة والعناوين
            if not line.strip() or re.search(r'(?:Items|Description|العناصر|الوصف|Qty|Quantity|الكمية|Price|السعر|Amount|المبلغ)', line, re.IGNORECASE):
                continue
            
            # محاولة استخراج الكمية والسعر والمبلغ
            qty_match = re.search(r'(\d+)', line)
            price_match = re.search(r'(\d+(?:\.\d+)?)', line)
            amount_match = re.search(r'(\d+(?:\.\d+)?)', line)
            
            if qty_match and price_match and amount_match:
                # استخراج الوصف (النص المتبقي بعد إزالة الأرقام)
                description = re.sub(r'\d+(?:\.\d+)?', '', line).strip()
                
                items.append({
                    'description': description,
                    'quantity': int(qty_match.group(1)),
                    'price': float(price_match.group(1)),
                    'amount': float(amount_match.group(1))
                })
        
        return items
    
    def _extract_tax(self, text):
        """
        استخراج الضريبة من النص
        
        المعلمات:
            text: النص المستخرج من الصورة
            
        العائد:
            مبلغ الضريبة
        """
        # أنماط مختلفة للضريبة
        patterns = [
            r'(?:Tax|VAT|ضريبة|ضريبة القيمة المضافة)[\s:]*(?:SAR|USD|AED|ر\.س\.|\$)?\s*([\d,\.]+)',
            r'(?:Tax|VAT|ضريبة|ضريبة القيمة المضافة)[\s:]*(\d+)%'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                # تنظيف المبلغ وتحويله إلى رقم
                tax_str = match.group(1).strip().replace(',', '')
                try:
                    return float(tax_str)
                except ValueError:
                    pass
        
        return None
    
    def scan_receipt(self, image_path, method='easyocr'):
        """
        مسح الإيصال واستخراج البيانات
        
        المعلمات:
            image_path: مسار ملف الصورة
            method: طريقة التعرف الضوئي ('tesseract' أو 'easyocr')
            
        العائد:
            قاموس يحتوي على بيانات الإيصال
        """
        # استخراج النص من الصورة
        if method == 'tesseract':
            text = self.extract_text_tesseract(image_path)
        else:  # easyocr
            text = self.extract_text_easyocr(image_path)
        
        # استخراج بيانات الإيصال من النص
        receipt_data = {
            'vendor': self._extract_vendor(text),
            'date': self._extract_date(text),
            'total_amount': self._extract_amount(text),
            'items': self._extract_items(text),
            'tax': self._extract_tax(text)
        }
        
        return receipt_data
    
    def detect_document_type(self, image_path, method='easyocr'):
        """
        تحديد نوع المستند (فاتورة، إيصال، عقد، إلخ)
        
        المعلمات:
            image_path: مسار ملف الصورة
            method: طريقة التعرف الضوئي ('tesseract' أو 'easyocr')
            
        العائد:
            نوع المستند
        """
        # استخراج النص من الصورة
        if method == 'tesseract':
            text = self.extract_text_tesseract(image_path)
        else:  # easyocr
            text = self.extract_text_easyocr(image_path)
        
        # تحديد نوع المستند بناءً على الكلمات الرئيسية
        text_lower = text.lower()
        
        if re.search(r'invoice|فاتورة|رقم الفاتورة', text_lower):
            return 'invoice'
        elif re.search(r'receipt|إيصال|وصل', text_lower):
            return 'receipt'
        elif re.search(r'contract|عقد|اتفاقية', text_lower):
            return 'contract'
        elif re.search(r'quotation|quote|عرض سعر|عرض أسعار', text_lower):
            return 'quotation'
        elif re.search(r'purchase order|أمر شراء|طلب شراء', text_lower):
            return 'purchase_order'
        else:
            return 'unknown'
    
    def extract_text_from_pdf(self, pdf_path, page_numbers=None):
        """
        استخراج النص من ملف PDF
        
        المعلمات:
            pdf_path: مسار ملف PDF
            page_numbers: أرقام الصفحات المراد استخراج النص منها (اختياري)
            
        العائد:
            النص المستخرج من ملف PDF
        """
        try:
            from pdf2image import convert_from_path
            from PyPDF2 import PdfReader
        except ImportError:
            raise ImportError("يجب تثبيت مكتبات pdf2image و PyPDF2 لاستخدام هذه الوظيفة")
        
        # محاولة استخراج النص مباشرة من PDF
        try:
            reader = PdfReader(pdf_path)
            text = ""
            
            if page_numbers is None:
                page_numbers = range(len(reader.pages))
            
            for page_num in page_numbers:
                if page_num < len(reader.pages):
                    text += reader.pages[page_num].extract_text() + "\n"
            
            # إذا تم استخراج النص بنجاح، أرجعه
            if text.strip():
                return text
        except Exception as e:
            print(f"فشل استخراج النص مباشرة من PDF: {e}")
        
        # إذا فشل استخراج النص مباشرة، استخدم OCR
        try:
            # تحويل PDF إلى صور
            images = convert_from_path(pdf_path)
            
            # تحديد الصفحات المراد معالجتها
            if page_numbers is not None:
                images = [images[i] for i in page_numbers if i < len(images)]
            
            # استخراج النص من كل صورة
            text = ""
            for i, image in enumerate(images):
                # حفظ الصورة في ملف مؤقت
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                image.save(temp_file.name, 'PNG')
                
                # استخراج النص من الصورة
                page_text = self.extract_text_easyocr(temp_file.name)
                text += f"=== صفحة {i+1} ===\n{page_text}\n\n"
                
                # حذف الملف المؤقت
                os.unlink(temp_file.name)
            
            return text
        except Exception as e:
            print(f"فشل استخراج النص من PDF باستخدام OCR: {e}")
            return ""
    
    def extract_tables_from_image(self, image_path):
        """
        استخراج الجداول من الصورة
        
        المعلمات:
            image_path: مسار ملف الصورة
            
        العائد:
            قائمة الجداول المستخرجة
        """
        try:
            import pytesseract
            from pytesseract import Output
        except ImportError:
            raise ImportError("يجب تثبيت مكتبة pytesseract لاستخدام هذه الوظيفة")
        
        # قراءة الصورة
        image = cv2.imread(image_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # استخراج معلومات الكلمات والأسطر
        data = pytesseract.image_to_data(gray, output_type=Output.DICT, lang='ara+eng')
        
        # تحديد الأسطر والأعمدة
        n_boxes = len(data['text'])
        lines = {}
        
        for i in range(n_boxes):
            if int(data['conf'][i]) > 60:  # تجاهل النص ذو الثقة المنخفضة
                (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
                text = data['text'][i]
                
                if text.strip():
                    line_num = data['line_num'][i]
                    if line_num not in lines:
                        lines[line_num] = []
                    
                    lines[line_num].append({
                        'text': text,
                        'x': x,
                        'y': y,
                        'w': w,
                        'h': h
                    })
        
        # تحديد الجداول بناءً على تنظيم النص
        tables = []
        current_table = []
        
        for line_num in sorted(lines.keys()):
            line_words = sorted(lines[line_num], key=lambda word: word['x'])
            
            # تحقق مما إذا كان السطر يحتوي على عدة كلمات متباعدة (محتمل أن يكون جدولًا)
            if len(line_words) > 2:
                # حساب المسافات بين الكلمات
                spaces = []
                for i in range(1, len(line_words)):
                    space = line_words[i]['x'] - (line_words[i-1]['x'] + line_words[i-1]['w'])
                    spaces.append(space)
                
                # إذا كانت المسافات متساوية تقريبًا، فقد يكون هذا جدولًا
                if len(set(spaces)) < len(spaces) / 2:
                    current_table.append([word['text'] for word in line_words])
                else:
                    # إذا كان هناك جدول حالي وانتهى، أضفه إلى قائمة الجداول
                    if current_table:
                        tables.append(current_table)
                        current_table = []
            else:
                # إذا كان هناك جدول حالي وانتهى، أضفه إلى قائمة الجداول
                if current_table:
                    tables.append(current_table)
                    current_table = []
        
        # إضافة الجدول الأخير إذا كان موجودًا
        if current_table:
            tables.append(current_table)
        
        return tables