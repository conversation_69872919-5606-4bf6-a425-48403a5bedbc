#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة تصدير البيانات
"""

import io
import csv
import pandas as pd
from datetime import datetime
from flask import send_file

class ExportHelper:
    """
    فئة لتصدير البيانات بتنسيقات مختلفة
    """
    
    @staticmethod
    def export_to_csv(data, filename=None, columns=None):
        """
        تصدير البيانات إلى ملف CSV
        
        المعلمات:
            data: البيانات المراد تصديرها (قائمة من القواميس)
            filename: اسم الملف (اختياري)
            columns: أعمدة التصدير (قاموس يحتوي على اسم العمود والمفتاح في البيانات)
            
        العائد:
            كائن استجابة Flask لتنزيل الملف
        """
        if not filename:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # إنشاء DataFrame من البيانات
        df = pd.DataFrame(data)
        
        # إعادة تسمية الأعمدة إذا تم تحديدها
        if columns:
            # تحديد الأعمدة المطلوبة فقط
            df = df[[key for key in columns.values() if key in df.columns]]
            # إعادة تسمية الأعمدة
            df.rename(columns={v: k for k, v in columns.items()}, inplace=True)
        
        # تحويل البيانات إلى CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8-sig')
        output.seek(0)
        
        # إرسال الملف
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8-sig')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=filename
        )
    
    @staticmethod
    def export_to_excel(data, filename=None, columns=None, sheet_name='Sheet1'):
        """
        تصدير البيانات إلى ملف Excel
        
        المعلمات:
            data: البيانات المراد تصديرها (قائمة من القواميس)
            filename: اسم الملف (اختياري)
            columns: أعمدة التصدير (قاموس يحتوي على اسم العمود والمفتاح في البيانات)
            sheet_name: اسم ورقة العمل (اختياري)
            
        العائد:
            كائن استجابة Flask لتنزيل الملف
        """
        if not filename:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # إنشاء DataFrame من البيانات
        df = pd.DataFrame(data)
        
        # إعادة تسمية الأعمدة إذا تم تحديدها
        if columns:
            # تحديد الأعمدة المطلوبة فقط
            df = df[[key for key in columns.values() if key in df.columns]]
            # إعادة تسمية الأعمدة
            df.rename(columns={v: k for k, v in columns.items()}, inplace=True)
        
        # تحويل البيانات إلى Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name=sheet_name)
            # تعديل عرض الأعمدة ليناسب المحتوى
            worksheet = writer.sheets[sheet_name]
            for i, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).map(len).max(),
                    len(str(col))
                ) + 2  # إضافة هامش
                worksheet.column_dimensions[chr(65 + i)].width = max_length
        
        output.seek(0)
        
        # إرسال الملف
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
    
    @staticmethod
    def format_data_for_export(data, formatters=None):
        """
        تنسيق البيانات للتصدير
        
        المعلمات:
            data: البيانات المراد تنسيقها (قائمة من القواميس)
            formatters: دالات التنسيق (قاموس يحتوي على المفتاح ودالة التنسيق)
            
        العائد:
            البيانات المنسقة
        """
        if not formatters:
            return data
        
        formatted_data = []
        for item in data:
            formatted_item = {}
            for key, value in item.items():
                if key in formatters:
                    formatted_item[key] = formatters[key](value)
                else:
                    formatted_item[key] = value
            formatted_data.append(formatted_item)
        
        return formatted_data
    
    @staticmethod
    def prepare_invoice_data(invoices):
        """
        تحضير بيانات الفواتير للتصدير
        
        المعلمات:
            invoices: قائمة كائنات الفاتورة
            
        العائد:
            بيانات الفواتير المنسقة للتصدير
        """
        data = []
        for invoice in invoices:
            # الحصول على اسم العميل
            client_name = invoice.client.name if invoice.client else 'غير محدد'
            
            # تحضير بيانات الفاتورة
            invoice_data = {
                'invoice_number': invoice.invoice_number,
                'client': client_name,
                'issue_date': invoice.issue_date.strftime('%Y-%m-%d') if invoice.issue_date else '',
                'due_date': invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                'status': invoice.status,
                'subtotal': invoice.subtotal,
                'tax_amount': invoice.tax_amount,
                'discount_amount': invoice.discount_amount,
                'total_amount': invoice.total_amount,
                'amount_paid': invoice.amount_paid,
                'amount_due': invoice.amount_due,
                'currency': invoice.currency
            }
            data.append(invoice_data)
        
        # تعريف أعمدة التصدير
        columns = {
            'رقم الفاتورة': 'invoice_number',
            'العميل': 'client',
            'تاريخ الإصدار': 'issue_date',
            'تاريخ الاستحقاق': 'due_date',
            'الحالة': 'status',
            'المجموع الفرعي': 'subtotal',
            'مبلغ الضريبة': 'tax_amount',
            'مبلغ الخصم': 'discount_amount',
            'المبلغ الإجمالي': 'total_amount',
            'المبلغ المدفوع': 'amount_paid',
            'المبلغ المستحق': 'amount_due',
            'العملة': 'currency'
        }
        
        return data, columns
    
    @staticmethod
    def prepare_transaction_data(transactions):
        """
        تحضير بيانات المعاملات للتصدير
        
        المعلمات:
            transactions: قائمة كائنات المعاملة
            
        العائد:
            بيانات المعاملات المنسقة للتصدير
        """
        data = []
        for transaction in transactions:
            # الحصول على اسم العميل والمشروع
            client_name = transaction.client.name if transaction.client else 'غير محدد'
            project_name = transaction.project.name if transaction.project else 'غير محدد'
            
            # تحضير بيانات المعاملة
            transaction_data = {
                'date': transaction.date.strftime('%Y-%m-%d') if transaction.date else '',
                'type': transaction.type,
                'amount': transaction.amount,
                'description': transaction.description,
                'category': transaction.category,
                'payment_method': transaction.payment_method,
                'client': client_name,
                'project': project_name,
                'reference': transaction.reference
            }
            data.append(transaction_data)
        
        # تعريف أعمدة التصدير
        columns = {
            'التاريخ': 'date',
            'النوع': 'type',
            'المبلغ': 'amount',
            'الوصف': 'description',
            'الفئة': 'category',
            'طريقة الدفع': 'payment_method',
            'العميل': 'client',
            'المشروع': 'project',
            'المرجع': 'reference'
        }
        
        return data, columns
    
    @staticmethod
    def prepare_client_data(clients):
        """
        تحضير بيانات العملاء للتصدير
        
        المعلمات:
            clients: قائمة كائنات العميل
            
        العائد:
            بيانات العملاء المنسقة للتصدير
        """
        data = []
        for client in clients:
            # تحضير بيانات العميل
            client_data = {
                'name': client.name,
                'email': client.email,
                'phone': client.phone,
                'company': client.company,
                'address': client.address,
                'created_at': client.created_at.strftime('%Y-%m-%d') if client.created_at else '',
                'last_contact_date': client.last_contact_date.strftime('%Y-%m-%d') if client.last_contact_date else ''
            }
            data.append(client_data)
        
        # تعريف أعمدة التصدير
        columns = {
            'الاسم': 'name',
            'البريد الإلكتروني': 'email',
            'الهاتف': 'phone',
            'الشركة': 'company',
            'العنوان': 'address',
            'تاريخ الإنشاء': 'created_at',
            'تاريخ آخر اتصال': 'last_contact_date'
        }
        
        return data, columns
    
    @staticmethod
    def prepare_project_data(projects):
        """
        تحضير بيانات المشاريع للتصدير
        
        المعلمات:
            projects: قائمة كائنات المشروع
            
        العائد:
            بيانات المشاريع المنسقة للتصدير
        """
        data = []
        for project in projects:
            # الحصول على اسم العميل
            client_name = project.client.name if project.client else 'غير محدد'
            
            # تحضير بيانات المشروع
            project_data = {
                'name': project.name,
                'client': client_name,
                'status': project.status,
                'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else '',
                'end_date': project.end_date.strftime('%Y-%m-%d') if project.end_date else '',
                'budget': project.budget,
                'description': project.description
            }
            data.append(project_data)
        
        # تعريف أعمدة التصدير
        columns = {
            'الاسم': 'name',
            'العميل': 'client',
            'الحالة': 'status',
            'تاريخ البدء': 'start_date',
            'تاريخ الانتهاء': 'end_date',
            'الميزانية': 'budget',
            'الوصف': 'description'
        }
        
        return data, columns
    
    @staticmethod
    def prepare_financial_report_data(transactions, start_date, end_date):
        """
        تحضير بيانات التقرير المالي للتصدير
        
        المعلمات:
            transactions: قائمة كائنات المعاملة
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        العائد:
            بيانات التقرير المالي المنسقة للتصدير
        """
        # تصنيف المعاملات حسب النوع والفئة
        income_by_category = {}
        expense_by_category = {}
        
        for transaction in transactions:
            if transaction.type == 'income':
                category = transaction.category or 'غير مصنف'
                if category not in income_by_category:
                    income_by_category[category] = 0
                income_by_category[category] += transaction.amount
            elif transaction.type == 'expense':
                category = transaction.category or 'غير مصنف'
                if category not in expense_by_category:
                    expense_by_category[category] = 0
                expense_by_category[category] += transaction.amount
        
        # تحضير بيانات الدخل
        income_data = []
        for category, amount in income_by_category.items():
            income_data.append({
                'category': category,
                'amount': amount,
                'type': 'دخل'
            })
        
        # تحضير بيانات المصروفات
        expense_data = []
        for category, amount in expense_by_category.items():
            expense_data.append({
                'category': category,
                'amount': amount,
                'type': 'مصروف'
            })
        
        # دمج البيانات
        data = income_data + expense_data
        
        # إضافة الإجماليات
        total_income = sum(income_by_category.values())
        total_expense = sum(expense_by_category.values())
        net_profit = total_income - total_expense
        profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
        
        data.append({
            'category': 'إجمالي الدخل',
            'amount': total_income,
            'type': 'ملخص'
        })
        
        data.append({
            'category': 'إجمالي المصروفات',
            'amount': total_expense,
            'type': 'ملخص'
        })
        
        data.append({
            'category': 'صافي الربح',
            'amount': net_profit,
            'type': 'ملخص'
        })
        
        data.append({
            'category': 'هامش الربح',
            'amount': f"{profit_margin:.2f}%",
            'type': 'ملخص'
        })
        
        # تعريف أعمدة التصدير
        columns = {
            'الفئة': 'category',
            'المبلغ': 'amount',
            'النوع': 'type'
        }
        
        return data, columns