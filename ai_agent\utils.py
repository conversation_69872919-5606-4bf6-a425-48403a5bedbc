#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أدوات مساعدة للوكيل الذكي المحسن
"""

import re
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score

class AIAgentUtils:
    """أدوات مساعدة للوكيل الذكي"""
    
    @staticmethod
    def clean_arabic_text(text: str) -> str:
        """تنظيف النص العربي"""
        if not text:
            return ""
        
        # إزالة الأحرف الخاصة والأرقام غير الضرورية
        text = re.sub(r'[^\u0600-\u06FF\s\d\.\,\?\!]', ' ', text)
        
        # توحيد المسافات
        text = re.sub(r'\s+', ' ', text)
        
        # إزالة المسافات في البداية والنهاية
        text = text.strip()
        
        return text
    
    @staticmethod
    def extract_numbers(text: str) -> List[float]:
        """استخراج الأرقام من النص"""
        numbers = re.findall(r'\d+\.?\d*', text)
        return [float(num) for num in numbers if num]
    
    @staticmethod
    def calculate_similarity(text1: str, text2: str) -> float:
        """حساب التشابه بين نصين"""
        # تحويل النصوص إلى مجموعات كلمات
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        # حساب معامل Jaccard
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    @staticmethod
    def format_currency(amount: float, currency: str = "ر.س") -> str:
        """تنسيق العملة"""
        try:
            return f"{amount:,.2f} {currency}"
        except (ValueError, TypeError):
            return f"0.00 {currency}"
    
    @staticmethod
    def format_percentage(value: float) -> str:
        """تنسيق النسبة المئوية"""
        try:
            return f"{value:.1f}%"
        except (ValueError, TypeError):
            return "0.0%"
    
    @staticmethod
    def calculate_growth_rate(current: float, previous: float) -> float:
        """حساب معدل النمو"""
        if previous == 0:
            return 0.0
        return ((current - previous) / previous) * 100
    
    @staticmethod
    def get_date_range(period: str, count: int = 1) -> Tuple[datetime, datetime]:
        """الحصول على نطاق تاريخي"""
        end_date = datetime.now()
        
        if period == 'day':
            start_date = end_date - timedelta(days=count)
        elif period == 'week':
            start_date = end_date - timedelta(weeks=count)
        elif period == 'month':
            start_date = end_date - timedelta(days=count * 30)
        elif period == 'year':
            start_date = end_date - timedelta(days=count * 365)
        else:
            start_date = end_date - timedelta(days=count)
        
        return start_date, end_date
    
    @staticmethod
    def categorize_amount(amount: float, thresholds: Dict[str, float]) -> str:
        """تصنيف المبلغ حسب العتبات"""
        for category, threshold in sorted(thresholds.items(), key=lambda x: x[1], reverse=True):
            if amount >= threshold:
                return category
        return 'low'
    
    @staticmethod
    def generate_insights(data: Dict[str, Any]) -> List[Dict[str, str]]:
        """توليد رؤى من البيانات"""
        insights = []
        
        # تحليل الاتجاهات
        if 'trend' in data:
            trend = data['trend']
            if trend == 'upward':
                insights.append({
                    'type': 'positive',
                    'title': 'اتجاه إيجابي',
                    'description': 'البيانات تظهر اتجاهاً صاعداً مما يشير إلى نمو جيد'
                })
            elif trend == 'downward':
                insights.append({
                    'type': 'warning',
                    'title': 'اتجاه سلبي',
                    'description': 'البيانات تظهر اتجاهاً هابطاً يحتاج إلى انتباه'
                })
        
        # تحليل النمو
        if 'growth_rate' in data:
            growth = data['growth_rate']
            if growth > 20:
                insights.append({
                    'type': 'excellent',
                    'title': 'نمو ممتاز',
                    'description': f'معدل نمو {growth:.1f}% يعتبر ممتازاً'
                })
            elif growth > 10:
                insights.append({
                    'type': 'good',
                    'title': 'نمو جيد',
                    'description': f'معدل نمو {growth:.1f}% يعتبر جيداً'
                })
            elif growth < 0:
                insights.append({
                    'type': 'danger',
                    'title': 'انخفاض',
                    'description': f'معدل نمو سلبي {growth:.1f}% يحتاج إلى تدخل'
                })
        
        return insights
    
    @staticmethod
    def create_conversation_id() -> str:
        """إنشاء معرف محادثة فريد"""
        timestamp = datetime.now().isoformat()
        random_str = str(np.random.randint(10000, 99999))
        return hashlib.md5(f"{timestamp}_{random_str}".encode()).hexdigest()[:12]
    
    @staticmethod
    def validate_financial_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة البيانات المالية"""
        errors = []
        warnings = []
        
        # التحقق من الأرقام السالبة
        for key, value in data.items():
            if isinstance(value, (int, float)):
                if key in ['revenue', 'income', 'assets'] and value < 0:
                    warnings.append(f"{key} لا يجب أن يكون سالباً")
                elif key in ['expenses', 'liabilities'] and value < 0:
                    errors.append(f"{key} لا يمكن أن يكون سالباً")
        
        # التحقق من التوازن المحاسبي
        if 'assets' in data and 'liabilities' in data and 'equity' in data:
            assets = data['assets']
            liabilities = data['liabilities']
            equity = data['equity']
            
            if abs(assets - (liabilities + equity)) > 0.01:
                errors.append("معادلة الميزانية غير متوازنة: الأصول ≠ الخصوم + حقوق الملكية")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    @staticmethod
    def calculate_financial_ratios(data: Dict[str, float]) -> Dict[str, float]:
        """حساب النسب المالية"""
        ratios = {}
        
        try:
            # نسب السيولة
            if 'current_assets' in data and 'current_liabilities' in data:
                if data['current_liabilities'] > 0:
                    ratios['current_ratio'] = data['current_assets'] / data['current_liabilities']
            
            # نسب الربحية
            if 'net_income' in data and 'revenue' in data:
                if data['revenue'] > 0:
                    ratios['profit_margin'] = data['net_income'] / data['revenue']
            
            if 'net_income' in data and 'equity' in data:
                if data['equity'] > 0:
                    ratios['roe'] = data['net_income'] / data['equity']
            
            # نسب الكفاءة
            if 'revenue' in data and 'total_assets' in data:
                if data['total_assets'] > 0:
                    ratios['asset_turnover'] = data['revenue'] / data['total_assets']
            
            # نسب المديونية
            if 'total_debt' in data and 'total_assets' in data:
                if data['total_assets'] > 0:
                    ratios['debt_ratio'] = data['total_debt'] / data['total_assets']
        
        except (ZeroDivisionError, TypeError, KeyError):
            pass
        
        return ratios
    
    @staticmethod
    def analyze_trend(values: List[float], periods: List[str] = None) -> Dict[str, Any]:
        """تحليل الاتجاه"""
        if len(values) < 2:
            return {'trend': 'insufficient_data', 'slope': 0, 'r_squared': 0}
        
        # إنشاء مصفوفة X (الفترات الزمنية)
        x = np.arange(len(values)).reshape(-1, 1)
        y = np.array(values)
        
        # حساب الانحدار الخطي
        from sklearn.linear_model import LinearRegression
        model = LinearRegression()
        model.fit(x, y)
        
        slope = model.coef_[0]
        r_squared = model.score(x, y)
        
        # تحديد الاتجاه
        if slope > 0.1:
            trend = 'upward'
        elif slope < -0.1:
            trend = 'downward'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'slope': slope,
            'r_squared': r_squared,
            'prediction': model.predict([[len(values)]])[0]
        }
    
    @staticmethod
    def segment_clients(client_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تجميع العملاء"""
        if len(client_data) < 3:
            return {'status': 'insufficient_data', 'segments': []}
        
        # تحضير البيانات
        features = []
        for client in client_data:
            features.append([
                client.get('total_spent', 0),
                client.get('frequency', 0),
                client.get('recency', 0)
            ])
        
        features = np.array(features)
        
        # تطبيع البيانات
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # تطبيق K-means
        from sklearn.cluster import KMeans
        
        # تحديد العدد الأمثل للمجموعات
        best_k = 3
        best_score = -1
        
        for k in range(2, min(6, len(client_data))):
            kmeans = KMeans(n_clusters=k, random_state=42)
            labels = kmeans.fit_predict(features_scaled)
            score = silhouette_score(features_scaled, labels)
            
            if score > best_score:
                best_score = score
                best_k = k
        
        # التجميع النهائي
        kmeans = KMeans(n_clusters=best_k, random_state=42)
        labels = kmeans.fit_predict(features_scaled)
        
        # تحليل المجموعات
        segments = []
        for i in range(best_k):
            segment_clients = [client_data[j] for j in range(len(client_data)) if labels[j] == i]
            
            avg_spent = np.mean([c.get('total_spent', 0) for c in segment_clients])
            avg_frequency = np.mean([c.get('frequency', 0) for c in segment_clients])
            
            # تصنيف المجموعة
            if avg_spent > 10000 and avg_frequency > 10:
                segment_type = 'high_value'
            elif avg_spent > 5000 and avg_frequency > 5:
                segment_type = 'medium_value'
            else:
                segment_type = 'low_value'
            
            segments.append({
                'id': i,
                'type': segment_type,
                'size': len(segment_clients),
                'avg_spent': avg_spent,
                'avg_frequency': avg_frequency,
                'clients': segment_clients
            })
        
        return {
            'status': 'success',
            'optimal_clusters': best_k,
            'silhouette_score': best_score,
            'segments': segments
        }
    
    @staticmethod
    def detect_anomalies(data: List[Dict[str, Any]], feature: str = 'amount') -> List[int]:
        """كشف الشذوذ في البيانات"""
        if len(data) < 10:
            return []
        
        values = [item.get(feature, 0) for item in data]
        values = np.array(values).reshape(-1, 1)
        
        from sklearn.ensemble import IsolationForest
        
        # تطبيق Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels = iso_forest.fit_predict(values)
        
        # إرجاع فهارس الشذوذ
        anomaly_indices = [i for i, label in enumerate(anomaly_labels) if label == -1]
        
        return anomaly_indices


class SimpleArabicNLP:
    """معالج بسيط للغة العربية"""

    def __init__(self):
        """تهيئة المعالج"""
        # كلمات الإيقاف العربية الأساسية
        self.stop_words = {
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'التي', 'الذي', 'التي', 'اللذان', 'اللتان', 'اللذين', 'اللتين',
            'هو', 'هي', 'هم', 'هن', 'أنت', 'أنتم', 'أنتن', 'أنا', 'نحن',
            'كان', 'كانت', 'كانوا', 'كن', 'يكون', 'تكون', 'يكونوا', 'يكن',
            'لا', 'لم', 'لن', 'ما', 'لكن', 'لكن', 'غير', 'سوى', 'إلا',
            'قد', 'قال', 'قالت', 'يقول', 'تقول', 'أم', 'أو', 'إما',
            'كل', 'بعض', 'جميع', 'كلا', 'كلتا', 'بين', 'خلال', 'أثناء'
        }

        # كلمات مفتاحية محاسبية
        self.accounting_keywords = {
            'فاتورة', 'فواتير', 'عميل', 'عملاء', 'مشروع', 'مشاريع',
            'ربح', 'أرباح', 'خسارة', 'خسائر', 'مبيعات', 'مشتريات',
            'إيرادات', 'مصروفات', 'أصول', 'خصوم', 'ميزانية',
            'تدفق', 'نقدي', 'تحليل', 'تقرير', 'حساب', 'احسب',
            'اظهر', 'عرض', 'قائمة', 'ملخص', 'إحصائيات'
        }

    def extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية من النص"""
        if not text:
            return []

        # تنظيف النص
        text = re.sub(r'[^\u0600-\u06FF\s]', ' ', text)
        words = text.split()

        # إزالة كلمات الإيقاف
        keywords = []
        for word in words:
            word = word.strip()
            if word and word not in self.stop_words and len(word) > 2:
                keywords.append(word)

        return keywords

    def identify_intent(self, text: str) -> str:
        """تحديد نية المستخدم"""
        text_lower = text.lower()

        # تحيات
        greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء']
        if any(greeting in text_lower for greeting in greetings):
            return 'greeting'

        # أسئلة محاسبية
        accounting_terms = ['محاسبة', 'مبدأ', 'قائمة', 'ميزانية', 'نسبة']
        if any(term in text_lower for term in accounting_terms):
            return 'accounting_question'

        # طلبات حسابية
        calculation_terms = ['احسب', 'حساب', 'كم', 'مجموع', 'متوسط']
        if any(term in text_lower for term in calculation_terms):
            return 'calculation'

        # طلبات تحليل
        analysis_terms = ['تحليل', 'تقرير', 'إحصائيات', 'اتجاه']
        if any(term in text_lower for term in analysis_terms):
            return 'analysis_request'

        # استعلامات البيانات
        data_terms = ['اظهر', 'عرض', 'قائمة', 'فواتير', 'عملاء']
        if any(term in text_lower for term in data_terms):
            return 'data_query'

        # طلبات المساعدة
        help_terms = ['مساعدة', 'كيف', 'ماذا', 'أين']
        if any(term in text_lower for term in help_terms):
            return 'help'

        return 'general'

    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """استخراج الكيانات من النص"""
        entities = {
            'amounts': [],
            'dates': [],
            'names': [],
            'accounting_terms': []
        }

        # استخراج المبالغ
        amounts = re.findall(r'\d+\.?\d*', text)
        entities['amounts'] = [float(amount) for amount in amounts if amount]

        # استخراج التواريخ (تنسيق بسيط)
        dates = re.findall(r'\d{1,2}/\d{1,2}/\d{4}', text)
        entities['dates'] = dates

        # استخراج المصطلحات المحاسبية
        words = text.split()
        for word in words:
            if word in self.accounting_keywords:
                entities['accounting_terms'].append(word)

        return entities

    def normalize_text(self, text: str) -> str:
        """تطبيع النص العربي"""
        if not text:
            return ""

        # توحيد الهمزات
        text = re.sub(r'[أإآ]', 'ا', text)
        text = re.sub(r'[ؤ]', 'و', text)
        text = re.sub(r'[ئ]', 'ي', text)
        text = re.sub(r'[ة]', 'ه', text)

        # إزالة التشكيل
        text = re.sub(r'[\u064B-\u0652]', '', text)

        # توحيد المسافات
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """حساب التشابه بين نصين"""
        words1 = set(self.extract_keywords(text1))
        words2 = set(self.extract_keywords(text2))

        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0
