{% extends "base.html" %}

{% block title %}إضافة دفعة للفاتورة رقم {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة دفعة للفاتورة رقم {{ invoice.invoice_number }}</h1>
            <p class="text-muted">تسجيل دفعة جديدة من العميل</p>
        </div>
        <div>
            <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى الفاتورة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>تفاصيل الدفعة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           min="0" step="0.01" max="{{ remaining_amount }}" 
                                           placeholder="0.00" required>
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <div class="form-text">الحد الأقصى: {{ remaining_amount|format_currency }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date" class="form-label">تاريخ الدفعة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date" name="date" 
                                       value="{{ today }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                    <option value="online">دفع إلكتروني</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="description" class="form-label">وصف الدفعة</label>
                                <input type="text" class="form-control" id="description" name="description" 
                                       placeholder="وصف اختياري للدفعة">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ الدفعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Invoice Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>ملخص الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-6">رقم الفاتورة:</div>
                        <div class="col-6 text-end"><strong>{{ invoice.invoice_number }}</strong></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">تاريخ الإصدار:</div>
                        <div class="col-6 text-end">{{ invoice.issue_date.strftime('%Y-%m-%d') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">تاريخ الاستحقاق:</div>
                        <div class="col-6 text-end">{{ invoice.due_date.strftime('%Y-%m-%d') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">الحالة:</div>
                        <div class="col-6 text-end">
                            {% if invoice.status == 'paid' %}
                                <span class="badge bg-success">مدفوعة</span>
                            {% elif invoice.status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوعة</span>
                            {% elif invoice.status == 'partially_paid' %}
                                <span class="badge bg-warning">مدفوعة جزئياً</span>
                            {% elif invoice.status == 'overdue' %}
                                <span class="badge bg-dark">متأخرة</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ invoice.status }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <h6>{{ client.name }}</h6>
                    {% if client.email %}
                    <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ client.email }}</p>
                    {% endif %}
                    {% if client.phone %}
                    <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ client.phone }}</p>
                    {% endif %}
                    {% if client.address %}
                    <p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>{{ client.address }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>ملخص المدفوعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-7">إجمالي الفاتورة:</div>
                        <div class="col-5 text-end"><strong>{{ invoice.total_amount|format_currency }}</strong></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-7">المبلغ المدفوع:</div>
                        <div class="col-5 text-end text-success">
                            <strong>{{ (invoice.total_amount - remaining_amount)|format_currency }}</strong>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-7"><strong>المبلغ المتبقي:</strong></div>
                        <div class="col-5 text-end">
                            <strong class="text-danger">{{ remaining_amount|format_currency }}</strong>
                        </div>
                    </div>
                    
                    {% if remaining_amount <= 0 %}
                    <div class="alert alert-success mt-3 mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        تم دفع الفاتورة بالكامل
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const remainingAmount = {{ remaining_amount }};
    
    // Set default amount to remaining amount
    amountInput.value = remainingAmount.toFixed(2);
    
    // Add quick amount buttons
    const amountGroup = amountInput.closest('.input-group');
    const quickAmounts = document.createElement('div');
    quickAmounts.className = 'mt-2';
    quickAmounts.innerHTML = `
        <small class="text-muted d-block mb-1">مبالغ سريعة:</small>
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-secondary" onclick="setAmount(${(remainingAmount * 0.25).toFixed(2)})">25%</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setAmount(${(remainingAmount * 0.5).toFixed(2)})">50%</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setAmount(${(remainingAmount * 0.75).toFixed(2)})">75%</button>
            <button type="button" class="btn btn-outline-primary" onclick="setAmount(${remainingAmount.toFixed(2)})">الكل</button>
        </div>
    `;
    
    amountGroup.parentNode.insertBefore(quickAmounts, amountGroup.nextSibling);
    
    // Validate amount
    amountInput.addEventListener('input', function() {
        const value = parseFloat(this.value);
        if (value > remainingAmount) {
            this.setCustomValidity('المبلغ لا يمكن أن يكون أكبر من المبلغ المتبقي');
        } else if (value <= 0) {
            this.setCustomValidity('المبلغ يجب أن يكون أكبر من صفر');
        } else {
            this.setCustomValidity('');
        }
    });
});

function setAmount(amount) {
    document.getElementById('amount').value = amount;
    document.getElementById('amount').dispatchEvent(new Event('input'));
}
</script>
{% endblock %}
