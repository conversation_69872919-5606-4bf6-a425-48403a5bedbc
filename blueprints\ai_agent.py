#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة الوكيل الذكي
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import logging

# استيراد النماذج
from models.user import User
from models.project import Project
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction
from extensions import db

# استيراد الوكيل الذكي
from ai_agent.agent import SmartBizAgent
from ai_agent.analytics import SmartBizAnalytics
from ai_agent.recommendations import SmartBizRecommendations

# إنشاء مخطط Blueprint
ai_agent_bp = Blueprint('ai_agent', __name__, url_prefix='/ai')

# إعداد السجل
logger = logging.getLogger(__name__)

@ai_agent_bp.route('/')
@login_required
def ai_dashboard():
    """
    لوحة تحكم الوكيل الذكي
    """
    try:
        # إنشاء كائن الوكيل الذكي
        agent = SmartBizAgent()
        analytics = SmartBizAnalytics()
        recommendations = SmartBizRecommendations(db)
        
        # الحصول على التوصيات
        all_recommendations = recommendations.get_all_recommendations(current_user.id)
        
        # الحصول على التحليلات
        business_insights = agent.get_business_insights(current_user.id)
        
        # الحصول على الإحصائيات
        stats = analytics.get_user_statistics(current_user.id)
        
        return render_template(
            'ai_agent/dashboard.html',
            recommendations=all_recommendations.get('recommendations', []),
            insights=business_insights,
            stats=stats
        )
        
    except Exception as e:
        logger.error(f"Error loading AI dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة تحكم الوكيل الذكي. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('ai_agent/dashboard.html', recommendations=[], insights=[], stats={})

@ai_agent_bp.route('/chat')
@login_required
def ai_chat():
    """
    واجهة المحادثة مع الوكيل الذكي
    """
    return render_template('ai_agent/chat.html')

@ai_agent_bp.route('/recommendations')
@login_required
def recommendations():
    """
    صفحة التوصيات الذكية
    """
    try:
        recommendations_engine = SmartBizRecommendations(db)
        all_recommendations = recommendations_engine.get_all_recommendations(current_user.id)
        
        return render_template(
            'ai_agent/recommendations.html',
            recommendations=all_recommendations.get('recommendations', []),
            categories=all_recommendations.get('categories', {}),
            count=all_recommendations.get('count', 0)
        )
        
    except Exception as e:
        logger.error(f"Error loading recommendations: {str(e)}")
        flash('حدث خطأ أثناء تحميل التوصيات. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('ai_agent/recommendations.html', recommendations=[], categories={}, count=0)

@ai_agent_bp.route('/analytics')
@login_required
def analytics():
    """
    صفحة التحليلات الذكية
    """
    try:
        analytics_engine = SmartBizAnalytics()
        
        # الحصول على التحليلات المختلفة
        sales_analysis = analytics_engine.analyze_sales_trends(current_user.id)
        cash_flow_analysis = analytics_engine.analyze_cash_flow(current_user.id)
        client_analysis = analytics_engine.analyze_client_behavior(current_user.id)
        project_analysis = analytics_engine.analyze_project_performance(current_user.id)
        
        return render_template(
            'ai_agent/analytics.html',
            sales_analysis=sales_analysis,
            cash_flow_analysis=cash_flow_analysis,
            client_analysis=client_analysis,
            project_analysis=project_analysis
        )
        
    except Exception as e:
        logger.error(f"Error loading analytics: {str(e)}")
        flash('حدث خطأ أثناء تحميل التحليلات. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('ai_agent/analytics.html')

# واجهات برمجة التطبيقات API

@ai_agent_bp.route('/api/chat', methods=['POST'])
@login_required
def api_chat():
    """
    واجهة برمجة التطبيقات المحسنة للمحادثة مع الوكيل الذكي
    """
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        context = data.get('context', {})

        if not message:
            return jsonify({
                'status': 'error',
                'message': 'يرجى إدخال رسالة'
            }), 400

        # إنشاء كائن الوكيل الذكي المحسن
        agent = SmartBizAgent(db)

        # معالجة الرسالة والحصول على الرد المحسن
        response = agent.process_message(message, current_user.id, context)

        # إضافة معلومات إضافية للرد
        response['timestamp'] = datetime.now().isoformat()
        response['user_id'] = current_user.id

        return jsonify(response)

    except Exception as e:
        logger.error(f"API Error - AI chat: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء معالجة الرسالة',
            'error_details': str(e) if current_app.debug else None
        }), 500

@ai_agent_bp.route('/api/recommendations')
@login_required
def api_recommendations():
    """
    واجهة برمجة التطبيقات للتوصيات
    """
    try:
        recommendations_engine = SmartBizRecommendations(db)
        all_recommendations = recommendations_engine.get_all_recommendations(current_user.id)
        
        return jsonify({
            'status': 'success',
            'data': all_recommendations
        })
        
    except Exception as e:
        logger.error(f"API Error - recommendations: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب التوصيات'
        }), 500

@ai_agent_bp.route('/api/insights')
@login_required
def api_insights():
    """
    واجهة برمجة التطبيقات للرؤى التجارية
    """
    try:
        agent = SmartBizAgent()
        insights = agent.get_business_insights(current_user.id)
        
        return jsonify({
            'status': 'success',
            'data': insights
        })
        
    except Exception as e:
        logger.error(f"API Error - insights: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الرؤى التجارية'
        }), 500

@ai_agent_bp.route('/api/analytics/<analysis_type>')
@login_required
def api_analytics(analysis_type):
    """
    واجهة برمجة التطبيقات للتحليلات
    """
    try:
        analytics_engine = SmartBizAnalytics()
        
        if analysis_type == 'sales':
            data = analytics_engine.analyze_sales_trends(current_user.id)
        elif analysis_type == 'cash_flow':
            data = analytics_engine.analyze_cash_flow(current_user.id)
        elif analysis_type == 'clients':
            data = analytics_engine.analyze_client_behavior(current_user.id)
        elif analysis_type == 'projects':
            data = analytics_engine.analyze_project_performance(current_user.id)
        else:
            return jsonify({
                'status': 'error',
                'message': 'نوع التحليل غير مدعوم'
            }), 400
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        logger.error(f"API Error - analytics {analysis_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'حدث خطأ أثناء جلب تحليل {analysis_type}'
        }), 500

@ai_agent_bp.route('/api/voice/process', methods=['POST'])
@login_required
def api_voice_process():
    """
    واجهة برمجة التطبيقات لمعالجة الأوامر الصوتية
    """
    try:
        # التحقق من وجود ملف صوتي
        if 'audio' not in request.files:
            return jsonify({
                'status': 'error',
                'message': 'لم يتم العثور على ملف صوتي'
            }), 400
        
        audio_file = request.files['audio']
        
        if audio_file.filename == '':
            return jsonify({
                'status': 'error',
                'message': 'لم يتم تحديد ملف صوتي'
            }), 400
        
        # معالجة الملف الصوتي
        agent = SmartBizAgent()
        result = agent.process_voice_command(audio_file, current_user.id)
        
        return jsonify({
            'status': 'success',
            'data': result
        })
        
    except Exception as e:
        logger.error(f"API Error - voice processing: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء معالجة الأمر الصوتي'
        }), 500

@ai_agent_bp.route('/api/advanced_analysis', methods=['POST'])
@login_required
def api_advanced_analysis():
    """
    واجهة برمجة التطبيقات للتحليل المتقدم
    """
    try:
        data = request.get_json()
        analysis_type = data.get('type', 'sales_trend')
        parameters = data.get('parameters', {})

        agent = SmartBizAgent(db)

        if analysis_type == 'sales_trend':
            period = parameters.get('period', 'month')
            months = parameters.get('months', 6)
            result = agent.analyze_sales_trend(current_user.id, period, months)
        elif analysis_type == 'client_segments':
            result = agent.analyze_client_segments(current_user.id)
        elif analysis_type == 'cash_flow':
            period = parameters.get('period', 'month')
            months = parameters.get('months', 12)
            result = agent.analyze_cash_flow(current_user.id, period, months)
        elif analysis_type == 'unpaid_alerts':
            days_threshold = parameters.get('days_threshold', 30)
            result = agent.get_unpaid_invoices_alert(current_user.id, days_threshold)
        elif analysis_type == 'business_insights':
            result = agent.get_business_insights(current_user.id)
        else:
            return jsonify({
                'status': 'error',
                'message': 'نوع التحليل غير مدعوم'
            }), 400

        return jsonify({
            'status': 'success',
            'data': result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"API Error - advanced analysis: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء التحليل المتقدم',
            'error_details': str(e) if current_app.debug else None
        }), 500

@ai_agent_bp.route('/api/smart_suggestions', methods=['GET'])
@login_required
def api_smart_suggestions():
    """
    واجهة برمجة التطبيقات للاقتراحات الذكية
    """
    try:
        agent = SmartBizAgent(db)

        # الحصول على اقتراحات ذكية بناءً على بيانات المستخدم
        suggestions = []

        # اقتراحات بناءً على الفواتير المتأخرة
        overdue_alerts = agent.get_unpaid_invoices_alert(current_user.id)
        if overdue_alerts.get('has_alerts'):
            suggestions.append({
                'type': 'alert',
                'title': 'فواتير متأخرة',
                'message': f"لديك {overdue_alerts['count']} فواتير متأخرة بقيمة {overdue_alerts['total_overdue']:,.2f}",
                'action': 'review_overdue_invoices',
                'priority': 'high'
            })

        # اقتراحات بناءً على تحليل المبيعات
        sales_analysis = agent.analyze_sales_trend(current_user.id)
        if sales_analysis.get('status') == 'success':
            if sales_analysis['trend'] == 'downward':
                suggestions.append({
                    'type': 'improvement',
                    'title': 'تحسين المبيعات',
                    'message': 'اتجاه المبيعات في انخفاض. ننصح بمراجعة استراتيجية التسويق.',
                    'action': 'review_sales_strategy',
                    'priority': 'medium'
                })
            elif sales_analysis['trend'] == 'upward':
                suggestions.append({
                    'type': 'opportunity',
                    'title': 'فرصة نمو',
                    'message': f"المبيعات في نمو بمعدل {sales_analysis['growth_rate']:.1f}%. فرصة جيدة للتوسع.",
                    'action': 'explore_expansion',
                    'priority': 'low'
                })

        # اقتراحات عامة
        suggestions.extend([
            {
                'type': 'tip',
                'title': 'نصيحة يومية',
                'message': 'راجع تقاريرك المالية بانتظام لاتخاذ قرارات مدروسة.',
                'action': 'view_reports',
                'priority': 'low'
            },
            {
                'type': 'feature',
                'title': 'ميزة جديدة',
                'message': 'جرب التحليل الذكي للعملاء لفهم سلوك عملائك بشكل أفضل.',
                'action': 'try_client_analysis',
                'priority': 'low'
            }
        ])

        return jsonify({
            'status': 'success',
            'suggestions': suggestions[:5],  # أول 5 اقتراحات
            'count': len(suggestions),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"API Error - smart suggestions: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب الاقتراحات الذكية'
        }), 500

@ai_agent_bp.route('/api/learning_progress', methods=['GET'])
@login_required
def api_learning_progress():
    """
    واجهة برمجة التطبيقات لتتبع تقدم التعلم
    """
    try:
        agent = SmartBizAgent(db)

        # تحليل تفاعل المستخدم مع النظام
        user_prefs = agent.user_preferences.get(current_user.id, {})

        progress = {
            'interaction_count': user_prefs.get('interaction_count', 0),
            'frequent_topics': user_prefs.get('favorite_topics', {}),
            'learning_areas': [],
            'recommendations': []
        }

        # تحديد مجالات التعلم المقترحة
        if progress['interaction_count'] < 10:
            progress['learning_areas'].append({
                'area': 'أساسيات المحاسبة',
                'description': 'تعلم المبادئ الأساسية للمحاسبة',
                'progress': 20
            })

        if 'فواتير' in progress['frequent_topics']:
            progress['learning_areas'].append({
                'area': 'إدارة الفواتير',
                'description': 'تحسين عملية إدارة وتتبع الفواتير',
                'progress': 60
            })

        if 'تحليل' in progress['frequent_topics']:
            progress['learning_areas'].append({
                'area': 'التحليل المالي',
                'description': 'فهم وتطبيق أدوات التحليل المالي',
                'progress': 40
            })

        return jsonify({
            'status': 'success',
            'progress': progress,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"API Error - learning progress: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب تقدم التعلم'
        }), 500
