{% extends "base.html" %}

{% block title %}{{ client.name }} - تفاصيل العميل{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ client.name }}</h1>
            <p class="text-muted">تفاصيل العميل</p>
        </div>
        <div>
            <div class="btn-group">
                <a href="{{ url_for('clients.list_clients') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
                </a>
                <a href="{{ url_for('clients.edit_client', client_id=client.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{{ url_for('invoices.add_invoice') }}?client_id={{ client.id }}" class="btn btn-success">
                    <i class="fas fa-file-invoice me-2"></i>إضافة فاتورة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Client Information -->
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        {% if client.profile_image %}
                        <img src="{{ client.profile_image }}" class="rounded-circle"
                            style="width: 120px; height: 120px; object-fit: cover;" alt="{{ client.name }}">
                        {% else %}
                        <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center text-white"
                            style="width: 120px; height: 120px;">
                            <span class="fs-1 fw-bold">{{ client.name[0].upper() }}</span>
                        </div>
                        {% endif %}
                    </div>
                    <h4 class="mb-1">{{ client.name }}</h4>
                    {% if client.company_name %}
                    <p class="text-muted mb-2">{{ client.company_name }}</p>
                    {% endif %}
                    <div class="mb-3">
                        {% if client.is_active %}
                        <span class="badge bg-success">عميل نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        {% if client.email %}
                        <a href="mailto:{{ client.email }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>إرسال بريد إلكتروني
                        </a>
                        {% endif %}
                        {% if client.phone %}
                        <a href="tel:{{ client.phone }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-phone me-2"></i>اتصال
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book me-2"></i>معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4"><strong>البريد الإلكتروني:</strong></div>
                        <div class="col-8">
                            {% if client.email %}
                            <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>الهاتف:</strong></div>
                        <div class="col-8">
                            {% if client.phone %}
                            <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>العنوان:</strong></div>
                        <div class="col-8">{{ client.address or 'غير محدد' }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>المدينة:</strong></div>
                        <div class="col-8">{{ client.city or 'غير محدد' }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>الدولة:</strong></div>
                        <div class="col-8">{{ client.country or 'غير محدد' }}</div>
                    </div>
                    {% if client.website %}
                    <div class="row mb-2">
                        <div class="col-4"><strong>الموقع الإلكتروني:</strong></div>
                        <div class="col-8">
                            <a href="{{ client.website }}" target="_blank">{{ client.website }}</a>
                        </div>
                    </div>
                    {% endif %}
                    {% if client.tax_number %}
                    <div class="row mb-2">
                        <div class="col-4"><strong>الرقم الضريبي:</strong></div>
                        <div class="col-8">{{ client.tax_number }}</div>
                    </div>
                    {% endif %}
                    {% if client.payment_method %}
                    <div class="row">
                        <div class="col-4"><strong>طريقة الدفع:</strong></div>
                        <div class="col-8">
                            {% if client.payment_method == 'cash' %}
                            <span><i class="fas fa-money-bill-wave text-success me-1"></i>نقدي</span>
                            {% elif client.payment_method == 'bank_transfer' %}
                            <span><i class="fas fa-university text-primary me-1"></i>تحويل بنكي</span>
                            {% elif client.payment_method == 'card' %}
                            <span><i class="fas fa-credit-card text-info me-1"></i>بطاقة ائتمان</span>
                            {% elif client.payment_method == 'digital_wallet' %}
                            <span><i class="fas fa-mobile-alt text-warning me-1"></i>محفظة رقمية</span>
                            {% elif client.payment_method == 'check' %}
                            <span><i class="fas fa-money-check text-secondary me-1"></i>شيك</span>
                            {% else %}
                            <span>{{ client.payment_method }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ client_stats.total_invoices or 0 }}</h4>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ client_stats.total_amount|format_currency }}</h4>
                            <small class="text-muted">إجمالي المبلغ</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-info">{{ client_stats.paid_amount|format_currency }}</h5>
                            <small class="text-muted">المبلغ المدفوع</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ client_stats.unpaid_amount|format_currency }}</h5>
                            <small class="text-muted">المبلغ المستحق</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoices and Activity -->
        <div class="col-lg-8">
            <!-- Recent Invoices -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة
                    </h5>
                    <a href="{{ url_for('invoices.add_invoice') }}?client_id={{ client.id }}"
                        class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة فاتورة
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}"
                                            class="text-decoration-none">
                                            <strong>#{{ invoice.invoice_number }}</strong>
                                        </a>
                                    </td>
                                    <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ invoice.total_amount|format_currency }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                        {% elif invoice.status == 'unpaid' %}
                                        <span class="badge bg-danger">غير مدفوعة</span>
                                        {% elif invoice.status == 'partially_paid' %}
                                        <span class="badge bg-warning">مدفوعة جزئياً</span>
                                        {% elif invoice.status == 'overdue' %}
                                        <span class="badge bg-dark">متأخرة</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}"
                                                class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}"
                                                class="btn btn-outline-secondary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد فواتير</h6>
                        <p class="text-muted">لم يتم إنشاء أي فواتير لهذا العميل بعد.</p>
                        <a href="{{ url_for('invoices.add_invoice') }}?client_id={{ client.id }}"
                            class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة فاتورة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Notes -->
            {% if client.notes %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>الملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ client.notes }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Activity Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء العميل</h6>
                                <p class="text-muted mb-0">{{ client.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% if client.updated_at and client.updated_at != client.created_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم تحديث البيانات</h6>
                                <p class="text-muted mb-0">{{ client.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% endif %}
                        {% for invoice in invoices[:3] %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء فاتورة #{{ invoice.invoice_number }}</h6>
                                <p class="text-muted mb-0">{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }
</style>
{% endblock %}