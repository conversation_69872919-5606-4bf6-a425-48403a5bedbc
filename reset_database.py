#!/usr/bin/env python3
"""
إعادة تعيين قاعدة البيانات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    try:
        print("🔄 إعادة تعيين قاعدة البيانات...")
        
        # حذف قاعدة البيانات القديمة إذا كانت موجودة
        db_path = os.path.join('instance', 'smartbiz.db')
        if os.path.exists(db_path):
            os.remove(db_path)
            print("🗑️ تم حذف قاعدة البيانات القديمة")
        
        # استيراد التطبيق
        from extensions import db
        from models.user import User
        from models.client import Client
        from models.project import Project
        from models.invoice import Invoice
        from models.transaction import Transaction
        from app import create_app
        
        # إنشاء التطبيق
        app = create_app()
        
        # إنشاء قاعدة البيانات الجديدة
        with app.app_context():
            print("📊 إنشاء جداول قاعدة البيانات...")
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
            
            # إنشاء مستخدم تجريبي (اختياري)
            print("👤 إنشاء مستخدم تجريبي...")
            test_user = User()
            test_user.username = "admin"
            test_user.first_name = "مدير"
            test_user.last_name = "النظام"
            test_user.email = "<EMAIL>"
            test_user.company_name = "شركة SmartBiz"
            test_user.phone = "*********"
            test_user.is_active = True
            test_user.is_admin = True
            test_user.api_key = "test-api-key-123"
            test_user.password = "admin123"  # سيتم تشفيرها تلقائياً
            
            db.session.add(test_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم التجريبي:")
            print("   📧 البريد: <EMAIL>")
            print("   🔑 كلمة المرور: admin123")
        
        print("\n🎉 تم إعادة تعيين قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = reset_database()
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق:")
        print("   python smartbiz_run.py")
    else:
        input("اضغط Enter للخروج...")
