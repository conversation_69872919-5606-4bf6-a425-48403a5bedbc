#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
نموذج المشروع
"""

from datetime import datetime
from app import db

class Project(db.Model):
    """
نموذج المشروع - يخزن معلومات المشاريع المختلفة
    """
    __tablename__ = 'projects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    project_type = db.Column(db.String(50), nullable=False)  # تجارة، خدمات، ورش، إلخ
    status = db.Column(db.String(20), default='active')  # active, completed, on_hold, cancelled
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    budget = db.Column(db.Float)
    currency = db.Column(db.String(3), default='MAD')  # العملة (درهم مغربي افتراضيًا)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    clients = db.relationship('Client', secondary='project_clients', backref='client_projects', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='project', lazy='dynamic')
    transactions = db.relationship('Transaction', backref='project', lazy='dynamic')

    def get_total_income(self):
        """
حساب إجمالي الدخل من المشروع
        """
        from models.transaction import Transaction
        income = Transaction.query.filter_by(
            project_id=self.id, 
            transaction_type='income'
        ).with_entities(db.func.sum(Transaction.amount)).scalar() or 0
        return income

    def get_total_expenses(self):
        """
حساب إجمالي المصروفات للمشروع
        """
        from models.transaction import Transaction
        expenses = Transaction.query.filter_by(
            project_id=self.id, 
            transaction_type='expense'
        ).with_entities(db.func.sum(Transaction.amount)).scalar() or 0
        return expenses

    def get_profit(self):
        """
حساب الربح (الدخل - المصروفات)
        """
        return self.get_total_income() - self.get_total_expenses()

    def get_profit_margin(self):
        """
حساب هامش الربح
        """
        income = self.get_total_income()
        if income > 0:
            return (self.get_profit() / income) * 100
        return 0

    def get_unpaid_invoices(self):
        """
الحصول على الفواتير غير المدفوعة
        """
        from models.invoice import Invoice
        return Invoice.query.filter_by(
            project_id=self.id, 
            status='unpaid'
        ).all()

    def to_dict(self):
        """
تحويل بيانات المشروع إلى قاموس
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'project_type': self.project_type,
            'status': self.status,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'budget': self.budget,
            'currency': self.currency,
            'total_income': self.get_total_income(),
            'total_expenses': self.get_total_expenses(),
            'profit': self.get_profit(),
            'profit_margin': self.get_profit_margin(),
            'owner_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<Project {self.name}>'


# جدول العلاقة بين المشاريع والعملاء (علاقة متعددة لمتعددة)
project_clients = db.Table('project_clients',
    db.Column('project_id', db.Integer, db.ForeignKey('projects.id'), primary_key=True),
    db.Column('client_id', db.Integer, db.ForeignKey('clients.id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=datetime.utcnow)
)