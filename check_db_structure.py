#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
فحص بنية قاعدة البيانات
Check database structure
"""

from app import create_app
from extensions import db

def check_clients_table():
    """فحص بنية جدول العملاء"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 فحص بنية جدول العملاء...")
            
            inspector = db.inspect(db.engine)
            columns = inspector.get_columns('clients')
            
            print("=== أعمدة جدول العملاء الحالية ===")
            for col in columns:
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                default = f" DEFAULT {col['default']}" if col['default'] else ""
                print(f"  {col['name']}: {col['type']} {nullable}{default}")
            
            # التحقق من وجود الأعمدة المطلوبة
            column_names = [col['name'] for col in columns]
            required_columns = ['company_name', 'last_contact']
            
            print("\n=== التحقق من الأعمدة المطلوبة ===")
            for col in required_columns:
                if col in column_names:
                    print(f"  ✅ {col}: موجود")
                else:
                    print(f"  ❌ {col}: مفقود")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص الجدول: {e}")
            return False

def check_migration_status():
    """فحص حالة الترحيل"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔄 فحص حالة الترحيل...")
            
            # التحقق من جدول alembic_version
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'alembic_version' in tables:
                result = db.session.execute(db.text("SELECT version_num FROM alembic_version")).fetchone()
                if result:
                    print(f"✅ إصدار الترحيل الحالي: {result[0]}")
                else:
                    print("⚠️ لا يوجد إصدار ترحيل مسجل")
            else:
                print("❌ جدول alembic_version غير موجود")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص الترحيل: {e}")
            return False

if __name__ == '__main__':
    print("🚀 بدء فحص قاعدة البيانات...")
    
    success1 = check_clients_table()
    success2 = check_migration_status()
    
    if success1 and success2:
        print("\n✅ تم فحص قاعدة البيانات بنجاح")
    else:
        print("\n❌ حدثت أخطاء أثناء الفحص")
    
    input("\nاضغط Enter للخروج...")
