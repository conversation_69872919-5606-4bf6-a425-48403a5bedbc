{% extends "base.html" %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">الإعدادات</h1>
            <p class="text-muted">إدارة إعدادات النظام والحساب</p>
        </div>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#profile" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                        <a href="#company" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-building me-2"></i>بيانات الشركة
                        </a>
                        <a href="#invoice-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير
                        </a>
                        <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-bell me-2"></i>الإشعارات
                        </a>
                        <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-shield-alt me-2"></i>الأمان
                        </a>
                        <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-download me-2"></i>النسخ الاحتياطي
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="tab-content">
                <!-- Profile Settings -->
                <div class="tab-pane fade show active" id="profile">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('main.settings') }}" enctype="multipart/form-data">
                                <input type="hidden" name="section" value="profile">
                                
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <div class="mb-3">
                                            {% if current_user.profile_image %}
                                            <img src="{{ current_user.profile_image }}" class="rounded-circle mb-3" 
                                                 style="width: 120px; height: 120px; object-fit: cover;" alt="الصورة الشخصية">
                                            {% else %}
                                            <div class="bg-primary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center text-white" 
                                                 style="width: 120px; height: 120px;">
                                                <span class="fs-1 fw-bold">{{ current_user.first_name[0].upper() if current_user.first_name else 'U' }}</span>
                                            </div>
                                            {% endif %}
                                            <input type="file" class="form-control" name="profile_image" accept="image/*">
                                            <small class="form-text text-muted">اختر صورة شخصية جديدة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="first_name" class="form-label">الاسم الأول</label>
                                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                                       value="{{ current_user.first_name or '' }}">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                                       value="{{ current_user.last_name or '' }}">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" id="email" name="email" 
                                                       value="{{ current_user.email }}" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="phone" class="form-label">رقم الهاتف</label>
                                                <input type="tel" class="form-control" id="phone" name="phone" 
                                                       value="{{ current_user.phone or '' }}">
                                            </div>
                                            <div class="col-12 mb-3">
                                                <label for="language_preference" class="form-label">اللغة المفضلة</label>
                                                <select class="form-select" id="language_preference" name="language_preference">
                                                    <option value="ar" {% if current_user.language_preference == 'ar' %}selected{% endif %}>العربية</option>
                                                    <option value="en" {% if current_user.language_preference == 'en' %}selected{% endif %}>English</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Company Settings -->
                <div class="tab-pane fade" id="company">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>بيانات الشركة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('main.settings') }}">
                                <input type="hidden" name="section" value="company">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="{{ current_user.company_name or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                               value="{{ company_settings.tax_number if company_settings else '' }}">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" name="address" rows="3">{{ current_user.address or '' }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">المدينة</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="{{ current_user.city or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="country" class="form-label">الدولة</label>
                                        <select class="form-select" id="country" name="country">
                                            <option value="SA" {% if current_user.country == 'SA' %}selected{% endif %}>المملكة العربية السعودية</option>
                                            <option value="AE" {% if current_user.country == 'AE' %}selected{% endif %}>الإمارات العربية المتحدة</option>
                                            <option value="KW" {% if current_user.country == 'KW' %}selected{% endif %}>الكويت</option>
                                            <option value="QA" {% if current_user.country == 'QA' %}selected{% endif %}>قطر</option>
                                            <option value="BH" {% if current_user.country == 'BH' %}selected{% endif %}>البحرين</option>
                                            <option value="OM" {% if current_user.country == 'OM' %}selected{% endif %}>عمان</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Invoice Settings -->
                <div class="tab-pane fade" id="invoice-settings">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('main.settings') }}">
                                <input type="hidden" name="section" value="invoice">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="invoice_prefix" class="form-label">بادئة رقم الفاتورة</label>
                                        <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix" 
                                               value="{{ invoice_settings.prefix if invoice_settings else 'INV' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="next_invoice_number" class="form-label">رقم الفاتورة التالي</label>
                                        <input type="number" class="form-control" id="next_invoice_number" name="next_invoice_number" 
                                               value="{{ invoice_settings.next_number if invoice_settings else 1 }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_due_days" class="form-label">أيام الاستحقاق الافتراضية</label>
                                        <input type="number" class="form-control" id="default_due_days" name="default_due_days" 
                                               value="{{ invoice_settings.default_due_days if invoice_settings else 30 }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_currency" class="form-label">العملة الافتراضية</label>
                                        <select class="form-select" id="default_currency" name="default_currency">
                                            <option value="SAR" {% if invoice_settings and invoice_settings.default_currency == 'SAR' %}selected{% endif %}>ريال سعودي</option>
                                            <option value="AED" {% if invoice_settings and invoice_settings.default_currency == 'AED' %}selected{% endif %}>درهم إماراتي</option>
                                            <option value="USD" {% if invoice_settings and invoice_settings.default_currency == 'USD' %}selected{% endif %}>دولار أمريكي</option>
                                            <option value="EUR" {% if invoice_settings and invoice_settings.default_currency == 'EUR' %}selected{% endif %}>يورو</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_tax_rate" class="form-label">معدل الضريبة الافتراضي (%)</label>
                                        <input type="number" class="form-control" id="default_tax_rate" name="default_tax_rate" 
                                               step="0.01" value="{{ invoice_settings.default_tax_rate if invoice_settings else 15 }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="auto_send_reminders" name="auto_send_reminders" 
                                                   {% if invoice_settings and invoice_settings.auto_send_reminders %}checked{% endif %}>
                                            <label class="form-check-label" for="auto_send_reminders">
                                                إرسال تذكيرات تلقائية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="tab-pane fade" id="notifications">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('main.settings') }}">
                                <input type="hidden" name="section" value="notifications">
                                
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <h6>إشعارات البريد الإلكتروني</h6>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="email_new_invoice" name="email_new_invoice" checked>
                                            <label class="form-check-label" for="email_new_invoice">
                                                فاتورة جديدة
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="email_payment_received" name="email_payment_received" checked>
                                            <label class="form-check-label" for="email_payment_received">
                                                استلام دفعة
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="email_overdue_invoice" name="email_overdue_invoice" checked>
                                            <label class="form-check-label" for="email_overdue_invoice">
                                                فاتورة متأخرة
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <h6>الإشعارات داخل النظام</h6>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="system_new_client" name="system_new_client" checked>
                                            <label class="form-check-label" for="system_new_client">
                                                عميل جديد
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="system_project_update" name="system_project_update" checked>
                                            <label class="form-check-label" for="system_project_update">
                                                تحديث المشروع
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security -->
                <div class="tab-pane fade" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('main.settings') }}">
                                <input type="hidden" name="section" value="security">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="two_factor_auth" name="two_factor_auth">
                                            <label class="form-check-label" for="two_factor_auth">
                                                تفعيل المصادقة الثنائية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    اتركي حقول كلمة المرور فارغة إذا كنت لا تريد تغييرها.
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Backup -->
                <div class="tab-pane fade" id="backup">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-download me-2"></i>النسخ الاحتياطي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>تصدير البيانات</h6>
                                    <p class="text-muted">قم بتصدير جميع بياناتك لحفظها كنسخة احتياطية</p>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" onclick="exportData('all')">
                                            <i class="fas fa-download me-2"></i>تصدير جميع البيانات
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="exportData('invoices')">
                                            <i class="fas fa-file-invoice me-2"></i>تصدير الفواتير فقط
                                        </button>
                                        <button class="btn btn-outline-info" onclick="exportData('clients')">
                                            <i class="fas fa-users me-2"></i>تصدير العملاء فقط
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>استيراد البيانات</h6>
                                    <p class="text-muted">استيراد البيانات من ملف نسخة احتياطية</p>
                                    <form method="POST" action="{{ url_for('main.import_data') }}" enctype="multipart/form-data">
                                        <div class="mb-3">
                                            <input type="file" class="form-control" name="backup_file" accept=".json,.csv,.xlsx">
                                        </div>
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-upload me-2"></i>استيراد البيانات
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function exportData(type) {
    const url = `/export/${type}`;
    window.open(url, '_blank');
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const passwordForm = document.querySelector('#security form');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
            }
        });
    }
});
</script>
{% endblock %}
