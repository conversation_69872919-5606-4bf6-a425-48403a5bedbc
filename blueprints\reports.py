#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة التقارير
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import logging
import json
import os
import tempfile
# import pandas as pd
# import matplotlib.pyplot as plt
# import seaborn as sns
import io
import csv

# استيراد النماذج
from models.user import User
from models.project import Project
from models.client import Client
from models.invoice import Invoice
from models.transaction import Transaction

# تم إزالة استيراد الوكيل الذكي

# إنشاء مخطط Blueprint
reports_bp = Blueprint('reports', __name__, url_prefix='/reports')

# إعداد السجل
logger = logging.getLogger(__name__)

@reports_bp.route('/')
@login_required
def index():
    """
الصفحة الرئيسية للتقارير
    """
    try:
        # الحصول على الفترة الزمنية من المعاملات
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        report_type = request.args.get('report_type', 'all')

        # تحديد التواريخ الافتراضية (آخر 30 يوم)
        if not date_from or not date_to:
            today = datetime.now().date()
            date_from = today - timedelta(days=30)
            date_to = today
        else:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            except ValueError:
                today = datetime.now().date()
                date_from = today - timedelta(days=30)
                date_to = today

        # الحصول على الفواتير للفترة المحددة
        invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= date_from,
            Invoice.issue_date <= date_to
        ).all()

        # حساب الإحصائيات
        total_sales = sum(invoice.total_amount for invoice in invoices)
        paid_invoices = [inv for inv in invoices if inv.status == 'paid']
        unpaid_invoices = [inv for inv in invoices if inv.status == 'unpaid']
        partially_paid_invoices = [inv for inv in invoices if inv.status == 'partially_paid']
        overdue_invoices = [inv for inv in invoices if inv.status == 'overdue']

        paid_invoices_count = len(paid_invoices)
        pending_invoices_count = len(unpaid_invoices) + len(partially_paid_invoices)
        unpaid_invoices_count = len(unpaid_invoices)
        partially_paid_invoices_count = len(partially_paid_invoices)
        overdue_invoices_count = len(overdue_invoices)

        paid_amount = sum(inv.total_amount for inv in paid_invoices)
        pending_amount = sum(inv.total_amount for inv in unpaid_invoices + partially_paid_invoices)

        # حساب نمو المبيعات (مقارنة بالفترة السابقة)
        previous_period_start = date_from - timedelta(days=(date_to - date_from).days)
        previous_period_end = date_from

        previous_invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= previous_period_start,
            Invoice.issue_date < previous_period_end
        ).all()

        previous_sales = sum(invoice.total_amount for invoice in previous_invoices)
        sales_growth = ((total_sales - previous_sales) / previous_sales * 100) if previous_sales > 0 else 0

        # الحصول على العملاء
        clients = Client.query.filter_by(user_id=current_user.id).all()
        active_clients_count = len([c for c in clients if getattr(c, 'is_active', True)])
        total_clients_count = len(clients)

        # أفضل العملاء (حسب إجمالي المبلغ)
        client_totals = {}
        for invoice in invoices:
            if invoice.client_id:
                client = next((c for c in clients if c.id == invoice.client_id), None)
                if client:
                    if client.id not in client_totals:
                        client_totals[client.id] = {
                            'name': client.name,
                            'total_amount': 0,
                            'invoices_count': 0
                        }
                    client_totals[client.id]['total_amount'] += invoice.total_amount
                    client_totals[client.id]['invoices_count'] += 1

        top_clients = sorted(client_totals.values(), key=lambda x: x['total_amount'], reverse=True)[:5]

        # الفواتير الأخيرة
        recent_invoices = Invoice.query.filter_by(user_id=current_user.id)\
            .order_by(Invoice.issue_date.desc()).limit(5).all()

        # إضافة اسم العميل لكل فاتورة
        for invoice in recent_invoices:
            if invoice.client_id:
                client = next((c for c in clients if c.id == invoice.client_id), None)
                invoice.client_name = client.name if client else 'غير محدد'
            else:
                invoice.client_name = 'غير محدد'

        # بيانات الرسوم البيانية
        # بيانات المبيعات (آخر 7 أيام)
        sales_labels = []
        sales_data = []
        for i in range(7):
            day = date_to - timedelta(days=6-i)
            day_invoices = [inv for inv in invoices if inv.issue_date == day]
            day_total = sum(inv.total_amount for inv in day_invoices)
            sales_labels.append(day.strftime('%m-%d'))
            sales_data.append(float(day_total))

        # بيانات شهرية (آخر 6 أشهر)
        monthly_labels = []
        monthly_revenue = []
        monthly_expenses = []

        for i in range(6):
            month_date = date_to.replace(day=1) - timedelta(days=i*30)
            month_start = month_date.replace(day=1)
            if month_date.month == 12:
                month_end = month_date.replace(year=month_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                month_end = month_date.replace(month=month_date.month + 1, day=1) - timedelta(days=1)

            month_invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= month_start,
                Invoice.issue_date <= month_end
            ).all()

            month_revenue = sum(inv.total_amount for inv in month_invoices if inv.status == 'paid')
            month_expense = 0  # يمكن إضافة حساب المصروفات هنا

            monthly_labels.insert(0, month_date.strftime('%Y-%m'))
            monthly_revenue.insert(0, float(month_revenue))
            monthly_expenses.insert(0, float(month_expense))

        return render_template(
            'reports/home.html',
            date_from=date_from.strftime('%Y-%m-%d'),
            date_to=date_to.strftime('%Y-%m-%d'),
            report_type=report_type,
            total_sales=total_sales,
            sales_growth=sales_growth,
            paid_invoices_count=paid_invoices_count,
            pending_invoices_count=pending_invoices_count,
            unpaid_invoices_count=unpaid_invoices_count,
            partially_paid_invoices_count=partially_paid_invoices_count,
            overdue_invoices_count=overdue_invoices_count,
            paid_amount=paid_amount,
            pending_amount=pending_amount,
            active_clients_count=active_clients_count,
            total_clients_count=total_clients_count,
            top_clients=top_clients,
            recent_invoices=recent_invoices,
            sales_labels=sales_labels,
            sales_data=sales_data,
            monthly_labels=monthly_labels,
            monthly_revenue=monthly_revenue,
            monthly_expenses=monthly_expenses
        )

    except Exception as e:
        logger.error(f"Error loading reports: {str(e)}")
        flash('حدث خطأ أثناء تحميل التقارير. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template(
            'reports/home.html',
            date_from='',
            date_to='',
            report_type='all',
            total_sales=0,
            sales_growth=0,
            paid_invoices_count=0,
            pending_invoices_count=0,
            unpaid_invoices_count=0,
            partially_paid_invoices_count=0,
            overdue_invoices_count=0,
            paid_amount=0,
            pending_amount=0,
            active_clients_count=0,
            total_clients_count=0,
            top_clients=[],
            recent_invoices=[],
            sales_labels=[],
            sales_data=[],
            monthly_labels=[],
            monthly_revenue=[],
            monthly_expenses=[]
        )

@reports_bp.route('/financial')
@login_required
def financial_reports():
    """
تقارير مالية
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        report_type = request.args.get('type', 'income_expense')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        elif period == 'custom':
            # التاريخ المخصص
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')
            
            if start_date_str and end_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return redirect(url_for('reports.financial_reports'))
            else:
                # افتراضي: الشهر الحالي
                start_date = today.replace(day=1)
                end_date = today
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
            end_date = today
        
        # إذا لم يتم تحديد تاريخ النهاية في حالة الفترة غير المخصصة
        if period != 'custom':
            end_date = today
        
        # الحصول على البيانات بناءً على نوع التقرير
        if report_type == 'income_expense':
            # تقرير الدخل والمصروفات
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date
            ).order_by(Transaction.date).all()
            
            # تجميع البيانات حسب التاريخ
            daily_data = {}
            for transaction in transactions:
                date_str = transaction.date.strftime('%Y-%m-%d')
                if date_str not in daily_data:
                    daily_data[date_str] = {'income': 0, 'expense': 0}
                
                if transaction.type == 'income':
                    daily_data[date_str]['income'] += transaction.amount
                elif transaction.type == 'expense':
                    daily_data[date_str]['expense'] += transaction.amount
            
            # تحويل البيانات إلى قائمة
            report_data = []
            for date_str, data in sorted(daily_data.items()):
                report_data.append({
                    'date': date_str,
                    'income': data['income'],
                    'expense': data['expense'],
                    'net': data['income'] - data['expense']
                })
            
            # حساب الإجماليات
            total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
            total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
            net_profit = total_income - total_expense
            profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
            
            # إنشاء رسم بياني
            try:
                analytics = SmartBizAnalytics()
                chart = analytics.generate_cash_flow_chart(current_user.id, start_date=start_date, end_date=end_date)
                chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
            except Exception as chart_error:
                logger.error(f"Error generating chart: {str(chart_error)}")
                chart_html = None
            
            return render_template(
                'reports/financial.html',
                period=period,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                report_data=report_data,
                total_income=total_income,
                total_expense=total_expense,
                net_profit=net_profit,
                profit_margin=profit_margin,
                chart_html=chart_html
            )
            
        elif report_type == 'invoice':
            # تقرير الفواتير
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).order_by(Invoice.issue_date).all()
            
            # حساب الإجماليات
            total_invoiced = sum(invoice.total_amount for invoice in invoices)
            paid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
            unpaid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status in ['unpaid', 'partially_paid'])
            
            # تجميع البيانات حسب الحالة
            status_data = {
                'paid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid'),
                'partially_paid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'partially_paid'),
                'unpaid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'unpaid'),
                'overdue': sum(invoice.total_amount for invoice in invoices if invoice.status == 'overdue')
            }
            
            # إنشاء رسم بياني
            try:
                analytics = SmartBizAnalytics()
                chart = analytics.generate_invoice_status_chart(current_user.id, start_date=start_date, end_date=end_date)
                chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
            except Exception as chart_error:
                logger.error(f"Error generating chart: {str(chart_error)}")
                chart_html = None
            
            return render_template(
                'reports/financial.html',
                period=period,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                invoices=invoices,
                total_invoiced=total_invoiced,
                paid_invoices=paid_invoices,
                unpaid_invoices=unpaid_invoices,
                status_data=status_data,
                chart_html=chart_html
            )
            
        elif report_type == 'client':
            # تقرير العملاء
            clients = Client.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.client_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).all()
            
            # تجميع البيانات حسب العميل
            client_data = {}
            for client in clients:
                client_data[client.id] = {
                    'id': client.id,
                    'name': client.name,
                    'income': 0,
                    'invoiced': 0,
                    'paid': 0,
                    'unpaid': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.client_id in client_data and transaction.type == 'income':
                    client_data[transaction.client_id]['income'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.client_id in client_data:
                    client_data[invoice.client_id]['invoiced'] += invoice.total_amount
                    if invoice.status == 'paid':
                        client_data[invoice.client_id]['paid'] += invoice.total_amount
                    elif invoice.status in ['unpaid', 'partially_paid', 'overdue']:
                        client_data[invoice.client_id]['unpaid'] += invoice.total_amount
            
            # تحويل البيانات إلى قائمة وإزالة العملاء بدون بيانات
            report_data = [data for data in client_data.values() if data['invoiced'] > 0 or data['income'] > 0]
            
            # ترتيب البيانات حسب الدخل
            report_data.sort(key=lambda x: x['income'], reverse=True)
            
            # إنشاء رسم بياني
            try:
                analytics = SmartBizAnalytics()
                chart = analytics.generate_client_segmentation_chart(current_user.id, start_date=start_date, end_date=end_date)
                chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
            except Exception as chart_error:
                logger.error(f"Error generating chart: {str(chart_error)}")
                chart_html = None
            
            return render_template(
                'reports/financial.html',
                period=period,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                report_data=report_data,
                chart_html=chart_html
            )
            
        elif report_type == 'project':
            # تقرير المشاريع
            projects = Project.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.project_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date,
                Invoice.project_id != None
            ).all()
            
            # تجميع البيانات حسب المشروع
            project_data = {}
            for project in projects:
                project_data[project.id] = {
                    'id': project.id,
                    'name': project.name,
                    'income': 0,
                    'expense': 0,
                    'invoiced': 0,
                    'profit': 0,
                    'profit_margin': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.project_id in project_data:
                    if transaction.type == 'income':
                        project_data[transaction.project_id]['income'] += transaction.amount
                    elif transaction.type == 'expense':
                        project_data[transaction.project_id]['expense'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.project_id in project_data:
                    project_data[invoice.project_id]['invoiced'] += invoice.total_amount
            
            # حساب الربح وهامش الربح
            for project_id, data in project_data.items():
                data['profit'] = data['income'] - data['expense']
                data['profit_margin'] = (data['profit'] / data['income'] * 100) if data['income'] > 0 else 0
            
            # تحويل البيانات إلى قائمة وإزالة المشاريع بدون بيانات
            report_data = [data for data in project_data.values() if data['invoiced'] > 0 or data['income'] > 0 or data['expense'] > 0]
            
            # ترتيب البيانات حسب الربح
            report_data.sort(key=lambda x: x['profit'], reverse=True)
            
            # إنشاء رسم بياني
            try:
                analytics = SmartBizAnalytics()
                chart = analytics.generate_project_profitability_chart(current_user.id, start_date=start_date, end_date=end_date)
                chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
            except Exception as chart_error:
                logger.error(f"Error generating chart: {str(chart_error)}")
                chart_html = None
            
            return render_template(
                'reports/financial.html',
                period=period,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                report_data=report_data,
                chart_html=chart_html
            )
        
        else:
            flash('نوع التقرير غير صالح.', 'danger')
            return redirect(url_for('reports.financial_reports'))
        
    except Exception as e:
        logger.error(f"Error generating financial report: {str(e)}")
        flash('حدث خطأ أثناء إنشاء التقرير المالي. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('reports/financial.html')

@reports_bp.route('/clients')
@login_required
def client_reports():
    """
تقارير العملاء
    """
    try:
        # الحصول على جميع العملاء
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        
        # الحصول على إحصائيات لكل عميل
        client_stats = []
        for client in clients:
            # حساب إجمالي المبيعات
            total_sales = client.calculate_total_sales()
            
            # حساب إجمالي المدفوعات
            total_paid = client.calculate_total_paid()
            
            # حساب إجمالي غير المدفوع
            total_unpaid = client.calculate_total_unpaid()
            
            # الحصول على الفواتير غير المدفوعة
            unpaid_invoices = client.get_unpaid_invoices()
            
            # الحصول على تاريخ آخر معاملة
            last_transaction = Transaction.query.filter_by(
                user_id=current_user.id,
                client_id=client.id
            ).order_by(Transaction.date.desc()).first()
            
            last_transaction_date = last_transaction.date if last_transaction else None
            
            # إضافة الإحصائيات إلى القائمة
            client_stats.append({
                'client': client,
                'total_sales': total_sales,
                'total_paid': total_paid,
                'total_unpaid': total_unpaid,
                'unpaid_invoices_count': len(unpaid_invoices),
                'last_transaction_date': last_transaction_date
            })
        
        # ترتيب العملاء حسب إجمالي المبيعات
        client_stats.sort(key=lambda x: x['total_sales'], reverse=True)
        
        # إنشاء رسم بياني
        try:
            analytics = SmartBizAnalytics()
            chart = analytics.generate_client_segmentation_chart(current_user.id)
            chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
        except Exception as chart_error:
            logger.error(f"Error generating chart: {str(chart_error)}")
            chart_html = None
        
        return render_template(
            'reports/clients.html',
            client_stats=client_stats,
            chart_html=chart_html
        )
        
    except Exception as e:
        logger.error(f"Error generating client report: {str(e)}")
        flash('حدث خطأ أثناء إنشاء تقرير العملاء. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('reports/clients.html')

@reports_bp.route('/projects')
@login_required
def project_reports():
    """
تقارير المشاريع
    """
    try:
        # الحصول على جميع المشاريع
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.start_date.desc()).all()
        
        # الحصول على إحصائيات لكل مشروع
        project_stats = []
        for project in projects:
            # حساب الدخل والمصروفات والربح
            income = project.calculate_income()
            expenses = project.calculate_expenses()
            profit = project.calculate_profit()
            profit_margin = project.calculate_profit_margin()
            
            # الحصول على الفواتير غير المدفوعة
            unpaid_invoices = project.get_unpaid_invoices()
            
            # إضافة الإحصائيات إلى القائمة
            project_stats.append({
                'project': project,
                'income': income,
                'expenses': expenses,
                'profit': profit,
                'profit_margin': profit_margin,
                'unpaid_invoices_count': len(unpaid_invoices)
            })
        
        # ترتيب المشاريع حسب الربح
        project_stats.sort(key=lambda x: x['profit'], reverse=True)
        
        # إنشاء رسم بياني
        try:
            analytics = SmartBizAnalytics()
            chart = analytics.generate_project_profitability_chart(current_user.id)
            chart_html = chart.get('chart_html') if chart.get('status') == 'success' else None
        except Exception as chart_error:
            logger.error(f"Error generating chart: {str(chart_error)}")
            chart_html = None
        
        return render_template(
            'reports/projects.html',
            project_stats=project_stats,
            chart_html=chart_html
        )
        
    except Exception as e:
        logger.error(f"Error generating project report: {str(e)}")
        flash('حدث خطأ أثناء إنشاء تقرير المشاريع. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('reports/projects.html')

@reports_bp.route('/export/<report_type>')
@login_required
def export_report(report_type):
    """
تصدير التقارير
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        format_type = request.args.get('format', 'csv')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        elif period == 'custom':
            # التاريخ المخصص
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')
            
            if start_date_str and end_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return redirect(url_for('reports.financial_reports'))
            else:
                # افتراضي: الشهر الحالي
                start_date = today.replace(day=1)
                end_date = today
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
            end_date = today
        
        # إذا لم يتم تحديد تاريخ النهاية في حالة الفترة غير المخصصة
        if period != 'custom':
            end_date = today
        
        # إنشاء البيانات بناءً على نوع التقرير
        if report_type == 'income_expense':
            # تقرير الدخل والمصروفات
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date
            ).order_by(Transaction.date).all()
            
            # تجميع البيانات حسب التاريخ
            daily_data = {}
            for transaction in transactions:
                date_str = transaction.date.strftime('%Y-%m-%d')
                if date_str not in daily_data:
                    daily_data[date_str] = {'income': 0, 'expense': 0}
                
                if transaction.type == 'income':
                    daily_data[date_str]['income'] += transaction.amount
                elif transaction.type == 'expense':
                    daily_data[date_str]['expense'] += transaction.amount
            
            # تحويل البيانات إلى قائمة
            report_data = []
            for date_str, data in sorted(daily_data.items()):
                report_data.append({
                    'date': date_str,
                    'income': data['income'],
                    'expense': data['expense'],
                    'net': data['income'] - data['expense']
                })
            
            # إنشاء DataFrame
            df = pd.DataFrame(report_data)
            
            # إضافة الإجماليات
            total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
            total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
            net_profit = total_income - total_expense
            
            totals_df = pd.DataFrame([{
                'date': 'الإجمالي',
                'income': total_income,
                'expense': total_expense,
                'net': net_profit
            }])
            
            df = pd.concat([df, totals_df])
            
            # تصدير البيانات
            if format_type == 'csv':
                output = io.StringIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    io.BytesIO(output.getvalue().encode('utf-8-sig')),
                    mimetype='text/csv',
                    as_attachment=True,
                    attachment_filename=f'income_expense_report_{start_date}_{end_date}.csv'
                )
            
            elif format_type == 'excel':
                output = io.BytesIO()
                df.to_excel(output, index=False)
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    attachment_filename=f'income_expense_report_{start_date}_{end_date}.xlsx'
                )
            
            else:
                flash('تنسيق التصدير غير صالح.', 'danger')
                return redirect(url_for('reports.financial_reports'))
            
        elif report_type == 'invoice':
            # تقرير الفواتير
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).order_by(Invoice.issue_date).all()
            
            # تحويل البيانات إلى قائمة
            report_data = []
            for invoice in invoices:
                client = Client.query.get(invoice.client_id) if invoice.client_id else None
                client_name = client.name if client else 'غير محدد'
                
                report_data.append({
                    'invoice_number': invoice.invoice_number,
                    'client': client_name,
                    'issue_date': invoice.issue_date.strftime('%Y-%m-%d'),
                    'due_date': invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                    'total_amount': invoice.total_amount,
                    'status': invoice.status
                })
            
            # إنشاء DataFrame
            df = pd.DataFrame(report_data)
            
            # إضافة الإجماليات
            total_invoiced = sum(invoice.total_amount for invoice in invoices)
            paid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
            unpaid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status in ['unpaid', 'partially_paid'])
            
            totals_df = pd.DataFrame([{
                'invoice_number': 'الإجمالي',
                'client': '',
                'issue_date': '',
                'due_date': '',
                'total_amount': total_invoiced,
                'status': f'مدفوع: {paid_invoices}, غير مدفوع: {unpaid_invoices}'
            }])
            
            df = pd.concat([df, totals_df])
            
            # تصدير البيانات
            if format_type == 'csv':
                output = io.StringIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    io.BytesIO(output.getvalue().encode('utf-8-sig')),
                    mimetype='text/csv',
                    as_attachment=True,
                    attachment_filename=f'invoice_report_{start_date}_{end_date}.csv'
                )
            
            elif format_type == 'excel':
                output = io.BytesIO()
                df.to_excel(output, index=False)
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    attachment_filename=f'invoice_report_{start_date}_{end_date}.xlsx'
                )
            
            else:
                flash('تنسيق التصدير غير صالح.', 'danger')
                return redirect(url_for('reports.financial_reports'))
            
        elif report_type == 'client':
            # تقرير العملاء
            clients = Client.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.client_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).all()
            
            # تجميع البيانات حسب العميل
            client_data = {}
            for client in clients:
                client_data[client.id] = {
                    'name': client.name,
                    'email': client.email,
                    'phone': client.phone,
                    'income': 0,
                    'invoiced': 0,
                    'paid': 0,
                    'unpaid': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.client_id in client_data and transaction.type == 'income':
                    client_data[transaction.client_id]['income'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.client_id in client_data:
                    client_data[invoice.client_id]['invoiced'] += invoice.total_amount
                    if invoice.status == 'paid':
                        client_data[invoice.client_id]['paid'] += invoice.total_amount
                    elif invoice.status in ['unpaid', 'partially_paid', 'overdue']:
                        client_data[invoice.client_id]['unpaid'] += invoice.total_amount
            
            # تحويل البيانات إلى قائمة وإزالة العملاء بدون بيانات
            report_data = [data for data in client_data.values() if data['invoiced'] > 0 or data['income'] > 0]
            
            # ترتيب البيانات حسب الدخل
            report_data.sort(key=lambda x: x['income'], reverse=True)
            
            # إنشاء DataFrame
            df = pd.DataFrame(report_data)
            
            # تصدير البيانات
            if format_type == 'csv':
                output = io.StringIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    io.BytesIO(output.getvalue().encode('utf-8-sig')),
                    mimetype='text/csv',
                    as_attachment=True,
                    attachment_filename=f'client_report_{start_date}_{end_date}.csv'
                )
            
            elif format_type == 'excel':
                output = io.BytesIO()
                df.to_excel(output, index=False)
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    attachment_filename=f'client_report_{start_date}_{end_date}.xlsx'
                )
            
            else:
                flash('تنسيق التصدير غير صالح.', 'danger')
                return redirect(url_for('reports.financial_reports'))
            
        elif report_type == 'project':
            # تقرير المشاريع
            projects = Project.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.project_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date,
                Invoice.project_id != None
            ).all()
            
            # تجميع البيانات حسب المشروع
            project_data = {}
            for project in projects:
                project_data[project.id] = {
                    'name': project.name,
                    'status': project.status,
                    'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else '',
                    'end_date': project.end_date.strftime('%Y-%m-%d') if project.end_date else '',
                    'income': 0,
                    'expense': 0,
                    'invoiced': 0,
                    'profit': 0,
                    'profit_margin': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.project_id in project_data:
                    if transaction.type == 'income':
                        project_data[transaction.project_id]['income'] += transaction.amount
                    elif transaction.type == 'expense':
                        project_data[transaction.project_id]['expense'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.project_id in project_data:
                    project_data[invoice.project_id]['invoiced'] += invoice.total_amount
            
            # حساب الربح وهامش الربح
            for project_id, data in project_data.items():
                data['profit'] = data['income'] - data['expense']
                data['profit_margin'] = (data['profit'] / data['income'] * 100) if data['income'] > 0 else 0
            
            # تحويل البيانات إلى قائمة وإزالة المشاريع بدون بيانات
            report_data = [data for data in project_data.values() if data['invoiced'] > 0 or data['income'] > 0 or data['expense'] > 0]
            
            # ترتيب البيانات حسب الربح
            report_data.sort(key=lambda x: x['profit'], reverse=True)
            
            # إنشاء DataFrame
            df = pd.DataFrame(report_data)
            
            # تصدير البيانات
            if format_type == 'csv':
                output = io.StringIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    io.BytesIO(output.getvalue().encode('utf-8-sig')),
                    mimetype='text/csv',
                    as_attachment=True,
                    attachment_filename=f'project_report_{start_date}_{end_date}.csv'
                )
            
            elif format_type == 'excel':
                output = io.BytesIO()
                df.to_excel(output, index=False)
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    attachment_filename=f'project_report_{start_date}_{end_date}.xlsx'
                )
            
            else:
                flash('تنسيق التصدير غير صالح.', 'danger')
                return redirect(url_for('reports.financial_reports'))
        
        else:
            flash('نوع التقرير غير صالح.', 'danger')
            return redirect(url_for('reports.financial_reports'))
        
    except Exception as e:
        logger.error(f"Error exporting report: {str(e)}")
        flash('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('reports.financial_reports'))

@reports_bp.route('/api/financial')
@login_required
def api_financial_reports():
    """
واجهة برمجة التطبيقات API للتقارير المالية
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        report_type = request.args.get('type', 'income_expense')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        elif period == 'custom':
            # التاريخ المخصص
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')
            
            if start_date_str and end_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                except ValueError:
                    return jsonify({
                        'status': 'error',
                        'message': 'تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.'
                    }), 400
            else:
                # افتراضي: الشهر الحالي
                start_date = today.replace(day=1)
                end_date = today
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
            end_date = today
        
        # إذا لم يتم تحديد تاريخ النهاية في حالة الفترة غير المخصصة
        if period != 'custom':
            end_date = today
        
        # الحصول على البيانات بناءً على نوع التقرير
        if report_type == 'income_expense':
            # تقرير الدخل والمصروفات
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date
            ).order_by(Transaction.date).all()
            
            # تجميع البيانات حسب التاريخ
            daily_data = {}
            for transaction in transactions:
                date_str = transaction.date.strftime('%Y-%m-%d')
                if date_str not in daily_data:
                    daily_data[date_str] = {'income': 0, 'expense': 0}
                
                if transaction.type == 'income':
                    daily_data[date_str]['income'] += transaction.amount
                elif transaction.type == 'expense':
                    daily_data[date_str]['expense'] += transaction.amount
            
            # تحويل البيانات إلى قائمة
            report_data = []
            for date_str, data in sorted(daily_data.items()):
                report_data.append({
                    'date': date_str,
                    'income': data['income'],
                    'expense': data['expense'],
                    'net': data['income'] - data['expense']
                })
            
            # حساب الإجماليات
            total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
            total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
            net_profit = total_income - total_expense
            profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
            
            return jsonify({
                'status': 'success',
                'data': {
                    'period': period,
                    'report_type': report_type,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'report_data': report_data,
                    'total_income': total_income,
                    'total_expense': total_expense,
                    'net_profit': net_profit,
                    'profit_margin': profit_margin
                }
            })
            
        elif report_type == 'invoice':
            # تقرير الفواتير
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).order_by(Invoice.issue_date).all()
            
            # تحويل البيانات إلى قائمة
            invoice_data = []
            for invoice in invoices:
                client = Client.query.get(invoice.client_id) if invoice.client_id else None
                client_name = client.name if client else 'غير محدد'
                
                invoice_data.append({
                    'id': invoice.id,
                    'invoice_number': invoice.invoice_number,
                    'client_id': invoice.client_id,
                    'client_name': client_name,
                    'issue_date': invoice.issue_date.strftime('%Y-%m-%d'),
                    'due_date': invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else None,
                    'total_amount': invoice.total_amount,
                    'status': invoice.status
                })
            
            # حساب الإجماليات
            total_invoiced = sum(invoice.total_amount for invoice in invoices)
            paid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid')
            unpaid_invoices = sum(invoice.total_amount for invoice in invoices if invoice.status in ['unpaid', 'partially_paid'])
            
            # تجميع البيانات حسب الحالة
            status_data = {
                'paid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'paid'),
                'partially_paid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'partially_paid'),
                'unpaid': sum(invoice.total_amount for invoice in invoices if invoice.status == 'unpaid'),
                'overdue': sum(invoice.total_amount for invoice in invoices if invoice.status == 'overdue')
            }
            
            return jsonify({
                'status': 'success',
                'data': {
                    'period': period,
                    'report_type': report_type,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'invoices': invoice_data,
                    'total_invoiced': total_invoiced,
                    'paid_invoices': paid_invoices,
                    'unpaid_invoices': unpaid_invoices,
                    'status_data': status_data
                }
            })
            
        elif report_type == 'client':
            # تقرير العملاء
            clients = Client.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.client_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).all()
            
            # تجميع البيانات حسب العميل
            client_data = {}
            for client in clients:
                client_data[client.id] = {
                    'id': client.id,
                    'name': client.name,
                    'email': client.email,
                    'phone': client.phone,
                    'income': 0,
                    'invoiced': 0,
                    'paid': 0,
                    'unpaid': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.client_id in client_data and transaction.type == 'income':
                    client_data[transaction.client_id]['income'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.client_id in client_data:
                    client_data[invoice.client_id]['invoiced'] += invoice.total_amount
                    if invoice.status == 'paid':
                        client_data[invoice.client_id]['paid'] += invoice.total_amount
                    elif invoice.status in ['unpaid', 'partially_paid', 'overdue']:
                        client_data[invoice.client_id]['unpaid'] += invoice.total_amount
            
            # تحويل البيانات إلى قائمة وإزالة العملاء بدون بيانات
            report_data = [data for data in client_data.values() if data['invoiced'] > 0 or data['income'] > 0]
            
            # ترتيب البيانات حسب الدخل
            report_data.sort(key=lambda x: x['income'], reverse=True)
            
            return jsonify({
                'status': 'success',
                'data': {
                    'period': period,
                    'report_type': report_type,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'report_data': report_data
                }
            })
            
        elif report_type == 'project':
            # تقرير المشاريع
            projects = Project.query.filter_by(user_id=current_user.id).all()
            
            # الحصول على المعاملات والفواتير للفترة المحددة
            transactions = Transaction.query.filter(
                Transaction.user_id == current_user.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date,
                Transaction.project_id != None
            ).all()
            
            invoices = Invoice.query.filter(
                Invoice.user_id == current_user.id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date,
                Invoice.project_id != None
            ).all()
            
            # تجميع البيانات حسب المشروع
            project_data = {}
            for project in projects:
                project_data[project.id] = {
                    'id': project.id,
                    'name': project.name,
                    'status': project.status,
                    'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else None,
                    'end_date': project.end_date.strftime('%Y-%m-%d') if project.end_date else None,
                    'income': 0,
                    'expense': 0,
                    'invoiced': 0,
                    'profit': 0,
                    'profit_margin': 0
                }
            
            # إضافة بيانات المعاملات
            for transaction in transactions:
                if transaction.project_id in project_data:
                    if transaction.type == 'income':
                        project_data[transaction.project_id]['income'] += transaction.amount
                    elif transaction.type == 'expense':
                        project_data[transaction.project_id]['expense'] += transaction.amount
            
            # إضافة بيانات الفواتير
            for invoice in invoices:
                if invoice.project_id in project_data:
                    project_data[invoice.project_id]['invoiced'] += invoice.total_amount
            
            # حساب الربح وهامش الربح
            for project_id, data in project_data.items():
                data['profit'] = data['income'] - data['expense']
                data['profit_margin'] = (data['profit'] / data['income'] * 100) if data['income'] > 0 else 0
            
            # تحويل البيانات إلى قائمة وإزالة المشاريع بدون بيانات
            report_data = [data for data in project_data.values() if data['invoiced'] > 0 or data['income'] > 0 or data['expense'] > 0]
            
            # ترتيب البيانات حسب الربح
            report_data.sort(key=lambda x: x['profit'], reverse=True)
            
            return jsonify({
                'status': 'success',
                'data': {
                    'period': period,
                    'report_type': report_type,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'report_data': report_data
                }
            })
        
        else:
            return jsonify({
                'status': 'error',
                'message': 'نوع التقرير غير صالح.'
            }), 400
        
    except Exception as e:
        logger.error(f"API Error - financial reports: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إنشاء التقرير المالي.'
        }), 500