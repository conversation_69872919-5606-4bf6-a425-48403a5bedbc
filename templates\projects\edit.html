{% extends "base.html" %}

{% block title %}تعديل {{ project.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تعديل {{ project.name }}</h1>
            <p class="text-muted">تحديث بيانات المشروع</p>
        </div>
        <div>
            <a href="{{ url_for('projects.view_project', project_id=project.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى المشروع
            </a>
        </div>
    </div>

    <form method="POST">
        <div class="row">
            <!-- Main Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-project-diagram me-2"></i>المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المشروع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ project.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="">اختر العميل (اختياري)</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}" {% if project.client_id == client.id %}selected{% endif %}>
                                        {{ client.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">وصف المشروع</label>
                                <textarea class="form-control" id="description" name="description" rows="4" 
                                          placeholder="وصف تفصيلي للمشروع">{{ project.description or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline and Budget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>الجدول الزمني والميزانية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ project.start_date.strftime('%Y-%m-%d') if project.start_date else '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">تاريخ النهاية المتوقعة</label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="budget" class="form-label">الميزانية</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           min="0" step="0.01" value="{{ project.budget or '' }}" placeholder="0.00">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="estimated_hours" class="form-label">الساعات المقدرة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" 
                                           min="0" step="0.5" value="{{ project.estimated_hours or '' }}" placeholder="0">
                                    <span class="input-group-text">ساعة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>تفاصيل إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" {% if project.priority == 'low' %}selected{% endif %}>منخفضة</option>
                                    <option value="medium" {% if project.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                                    <option value="high" {% if project.priority == 'high' %}selected{% endif %}>عالية</option>
                                    <option value="urgent" {% if project.priority == 'urgent' %}selected{% endif %}>عاجلة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <input type="text" class="form-control" id="category" name="category" 
                                       value="{{ project.category or '' }}" placeholder="مثل: تطوير ويب، تصميم، استشارات">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="requirements" class="form-label">المتطلبات</label>
                                <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                          placeholder="متطلبات المشروع والمواصفات">{{ project.requirements or '' }}</textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="ملاحظات إضافية">{{ project.notes or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-toggle-on me-2"></i>الحالة والتقدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="status" class="form-label">حالة المشروع</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" {% if project.status == 'active' %}selected{% endif %}>نشط</option>
                                <option value="on_hold" {% if project.status == 'on_hold' %}selected{% endif %}>معلق</option>
                                <option value="completed" {% if project.status == 'completed' %}selected{% endif %}>مكتمل</option>
                                <option value="cancelled" {% if project.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="progress" class="form-label">نسبة التقدم (%)</label>
                            <input type="range" class="form-range" id="progress" name="progress" 
                                   min="0" max="100" value="{{ project.progress or 0 }}" 
                                   oninput="updateProgressValue(this.value)">
                            <div class="d-flex justify-content-between">
                                <span>0%</span>
                                <span id="progressValue" class="fw-bold">{{ project.progress or 0 }}%</span>
                                <span>100%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Members -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>فريق العمل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="project_manager" class="form-label">مدير المشروع</label>
                            <input type="text" class="form-control" id="project_manager" name="project_manager" 
                                   value="{{ project.project_manager or '' }}" placeholder="اسم مدير المشروع">
                        </div>
                        <div class="mb-3">
                            <label for="team_members" class="form-label">أعضاء الفريق</label>
                            <textarea class="form-control" id="team_members" name="team_members" rows="3" 
                                      placeholder="أسماء أعضاء الفريق (كل اسم في سطر منفصل)">{{ project.team_members or '' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <a href="{{ url_for('projects.view_project', project_id=project.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="button" class="btn btn-danger" 
                                    onclick="confirmDelete({{ project.id }}, '{{ project.name }}')">
                                <i class="fas fa-trash me-2"></i>حذف المشروع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف المشروع <strong id="projectName"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: سيتم حذف جميع الفواتير والمهام المرتبطة بهذا المشروع.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateProgressValue(value) {
    document.getElementById('progressValue').textContent = value + '%';
}

function confirmDelete(projectId, projectName) {
    document.getElementById('projectName').textContent = projectName;
    document.getElementById('deleteForm').action = `/projects/${projectId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate name
        if (!nameInput.value.trim()) {
            isValid = false;
            nameInput.classList.add('is-invalid');
        } else {
            nameInput.classList.remove('is-invalid');
        }
        
        // Validate dates
        if (startDateInput.value && endDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            
            if (endDate <= startDate) {
                isValid = false;
                endDateInput.classList.add('is-invalid');
                alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            } else {
                endDateInput.classList.remove('is-invalid');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Real-time validation
    nameInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('is-invalid');
        }
    });
    
    // Date validation
    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(this.value);
            
            if (endDate <= startDate) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        }
    });
});
</script>
{% endblock %}
