#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
ملف الإضافات والتوسعات
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_cors import CORS
from flask_mail import Mail
from flask_bcrypt import Bcrypt

# إنشاء كائنات الإضافات
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
cors = CORS()
mail = Mail()
bcrypt = Bcrypt()

def init_extensions(app):
    """
    تهيئة جميع الإضافات مع التطبيق
    """
    # تهيئة قاعدة البيانات
    db.init_app(app)

    # تهيئة نظام تسجيل الدخول
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'

    # تهيئة الهجرة
    migrate.init_app(app, db)

    # تهيئة CORS
    cors.init_app(app)

    # تهيئة البريد الإلكتروني
    mail.init_app(app)

    # تهيئة التشفير
    bcrypt.init_app(app)

@login_manager.user_loader
def load_user(user_id):
    """
    تحميل المستخدم من قاعدة البيانات
    """
    from models.user import User
    return User.query.get(int(user_id))
