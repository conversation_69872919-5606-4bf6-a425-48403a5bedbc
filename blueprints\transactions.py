#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة إدارة المعاملات المالية
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import logging
import json

# استيراد النماذج وقاعدة البيانات
from models.transaction import Transaction
from models.client import Client
from models.project import Project
from models.invoice import Invoice
from extensions import db

# إنشاء مخطط Blueprint
transactions_bp = Blueprint('transactions', __name__, url_prefix='/transactions')

# إعداد السجل
logger = logging.getLogger(__name__)

@transactions_bp.route('/')
@login_required
def transactions_list():
    """
قائمة المعاملات المالية
    """
    try:
        # الحصول على جميع المعاملات للمستخدم الحالي
        transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(Transaction.date.desc()).all()
        
        # الحصول على العملاء والمشاريع والفواتير للفلترة
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
        invoices = Invoice.query.filter_by(user_id=current_user.id).order_by(Invoice.issue_date.desc()).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        type_filter = request.args.get('type')
        client_filter = request.args.get('client_id')
        project_filter = request.args.get('project_id')
        invoice_filter = request.args.get('invoice_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if type_filter and type_filter != 'all':
            transactions = [transaction for transaction in transactions if transaction.type == type_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            transactions = [transaction for transaction in transactions if transaction.client_id == client_id]
        
        if project_filter and project_filter.isdigit():
            project_id = int(project_filter)
            transactions = [transaction for transaction in transactions if transaction.project_id == project_id]
        
        if invoice_filter and invoice_filter.isdigit():
            invoice_id = int(invoice_filter)
            transactions = [transaction for transaction in transactions if transaction.invoice_id == invoice_id]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                transactions = [transaction for transaction in transactions if transaction.date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                transactions = [transaction for transaction in transactions if transaction.date.date() <= date_to]
            except ValueError:
                pass
        
        # حساب الإجماليات
        total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
        total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
        net_amount = total_income - total_expense
        
        return render_template(
            'transactions/list.html',
            transactions=transactions,
            clients=clients,
            projects=projects,
            invoices=invoices,
            total_income=total_income,
            total_expense=total_expense,
            net_amount=net_amount,
            type_filter=type_filter,
            client_filter=client_filter,
            project_filter=project_filter,
            invoice_filter=invoice_filter,
            date_from=date_from,
            date_to=date_to
        )
        
    except Exception as e:
        logger.error(f"Error loading transactions list: {str(e)}")
        flash('حدث خطأ أثناء تحميل قائمة المعاملات. يرجى المحاولة مرة أخرى.', 'danger')
        return render_template('transactions/list.html', transactions=[])

@transactions_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_transaction():
    """
إضافة معاملة جديدة
    """
    # الحصول على العملاء والمشاريع والفواتير
    clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
    projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
    invoices = Invoice.query.filter_by(user_id=current_user.id, status='unpaid').order_by(Invoice.issue_date.desc()).all()
    
    if request.method == 'POST':
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')
        invoice_id = request.form.get('invoice_id')
        amount = request.form.get('amount')
        date = request.form.get('date')
        transaction_type = request.form.get('type')
        payment_method = request.form.get('payment_method')
        description = request.form.get('description')
        
        # التحقق من البيانات
        if not amount or not date or not transaction_type:
            flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
            return render_template(
                'transactions/add.html',
                clients=clients,
                projects=projects,
                invoices=invoices
            )
        
        try:
            # التحقق من المبلغ
            amount = float(amount)
            if amount <= 0:
                flash('يجب أن يكون المبلغ أكبر من صفر.', 'danger')
                return render_template(
                    'transactions/add.html',
                    clients=clients,
                    projects=projects,
                    invoices=invoices
                )
            
            # التحقق من تنسيق التاريخ
            try:
                transaction_date = datetime.strptime(date, '%Y-%m-%d')
            except ValueError:
                flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                return render_template(
                    'transactions/add.html',
                    clients=clients,
                    projects=projects,
                    invoices=invoices
                )
            
            # التحقق من وجود العميل إذا تم تحديده
            if client_id:
                client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
                if not client:
                    flash('العميل غير موجود.', 'danger')
                    return render_template(
                        'transactions/add.html',
                        clients=clients,
                        projects=projects,
                        invoices=invoices
                    )
            
            # التحقق من وجود المشروع إذا تم تحديده
            if project_id:
                project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
                if not project:
                    flash('المشروع غير موجود.', 'danger')
                    return render_template(
                        'transactions/add.html',
                        clients=clients,
                        projects=projects,
                        invoices=invoices
                    )
            
            # التحقق من وجود الفاتورة إذا تم تحديدها
            if invoice_id:
                invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
                if not invoice:
                    flash('الفاتورة غير موجودة.', 'danger')
                    return render_template(
                        'transactions/add.html',
                        clients=clients,
                        projects=projects,
                        invoices=invoices
                    )
                
                # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي للفاتورة
                if transaction_type == 'income':
                    # حساب المبلغ المدفوع بالفعل
                    paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all())
                    remaining_amount = invoice.total_amount - paid_amount
                    
                    if amount > remaining_amount:
                        flash(f'المبلغ يتجاوز المبلغ المتبقي للفاتورة ({remaining_amount}).', 'danger')
                        return render_template(
                            'transactions/add.html',
                            clients=clients,
                            projects=projects,
                            invoices=invoices
                        )
            
            # إنشاء معاملة جديدة
            new_transaction = Transaction(
                user_id=current_user.id,
                client_id=client_id if client_id else None,
                project_id=project_id if project_id else None,
                invoice_id=invoice_id if invoice_id else None,
                amount=amount,
                date=transaction_date,
                type=transaction_type,
                payment_method=payment_method,
                description=description,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(new_transaction)
            
            # تحديث حالة الفاتورة إذا كانت معاملة دخل مرتبطة بفاتورة
            if transaction_type == 'income' and invoice_id:
                invoice = Invoice.query.get(invoice_id)
                if invoice:
                    # حساب المبلغ المدفوع بعد إضافة المعاملة الجديدة
                    paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all()) + amount
                    
                    if paid_amount >= invoice.total_amount:
                        invoice.status = 'paid'
                    elif paid_amount > 0:
                        invoice.status = 'partially_paid'
                    
                    invoice.updated_at = datetime.now()
            
            # تحديث تاريخ آخر اتصال للعميل إذا تم تحديده
            if client_id:
                client = Client.query.get(client_id)
                if client:
                    client.update_last_contact()
            
            db.session.commit()
            
            flash(f'تمت إضافة معاملة بقيمة {amount} بنجاح.', 'success')
            return redirect(url_for('transactions.transactions_list'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error adding transaction: {str(e)}")
            flash('حدث خطأ أثناء إضافة المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
    
    return render_template(
        'transactions/add.html',
        clients=clients,
        projects=projects,
        invoices=invoices,
        today=datetime.now().strftime('%Y-%m-%d')
    )

@transactions_bp.route('/<int:transaction_id>')
@login_required
def view_transaction(transaction_id):
    """
عرض تفاصيل المعاملة
    """
    try:
        # الحصول على المعاملة
        transaction = Transaction.query.filter_by(id=transaction_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العميل والمشروع والفاتورة المرتبطين
        client = Client.query.get(transaction.client_id) if transaction.client_id else None
        project = Project.query.get(transaction.project_id) if transaction.project_id else None
        invoice = Invoice.query.get(transaction.invoice_id) if transaction.invoice_id else None
        
        return render_template(
            'transactions/view.html',
            transaction=transaction,
            client=client,
            project=project,
            invoice=invoice
        )
        
    except Exception as e:
        logger.error(f"Error viewing transaction: {str(e)}")
        flash('حدث خطأ أثناء عرض تفاصيل المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('transactions.transactions_list'))

@transactions_bp.route('/<int:transaction_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_transaction(transaction_id):
    """
تعديل المعاملة
    """
    try:
        # الحصول على المعاملة
        transaction = Transaction.query.filter_by(id=transaction_id, user_id=current_user.id).first_or_404()
        
        # الحصول على العملاء والمشاريع والفواتير
        clients = Client.query.filter_by(user_id=current_user.id).order_by(Client.name).all()
        projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.name).all()
        
        # الحصول على الفواتير غير المدفوعة أو الفاتورة الحالية
        invoices = Invoice.query.filter(
            (Invoice.user_id == current_user.id) & 
            ((Invoice.status == 'unpaid') | (Invoice.status == 'partially_paid') | (Invoice.id == transaction.invoice_id))
        ).order_by(Invoice.issue_date.desc()).all()
        
        if request.method == 'POST':
            client_id = request.form.get('client_id')
            project_id = request.form.get('project_id')
            invoice_id = request.form.get('invoice_id')
            amount = request.form.get('amount')
            date = request.form.get('date')
            transaction_type = request.form.get('type')
            payment_method = request.form.get('payment_method')
            description = request.form.get('description')
            
            # التحقق من البيانات
            if not amount or not date or not transaction_type:
                flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
                return render_template(
                    'transactions/edit.html',
                    transaction=transaction,
                    clients=clients,
                    projects=projects,
                    invoices=invoices
                )
            
            try:
                # التحقق من المبلغ
                amount = float(amount)
                if amount <= 0:
                    flash('يجب أن يكون المبلغ أكبر من صفر.', 'danger')
                    return render_template(
                        'transactions/edit.html',
                        transaction=transaction,
                        clients=clients,
                        projects=projects,
                        invoices=invoices
                    )
                
                # التحقق من تنسيق التاريخ
                try:
                    transaction_date = datetime.strptime(date, '%Y-%m-%d')
                except ValueError:
                    flash('تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق YYYY-MM-DD.', 'danger')
                    return render_template(
                        'transactions/edit.html',
                        transaction=transaction,
                        clients=clients,
                        projects=projects,
                        invoices=invoices
                    )
                
                # حفظ الفاتورة القديمة للتحديث لاحقًا
                old_invoice_id = transaction.invoice_id
                
                # التحقق من وجود الفاتورة الجديدة إذا تم تحديدها
                if invoice_id:
                    invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
                    if not invoice:
                        flash('الفاتورة غير موجودة.', 'danger')
                        return render_template(
                            'transactions/edit.html',
                            transaction=transaction,
                            clients=clients,
                            projects=projects,
                            invoices=invoices
                        )
                    
                    # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي للفاتورة
                    if transaction_type == 'income' and invoice_id != old_invoice_id:
                        # حساب المبلغ المدفوع بالفعل
                        paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all())
                        remaining_amount = invoice.total_amount - paid_amount
                        
                        if amount > remaining_amount:
                            flash(f'المبلغ يتجاوز المبلغ المتبقي للفاتورة ({remaining_amount}).', 'danger')
                            return render_template(
                                'transactions/edit.html',
                                transaction=transaction,
                                clients=clients,
                                projects=projects,
                                invoices=invoices
                            )
                
                # تحديث بيانات المعاملة
                transaction.client_id = client_id if client_id else None
                transaction.project_id = project_id if project_id else None
                transaction.invoice_id = invoice_id if invoice_id else None
                transaction.amount = amount
                transaction.date = transaction_date
                transaction.type = transaction_type
                transaction.payment_method = payment_method
                transaction.description = description
                transaction.updated_at = datetime.now()
                
                # تحديث حالة الفاتورة القديمة إذا كانت موجودة
                if old_invoice_id:
                    old_invoice = Invoice.query.get(old_invoice_id)
                    if old_invoice:
                        # حساب المبلغ المدفوع بعد إزالة المعاملة
                        paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=old_invoice.id).all() if t.id != transaction.id)
                        
                        if paid_amount >= old_invoice.total_amount:
                            old_invoice.status = 'paid'
                        elif paid_amount > 0:
                            old_invoice.status = 'partially_paid'
                        else:
                            old_invoice.status = 'unpaid'
                        
                        old_invoice.updated_at = datetime.now()
                
                # تحديث حالة الفاتورة الجديدة إذا كانت معاملة دخل مرتبطة بفاتورة
                if transaction_type == 'income' and invoice_id:
                    invoice = Invoice.query.get(invoice_id)
                    if invoice:
                        # حساب المبلغ المدفوع بعد إضافة المعاملة
                        paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all() if t.id != transaction.id) + amount
                        
                        if paid_amount >= invoice.total_amount:
                            invoice.status = 'paid'
                        elif paid_amount > 0:
                            invoice.status = 'partially_paid'
                        else:
                            invoice.status = 'unpaid'
                        
                        invoice.updated_at = datetime.now()
                
                # تحديث تاريخ آخر اتصال للعميل إذا تم تحديده
                if client_id:
                    client = Client.query.get(client_id)
                    if client:
                        client.update_last_contact()
                
                db.session.commit()
                
                flash(f'تم تحديث المعاملة بنجاح.', 'success')
                return redirect(url_for('transactions.view_transaction', transaction_id=transaction.id))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating transaction: {str(e)}")
                flash('حدث خطأ أثناء تحديث المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        
        return render_template(
            'transactions/edit.html',
            transaction=transaction,
            clients=clients,
            projects=projects,
            invoices=invoices
        )
        
    except Exception as e:
        logger.error(f"Error loading transaction for edit: {str(e)}")
        flash('حدث خطأ أثناء تحميل بيانات المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('transactions.transactions_list'))

@transactions_bp.route('/<int:transaction_id>/delete', methods=['POST'])
@login_required
def delete_transaction(transaction_id):
    """
حذف المعاملة
    """
    try:
        # الحصول على المعاملة
        transaction = Transaction.query.filter_by(id=transaction_id, user_id=current_user.id).first_or_404()
        
        # حفظ معرف الفاتورة للتحديث لاحقًا
        invoice_id = transaction.invoice_id
        
        # حذف المعاملة
        db.session.delete(transaction)
        
        # تحديث حالة الفاتورة إذا كانت موجودة
        if invoice_id:
            invoice = Invoice.query.get(invoice_id)
            if invoice:
                # حساب المبلغ المدفوع بعد حذف المعاملة
                paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all() if t.id != transaction_id)
                
                if paid_amount >= invoice.total_amount:
                    invoice.status = 'paid'
                elif paid_amount > 0:
                    invoice.status = 'partially_paid'
                else:
                    invoice.status = 'unpaid'
                
                invoice.updated_at = datetime.now()
        
        db.session.commit()
        
        flash('تم حذف المعاملة بنجاح.', 'success')
        return redirect(url_for('transactions.transactions_list'))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting transaction: {str(e)}")
        flash('حدث خطأ أثناء حذف المعاملة. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('transactions.transactions_list'))

@transactions_bp.route('/api/list')
@login_required
def api_transactions_list():
    """
واجهة برمجة التطبيقات API لقائمة المعاملات
    """
    try:
        # الحصول على جميع المعاملات للمستخدم الحالي
        transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(Transaction.date.desc()).all()
        
        # تطبيق الفلترة إذا تم تحديدها
        type_filter = request.args.get('type')
        client_filter = request.args.get('client_id')
        project_filter = request.args.get('project_id')
        invoice_filter = request.args.get('invoice_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if type_filter and type_filter != 'all':
            transactions = [transaction for transaction in transactions if transaction.type == type_filter]
        
        if client_filter and client_filter.isdigit():
            client_id = int(client_filter)
            transactions = [transaction for transaction in transactions if transaction.client_id == client_id]
        
        if project_filter and project_filter.isdigit():
            project_id = int(project_filter)
            transactions = [transaction for transaction in transactions if transaction.project_id == project_id]
        
        if invoice_filter and invoice_filter.isdigit():
            invoice_id = int(invoice_filter)
            transactions = [transaction for transaction in transactions if transaction.invoice_id == invoice_id]
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                transactions = [transaction for transaction in transactions if transaction.date.date() >= date_from]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                transactions = [transaction for transaction in transactions if transaction.date.date() <= date_to]
            except ValueError:
                pass
        
        # تحويل البيانات إلى تنسيق JSON
        transactions_data = []
        for transaction in transactions:
            transaction_data = transaction.to_dict()
            
            # إضافة بيانات العميل والمشروع والفاتورة إذا كانت موجودة
            if transaction.client_id:
                client = Client.query.get(transaction.client_id)
                transaction_data['client'] = client.to_dict() if client else None
            
            if transaction.project_id:
                project = Project.query.get(transaction.project_id)
                transaction_data['project'] = project.to_dict() if project else None
            
            if transaction.invoice_id:
                invoice = Invoice.query.get(transaction.invoice_id)
                transaction_data['invoice'] = invoice.to_dict() if invoice else None
            
            transactions_data.append(transaction_data)
        
        # حساب الإجماليات
        total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
        total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
        net_amount = total_income - total_expense
        
        return jsonify({
            'status': 'success',
            'data': {
                'transactions': transactions_data,
                'total_income': total_income,
                'total_expense': total_expense,
                'net_amount': net_amount
            }
        })
        
    except Exception as e:
        logger.error(f"API Error - transactions list: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب قائمة المعاملات'
        }), 500

@transactions_bp.route('/api/<int:transaction_id>')
@login_required
def api_transaction_details(transaction_id):
    """
واجهة برمجة التطبيقات API لتفاصيل المعاملة
    """
    try:
        # الحصول على المعاملة
        transaction = Transaction.query.filter_by(id=transaction_id, user_id=current_user.id).first()
        
        if not transaction:
            return jsonify({
                'status': 'error',
                'message': 'المعاملة غير موجودة'
            }), 404
        
        # تحويل البيانات إلى تنسيق JSON
        transaction_data = transaction.to_dict()
        
        # إضافة بيانات العميل والمشروع والفاتورة إذا كانت موجودة
        if transaction.client_id:
            client = Client.query.get(transaction.client_id)
            transaction_data['client'] = client.to_dict() if client else None
        
        if transaction.project_id:
            project = Project.query.get(transaction.project_id)
            transaction_data['project'] = project.to_dict() if project else None
        
        if transaction.invoice_id:
            invoice = Invoice.query.get(transaction.invoice_id)
            transaction_data['invoice'] = invoice.to_dict() if invoice else None
        
        return jsonify({
            'status': 'success',
            'data': transaction_data
        })
        
    except Exception as e:
        logger.error(f"API Error - transaction details: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب تفاصيل المعاملة'
        }), 500

@transactions_bp.route('/api/add', methods=['POST'])
@login_required
def api_add_transaction():
    """
واجهة برمجة التطبيقات API لإضافة معاملة
    """
    try:
        # الحصول على البيانات من الطلب
        data = request.get_json()
        
        if not data or 'amount' not in data or 'type' not in data:
            return jsonify({
                'status': 'error',
                'message': 'البيانات غير كاملة'
            }), 400
        
        # التحقق من المبلغ
        amount = float(data['amount'])
        if amount <= 0:
            return jsonify({
                'status': 'error',
                'message': 'يجب أن يكون المبلغ أكبر من صفر'
            }), 400
        
        # التحقق من نوع المعاملة
        transaction_type = data['type']
        if transaction_type not in ['income', 'expense']:
            return jsonify({
                'status': 'error',
                'message': 'نوع المعاملة غير صالح'
            }), 400
        
        # التحقق من وجود الفاتورة إذا تم تحديدها
        invoice_id = data.get('invoice_id')
        if invoice_id:
            invoice = Invoice.query.filter_by(id=invoice_id, user_id=current_user.id).first()
            if not invoice:
                return jsonify({
                    'status': 'error',
                    'message': 'الفاتورة غير موجودة'
                }), 400
            
            # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي للفاتورة
            if transaction_type == 'income':
                # حساب المبلغ المدفوع بالفعل
                paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all())
                remaining_amount = invoice.total_amount - paid_amount
                
                if amount > remaining_amount:
                    return jsonify({
                        'status': 'error',
                        'message': f'المبلغ يتجاوز المبلغ المتبقي للفاتورة ({remaining_amount})'
                    }), 400
        
        # إنشاء معاملة جديدة
        new_transaction = Transaction(
            user_id=current_user.id,
            client_id=data.get('client_id'),
            project_id=data.get('project_id'),
            invoice_id=invoice_id,
            amount=amount,
            date=datetime.strptime(data.get('date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d'),
            type=transaction_type,
            payment_method=data.get('payment_method', 'bank_transfer'),
            description=data.get('description', ''),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.session.add(new_transaction)
        
        # تحديث حالة الفاتورة إذا كانت معاملة دخل مرتبطة بفاتورة
        if transaction_type == 'income' and invoice_id:
            invoice = Invoice.query.get(invoice_id)
            if invoice:
                # حساب المبلغ المدفوع بعد إضافة المعاملة الجديدة
                paid_amount = sum(t.amount for t in Transaction.query.filter_by(invoice_id=invoice.id).all()) + amount
                
                if paid_amount >= invoice.total_amount:
                    invoice.status = 'paid'
                elif paid_amount > 0:
                    invoice.status = 'partially_paid'
                
                invoice.updated_at = datetime.now()
        
        # تحديث تاريخ آخر اتصال للعميل إذا تم تحديده
        client_id = data.get('client_id')
        if client_id:
            client = Client.query.get(client_id)
            if client:
                client.update_last_contact()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'تمت إضافة معاملة بقيمة {amount} بنجاح.',
            'data': new_transaction.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"API Error - add transaction: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء إضافة المعاملة'
        }), 500

@transactions_bp.route('/api/summary')
@login_required
def api_transactions_summary():
    """
واجهة برمجة التطبيقات API لملخص المعاملات
    """
    try:
        # الحصول على الفترة الزمنية
        period = request.args.get('period', 'month')
        
        # تحديد تاريخ البداية بناءً على الفترة
        today = datetime.now().date()
        if period == 'week':
            start_date = today - timedelta(days=today.weekday())
        elif period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
        
        # الحصول على المعاملات في الفترة المحددة
        transactions = Transaction.query.filter(
            Transaction.user_id == current_user.id,
            Transaction.date >= start_date
        ).order_by(Transaction.date).all()
        
        # حساب الإجماليات
        total_income = sum(transaction.amount for transaction in transactions if transaction.type == 'income')
        total_expense = sum(transaction.amount for transaction in transactions if transaction.type == 'expense')
        net_amount = total_income - total_expense
        
        # تجميع البيانات حسب التاريخ
        daily_data = {}
        for transaction in transactions:
            date_str = transaction.date.strftime('%Y-%m-%d')
            if date_str not in daily_data:
                daily_data[date_str] = {'income': 0, 'expense': 0}
            
            if transaction.type == 'income':
                daily_data[date_str]['income'] += transaction.amount
            elif transaction.type == 'expense':
                daily_data[date_str]['expense'] += transaction.amount
        
        # تحويل البيانات إلى قائمة
        daily_summary = []
        for date_str, data in sorted(daily_data.items()):
            daily_summary.append({
                'date': date_str,
                'income': data['income'],
                'expense': data['expense'],
                'net': data['income'] - data['expense']
            })
        
        return jsonify({
            'status': 'success',
            'data': {
                'period': period,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': today.strftime('%Y-%m-%d'),
                'total_income': total_income,
                'total_expense': total_expense,
                'net_amount': net_amount,
                'daily_summary': daily_summary
            }
        })
        
    except Exception as e:
        logger.error(f"API Error - transactions summary: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء جلب ملخص المعاملات'
        }), 500