#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
وحدة تحليل البيانات المبسطة
"""

from datetime import datetime, timedelta
from sqlalchemy import func

class SmartBizAnalytics:
    """
    فئة تحليل البيانات المبسطة لنظام SmartBiz
    """
    
    def __init__(self, db):
        self.db = db
    
    def generate_sales_chart(self, user_id, period='month', months=12, chart_type='line'):
        """
        إنشاء بيانات المبيعات للرسم البياني
        """
        try:
            from models.invoice import Invoice
            
            end_date = datetime.utcnow().date()
            start_date = end_date - timedelta(days=30 * months)
            
            # الحصول على بيانات المبيعات
            sales_data = self.db.session.query(
                func.extract('month', Invoice.issue_date).label('period'),
                func.sum(Invoice.total_amount).label('total_sales')
            ).filter(
                Invoice.user_id == user_id,
                Invoice.issue_date >= start_date,
                Invoice.issue_date <= end_date
            ).group_by('period').order_by('period').all()
            
            if not sales_data:
                return {
                    'status': 'error',
                    'message': 'لا توجد بيانات كافية لإنشاء الرسم البياني'
                }
            
            # تحويل البيانات إلى قائمة بسيطة
            data = [{'period': int(x.period), 'total_sales': float(x.total_sales)} for x in sales_data]
            
            return {
                'status': 'success',
                'chart_type': chart_type,
                'data': data
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في إنشاء بيانات المبيعات: {str(e)}'
            }
    
    def get_basic_stats(self, user_id):
        """
        الحصول على إحصائيات أساسية
        """
        try:
            from models.invoice import Invoice
            from models.client import Client
            from models.project import Project
            from models.transaction import Transaction
            
            # إحصائيات أساسية
            total_invoices = Invoice.query.filter_by(user_id=user_id).count()
            total_clients = Client.query.filter_by(user_id=user_id).count()
            total_projects = Project.query.filter_by(user_id=user_id).count()
            
            # إجمالي المبيعات
            total_sales = self.db.session.query(func.sum(Invoice.total_amount)).filter_by(user_id=user_id).scalar() or 0
            
            return {
                'total_invoices': total_invoices,
                'total_clients': total_clients,
                'total_projects': total_projects,
                'total_sales': float(total_sales),
                'total_expenses': 0,
                'net_profit': float(total_sales)
            }
        except Exception:
            return {
                'total_invoices': 0,
                'total_clients': 0,
                'total_projects': 0,
                'total_sales': 0,
                'total_expenses': 0,
                'net_profit': 0
            }
