{% extends "base.html" %}

{% block title %}استعادة كلمة المرور - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">استعادة كلمة المرور</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting" height="80">
                        <p class="text-muted mt-3">أدخل بريدك الإلكتروني وسنرسل لك رابطاً لإعادة تعيين كلمة المرور</p>
                    </div>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST" action="{{ url_for('auth.forgot_password') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-4">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i> إرسال رابط إعادة التعيين
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i> العودة إلى صفحة تسجيل الدخول
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}