#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة إنشاء ملفات PDF
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل خط عربي
    font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
    if os.path.exists(font_path):
        pdfmetrics.registerFont(TTFont('Arabic', font_path))
    else:
        # استخدام الخط الافتراضي إذا لم يتم العثور على الخط العربي
        print("Warning: Arabic font not found. Using default font.")
except Exception as e:
    print(f"Error registering font: {str(e)}")

class PDFGenerator:
    """
    فئة لإنشاء ملفات PDF للفواتير والتقارير
    """
    
    def __init__(self, rtl=True):
        """
        تهيئة منشئ ملفات PDF
        
        المعلمات:
            rtl (bool): ما إذا كان النص من اليمين إلى اليسار (العربية)
        """
        self.rtl = rtl
        self.styles = getSampleStyleSheet()
        
        # إنشاء نمط للنص العربي
        self.arabic_style = ParagraphStyle(
            'Arabic',
            parent=self.styles['Normal'],
            fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            alignment=2 if rtl else 0,  # محاذاة إلى اليمين للنص العربي
            rightIndent=0,
            leftIndent=0,
            spaceBefore=0,
            spaceAfter=0,
            leading=16
        )
        
        # إنشاء نمط للعناوين
        self.heading_style = ParagraphStyle(
            'Heading',
            parent=self.styles['Heading1'],
            fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            alignment=1,  # وسط
            fontSize=18,
            spaceAfter=0.5*cm
        )
        
        # إنشاء نمط للعناوين الفرعية
        self.subheading_style = ParagraphStyle(
            'SubHeading',
            parent=self.styles['Heading2'],
            fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            alignment=2 if rtl else 0,
            fontSize=14,
            spaceAfter=0.3*cm
        )
    
    def create_invoice_pdf(self, invoice, client, items, output_path=None):
        """
        إنشاء ملف PDF للفاتورة
        
        المعلمات:
            invoice: كائن الفاتورة
            client: كائن العميل
            items: قائمة عناصر الفاتورة
            output_path: مسار الملف الناتج (اختياري)
            
        العائد:
            مسار الملف المؤقت إذا لم يتم تحديد مسار الإخراج
        """
        # إنشاء ملف مؤقت إذا لم يتم تحديد مسار الإخراج
        if not output_path:
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            output_path = temp_file.name
            temp_file.close()
        
        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # قائمة العناصر التي سيتم إضافتها إلى المستند
        elements = []
        
        # إضافة شعار الشركة إذا كان متاحًا
        logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'img', 'logo.png')
        if os.path.exists(logo_path):
            elements.append(Image(logo_path, width=5*cm, height=2*cm))
            elements.append(Spacer(1, 0.5*cm))
        
        # إضافة عنوان الفاتورة
        elements.append(Paragraph(f"فاتورة #{invoice.invoice_number}", self.heading_style))
        elements.append(Spacer(1, 0.5*cm))
        
        # معلومات الفاتورة والعميل
        invoice_info = [
            [Paragraph("معلومات الفاتورة", self.subheading_style), Paragraph("معلومات العميل", self.subheading_style)],
            [
                Paragraph(f"رقم الفاتورة: {invoice.invoice_number}<br/>"
                          f"تاريخ الإصدار: {invoice.issue_date.strftime('%Y-%m-%d')}<br/>"
                          f"تاريخ الاستحقاق: {invoice.due_date.strftime('%Y-%m-%d')}<br/>"
                          f"الحالة: {self._get_status_text(invoice.status)}", self.arabic_style),
                          
                Paragraph(f"الاسم: {client.name}<br/>"
                          f"الشركة: {client.company or '-'}<br/>"
                          f"البريد الإلكتروني: {client.email or '-'}<br/>"
                          f"الهاتف: {client.phone or '-'}<br/>"
                          f"العنوان: {client.address or '-'}", self.arabic_style)
            ]
        ]
        
        # إنشاء جدول معلومات الفاتورة والعميل
        info_table = Table(invoice_info, colWidths=[doc.width/2.0]*2)
        info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (1, 0), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 1*cm))
        
        # إنشاء جدول عناصر الفاتورة
        if items:
            # رأس الجدول
            items_data = [
                [Paragraph("الوصف", self.arabic_style),
                 Paragraph("الكمية", self.arabic_style),
                 Paragraph("السعر", self.arabic_style),
                 Paragraph("الضريبة %", self.arabic_style),
                 Paragraph("الخصم %", self.arabic_style),
                 Paragraph("المجموع", self.arabic_style)]
            ]
            
            # إضافة عناصر الفاتورة
            for item in items:
                items_data.append([
                    Paragraph(item.description, self.arabic_style),
                    Paragraph(str(item.quantity), self.arabic_style),
                    Paragraph(f"{item.unit_price:.2f}", self.arabic_style),
                    Paragraph(f"{item.tax_rate:.2f}" if item.tax_rate else "0.00", self.arabic_style),
                    Paragraph(f"{item.discount_rate:.2f}" if item.discount_rate else "0.00", self.arabic_style),
                    Paragraph(f"{item.total:.2f}", self.arabic_style)
                ])
            
            # إنشاء جدول العناصر
            items_table = Table(items_data, colWidths=[doc.width*0.3, doc.width*0.1, doc.width*0.15, 
                                                      doc.width*0.1, doc.width*0.1, doc.width*0.15])
            items_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ]))
            elements.append(items_table)
            elements.append(Spacer(1, 0.5*cm))
            
            # إضافة ملخص المبالغ
            summary_data = [
                [Paragraph("المجموع الفرعي:", self.arabic_style), Paragraph(f"{invoice.subtotal:.2f}", self.arabic_style)],
                [Paragraph(f"الضريبة ({invoice.tax_rate:.2f}%):", self.arabic_style), Paragraph(f"{invoice.tax_amount:.2f}", self.arabic_style)],
                [Paragraph(f"الخصم ({invoice.discount_rate:.2f}%):", self.arabic_style), Paragraph(f"{invoice.discount_amount:.2f}", self.arabic_style)],
                [Paragraph("المجموع النهائي:", self.arabic_style), Paragraph(f"{invoice.total_amount:.2f}", self.arabic_style)],
                [Paragraph("المبلغ المدفوع:", self.arabic_style), Paragraph(f"{invoice.amount_paid:.2f}", self.arabic_style)],
                [Paragraph("المبلغ المستحق:", self.arabic_style), Paragraph(f"{invoice.amount_due:.2f}", self.arabic_style)]
            ]
            
            # إنشاء جدول ملخص المبالغ
            summary_table = Table(summary_data, colWidths=[doc.width*0.7, doc.width*0.2])
            summary_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT' if not self.rtl else 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ]))
            elements.append(summary_table)
        
        # إضافة ملاحظات الفاتورة
        if invoice.notes:
            elements.append(Spacer(1, 1*cm))
            elements.append(Paragraph("ملاحظات:", self.subheading_style))
            elements.append(Paragraph(invoice.notes, self.arabic_style))
        
        # إضافة شروط الدفع
        if invoice.payment_terms:
            elements.append(Spacer(1, 0.5*cm))
            elements.append(Paragraph("شروط الدفع:", self.subheading_style))
            elements.append(Paragraph(invoice.payment_terms, self.arabic_style))
        
        # إضافة تذييل الصفحة
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph(f"تم إنشاء هذه الفاتورة بواسطة نظام SmartBiz Accounting بتاريخ {datetime.now().strftime('%Y-%m-%d %H:%M')}", self.arabic_style))
        
        # بناء المستند
        doc.build(elements)
        
        return output_path
    
    def create_report_pdf(self, title, subtitle, data, columns, output_path=None):
        """
        إنشاء ملف PDF للتقرير
        
        المعلمات:
            title: عنوان التقرير
            subtitle: العنوان الفرعي للتقرير
            data: بيانات التقرير (قائمة من القواميس)
            columns: أعمدة التقرير (قاموس يحتوي على اسم العمود والمفتاح في البيانات)
            output_path: مسار الملف الناتج (اختياري)
            
        العائد:
            مسار الملف المؤقت إذا لم يتم تحديد مسار الإخراج
        """
        # إنشاء ملف مؤقت إذا لم يتم تحديد مسار الإخراج
        if not output_path:
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            output_path = temp_file.name
            temp_file.close()
        
        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # قائمة العناصر التي سيتم إضافتها إلى المستند
        elements = []
        
        # إضافة شعار الشركة إذا كان متاحًا
        logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'img', 'logo.png')
        if os.path.exists(logo_path):
            elements.append(Image(logo_path, width=5*cm, height=2*cm))
            elements.append(Spacer(1, 0.5*cm))
        
        # إضافة عنوان التقرير
        elements.append(Paragraph(title, self.heading_style))
        elements.append(Spacer(1, 0.3*cm))
        
        # إضافة العنوان الفرعي
        if subtitle:
            elements.append(Paragraph(subtitle, self.subheading_style))
            elements.append(Spacer(1, 0.5*cm))
        
        # إنشاء جدول البيانات
        if data and columns:
            # رأس الجدول
            table_data = [[Paragraph(col_name, self.arabic_style) for col_name in columns.keys()]]
            
            # إضافة صفوف البيانات
            for row in data:
                table_row = []
                for col_key in columns.values():
                    value = row.get(col_key, '')
                    # تنسيق القيم الرقمية
                    if isinstance(value, (int, float)):
                        value = f"{value:.2f}" if isinstance(value, float) else str(value)
                    # تنسيق التواريخ
                    elif isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d')
                    table_row.append(Paragraph(str(value), self.arabic_style))
                table_data.append(table_row)
            
            # حساب عرض الأعمدة
            col_widths = [doc.width / len(columns)] * len(columns)
            
            # إنشاء الجدول
            data_table = Table(table_data, colWidths=col_widths)
            data_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ]))
            elements.append(data_table)
        
        # إضافة تذييل الصفحة
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph(f"تم إنشاء هذا التقرير بواسطة نظام SmartBiz Accounting بتاريخ {datetime.now().strftime('%Y-%m-%d %H:%M')}", self.arabic_style))
        
        # بناء المستند
        doc.build(elements)
        
        return output_path
    
    def _get_status_text(self, status):
        """
        الحصول على النص العربي لحالة الفاتورة
        """
        status_map = {
            'paid': 'مدفوعة',
            'unpaid': 'غير مدفوعة',
            'partially_paid': 'مدفوعة جزئيًا',
            'cancelled': 'ملغاة',
            'overdue': 'متأخرة'
        }
        return status_map.get(status, status)