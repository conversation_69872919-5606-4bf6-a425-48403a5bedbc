# SmartBiz Accounting - نظام المحاسبة الذكي
# GitHub Actions للتكامل المستمر

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # تشغيل يومي في الساعة 2:00 صباحاً UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # فحص جودة الكود
  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality Checks
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        
    - name: Run pre-commit hooks
      uses: pre-commit/action@v3.0.0
      
    - name: <PERSON>t with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics
        
    - name: Check code formatting with black
      run: black --check .
      
    - name: Check import sorting with isort
      run: isort --check-only .
      
    - name: Security check with bandit
      run: bandit -r . -x tests/
      
    - name: Type checking with mypy
      run: mypy . --ignore-missing-imports
      continue-on-error: true

  # الاختبارات
  tests:
    runs-on: ubuntu-latest
    name: Tests
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
        
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_smartbiz
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          ${{ runner.os }}-pip-
          
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr tesseract-ocr-ara
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        
    - name: Set up environment variables
      run: |
        echo "TESTING=True" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_smartbiz" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key" >> $GITHUB_ENV
        
    - name: Run unit tests
      run: pytest tests/unit/ -v --cov=. --cov-report=xml
      
    - name: Run integration tests
      run: pytest tests/integration/ -v
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  # فحص الأمان
  security:
    runs-on: ubuntu-latest
    name: Security Scan
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # بناء Docker
  docker-build:
    runs-on: ubuntu-latest
    name: Docker Build
    needs: [code-quality, tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: smartbiz-accounting:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Test Docker image
      run: |
        docker run --rm smartbiz-accounting:latest python -c "import app; print('Docker image works!')"

  # النشر
  deploy:
    runs-on: ubuntu-latest
    name: Deploy
    needs: [code-quality, tests, security, docker-build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # إضافة خطوات النشر هنا
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # إضافة اختبارات الدخان هنا
        
    - name: Deploy to production
      if: success()
      run: |
        echo "Deploying to production environment..."
        # إضافة خطوات النشر للإنتاج هنا

  # إشعارات
  notify:
    runs-on: ubuntu-latest
    name: Notifications
    needs: [deploy]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        # إضافة إشعارات النجاح (Slack, Discord, etc.)
        
    - name: Notify on failure
      if: needs.deploy.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        # إضافة إشعارات الفشل
