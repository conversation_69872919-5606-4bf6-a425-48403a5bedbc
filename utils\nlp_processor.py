#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة معالجة النصوص ومعالجة اللغة الطبيعية (NLP)
"""

import re
import string
import numpy as np
import pandas as pd
from datetime import datetime
from collections import Counter

# محاولة استيراد المكتبات المتقدمة مع التعامل مع الأخطاء
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

class NLPProcessor:
    """
    فئة لمعالجة النصوص ومعالجة اللغة الطبيعية
    """
    
    def __init__(self, language='ar'):
        """
        تهيئة معالج NLP
        
        المعلمات:
            language: اللغة الافتراضية ('ar' للعربية، 'en' للإنجليزية)
        """
        self.language = language
        self.nlp = None
        self.sentiment_analyzer = None
        
        # تحميل نموذج spaCy إذا كان متاحًا
        if SPACY_AVAILABLE:
            try:
                if language == 'ar':
                    self.nlp = spacy.load('xx_ent_wiki_sm')  # نموذج متعدد اللغات
                elif language == 'en':
                    self.nlp = spacy.load('en_core_web_sm')
            except OSError:
                print("تحذير: نموذج spaCy غير متاح. استخدم 'python -m spacy download xx_ent_wiki_sm' أو 'python -m spacy download en_core_web_sm' لتثبيته.")
        
        # تهيئة محلل المشاعر إذا كانت مكتبة transformers متاحة
        if TRANSFORMERS_AVAILABLE:
            try:
                self.sentiment_analyzer = pipeline('sentiment-analysis')
            except Exception as e:
                print(f"تحذير: فشل تهيئة محلل المشاعر: {e}")
    
    def preprocess_text(self, text):
        """
        معالجة النص قبل التحليل
        
        المعلمات:
            text: النص المراد معالجته
            
        العائد:
            النص بعد المعالجة
        """
        if not text:
            return ""
        
        # تحويل النص إلى أحرف صغيرة (للنصوص الإنجليزية)
        if self.language == 'en':
            text = text.lower()
        
        # إزالة علامات الترقيم
        text = re.sub(f'[{re.escape(string.punctuation)}]', ' ', text)
        
        # إزالة الأرقام
        text = re.sub(r'\d+', ' ', text)
        
        # إزالة المسافات الزائدة
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_keywords(self, text, num_keywords=5):
        """
        استخراج الكلمات المفتاحية من النص
        
        المعلمات:
            text: النص المراد تحليله
            num_keywords: عدد الكلمات المفتاحية المراد استخراجها
            
        العائد:
            قائمة الكلمات المفتاحية
        """
        if not text:
            return []
        
        # معالجة النص
        processed_text = self.preprocess_text(text)
        
        if SPACY_AVAILABLE and self.nlp:
            # استخدام spaCy لاستخراج الكلمات المفتاحية
            doc = self.nlp(processed_text)
            
            # استبعاد كلمات التوقف والكلمات القصيرة
            keywords = [token.text for token in doc if not token.is_stop and len(token.text) > 2]
            
            # حساب تكرار الكلمات
            keyword_freq = Counter(keywords)
            
            # إرجاع الكلمات الأكثر تكرارًا
            return [word for word, freq in keyword_freq.most_common(num_keywords)]
        else:
            # طريقة بسيطة لاستخراج الكلمات المفتاحية بدون spaCy
            # تقسيم النص إلى كلمات
            words = processed_text.split()
            
            # استبعاد الكلمات القصيرة
            words = [word for word in words if len(word) > 2]
            
            # حساب تكرار الكلمات
            word_freq = Counter(words)
            
            # إرجاع الكلمات الأكثر تكرارًا
            return [word for word, freq in word_freq.most_common(num_keywords)]
    
    def analyze_sentiment(self, text):
        """
        تحليل مشاعر النص
        
        المعلمات:
            text: النص المراد تحليله
            
        العائد:
            قاموس يحتوي على تصنيف المشاعر والثقة
        """
        if not text:
            return {'label': 'neutral', 'score': 0.5}
        
        if TRANSFORMERS_AVAILABLE and self.sentiment_analyzer:
            try:
                # استخدام نموذج transformers لتحليل المشاعر
                result = self.sentiment_analyzer(text)[0]
                return result
            except Exception as e:
                print(f"خطأ في تحليل المشاعر: {e}")
                return {'label': 'neutral', 'score': 0.5}
        else:
            # طريقة بسيطة لتحليل المشاعر بدون transformers
            # قائمة الكلمات الإيجابية والسلبية
            positive_words = ['جيد', 'ممتاز', 'رائع', 'مذهل', 'سعيد', 'ناجح', 'إيجابي', 'good', 'excellent', 'great', 'amazing', 'happy', 'successful', 'positive']
            negative_words = ['سيء', 'ضعيف', 'فاشل', 'حزين', 'غاضب', 'سلبي', 'bad', 'poor', 'failed', 'sad', 'angry', 'negative']
            
            # معالجة النص
            processed_text = self.preprocess_text(text)
            words = processed_text.split()
            
            # حساب عدد الكلمات الإيجابية والسلبية
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)
            
            # تحديد المشاعر بناءً على عدد الكلمات
            if positive_count > negative_count:
                label = 'POSITIVE'
                score = 0.5 + (positive_count / (positive_count + negative_count + 1)) * 0.5
            elif negative_count > positive_count:
                label = 'NEGATIVE'
                score = 0.5 - (negative_count / (positive_count + negative_count + 1)) * 0.5
            else:
                label = 'NEUTRAL'
                score = 0.5
            
            return {'label': label, 'score': score}
    
    def categorize_text(self, text, categories):
        """
        تصنيف النص إلى فئة
        
        المعلمات:
            text: النص المراد تصنيفه
            categories: قاموس يحتوي على الفئات والكلمات المرتبطة بها
            
        العائد:
            الفئة المصنفة والثقة
        """
        if not text or not categories:
            return {'category': 'unknown', 'confidence': 0}
        
        # معالجة النص
        processed_text = self.preprocess_text(text)
        words = set(processed_text.split())
        
        # حساب تطابق الكلمات مع كل فئة
        category_scores = {}
        for category, category_words in categories.items():
            # حساب عدد الكلمات المتطابقة
            matching_words = words.intersection(set(category_words))
            score = len(matching_words) / len(category_words) if category_words else 0
            category_scores[category] = score
        
        # تحديد الفئة ذات أعلى درجة
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            return {'category': best_category[0], 'confidence': best_category[1]}
        else:
            return {'category': 'unknown', 'confidence': 0}
    
    def extract_entities(self, text):
        """
        استخراج الكيانات من النص (أشخاص، منظمات، أماكن، إلخ)
        
        المعلمات:
            text: النص المراد تحليله
            
        العائد:
            قاموس يحتوي على الكيانات المستخرجة
        """
        if not text:
            return {}
        
        if SPACY_AVAILABLE and self.nlp:
            # استخدام spaCy لاستخراج الكيانات
            doc = self.nlp(text)
            
            entities = {}
            for ent in doc.ents:
                entity_type = ent.label_
                if entity_type not in entities:
                    entities[entity_type] = []
                entities[entity_type].append(ent.text)
            
            return entities
        else:
            # طريقة بسيطة لاستخراج الكيانات بدون spaCy
            # استخراج الأرقام
            numbers = re.findall(r'\d+(?:\.\d+)?', text)
            
            # استخراج البريد الإلكتروني
            emails = re.findall(r'[\w\.-]+@[\w\.-]+', text)
            
            # استخراج عناوين URL
            urls = re.findall(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+', text)
            
            # استخراج التواريخ
            dates = re.findall(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', text)
            
            entities = {
                'NUMBER': numbers,
                'EMAIL': emails,
                'URL': urls,
                'DATE': dates
            }
            
            return {k: v for k, v in entities.items() if v}
    
    def summarize_text(self, text, num_sentences=3):
        """
        تلخيص النص
        
        المعلمات:
            text: النص المراد تلخيصه
            num_sentences: عدد الجمل في الملخص
            
        العائد:
            النص الملخص
        """
        if not text:
            return ""
        
        # تقسيم النص إلى جمل
        sentences = re.split(r'[.!?]\s+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) <= num_sentences:
            return text
        
        if SKLEARN_AVAILABLE:
            # استخدام TF-IDF وتشابه الجيب التمام لتحديد الجمل الأكثر أهمية
            vectorizer = TfidfVectorizer(stop_words='english' if self.language == 'en' else None)
            try:
                # تحويل الجمل إلى متجهات TF-IDF
                tfidf_matrix = vectorizer.fit_transform(sentences)
                
                # حساب مصفوفة التشابه
                similarity_matrix = cosine_similarity(tfidf_matrix, tfidf_matrix)
                
                # حساب درجة أهمية كل جملة
                scores = similarity_matrix.sum(axis=1)
                
                # اختيار الجمل ذات أعلى درجات
                ranked_sentences = sorted(((scores[i], i, s) for i, s in enumerate(sentences)), reverse=True)
                
                # اختيار أفضل الجمل
                top_sentences = [ranked_sentences[i][2] for i in range(min(num_sentences, len(ranked_sentences)))]
                
                # إعادة ترتيب الجمل حسب ترتيبها الأصلي في النص
                ordered_sentences = [s for _, i, s in sorted([(ranked_sentences[j][1], j, ranked_sentences[j][2]) for j in range(min(num_sentences, len(ranked_sentences)))])]
                
                return '. '.join(ordered_sentences) + '.'
            except Exception as e:
                print(f"خطأ في تلخيص النص: {e}")
                # استخدام الطريقة البسيطة في حالة الخطأ
                return '. '.join(sentences[:num_sentences]) + '.'
        else:
            # طريقة بسيطة لتلخيص النص بدون sklearn
            # اختيار الجمل الأولى
            return '. '.join(sentences[:num_sentences]) + '.'
    
    def find_similar_texts(self, query_text, text_list, top_n=3):
        """
        البحث عن النصوص المشابهة
        
        المعلمات:
            query_text: نص الاستعلام
            text_list: قائمة النصوص للبحث فيها
            top_n: عدد النتائج المراد إرجاعها
            
        العائد:
            قائمة بمؤشرات النصوص المشابهة ودرجات التشابه
        """
        if not query_text or not text_list:
            return []
        
        if SKLEARN_AVAILABLE:
            # استخدام TF-IDF وتشابه الجيب التمام للبحث عن النصوص المشابهة
            vectorizer = TfidfVectorizer(stop_words='english' if self.language == 'en' else None)
            try:
                # إضافة نص الاستعلام إلى قائمة النصوص
                all_texts = [query_text] + text_list
                
                # تحويل النصوص إلى متجهات TF-IDF
                tfidf_matrix = vectorizer.fit_transform(all_texts)
                
                # حساب تشابه نص الاستعلام مع النصوص الأخرى
                query_vector = tfidf_matrix[0:1]
                similarity_scores = cosine_similarity(query_vector, tfidf_matrix[1:]).flatten()
                
                # ترتيب النتائج حسب درجة التشابه
                top_indices = similarity_scores.argsort()[::-1][:top_n]
                
                return [(int(i), float(similarity_scores[i])) for i in top_indices]
            except Exception as e:
                print(f"خطأ في البحث عن النصوص المشابهة: {e}")
                return []
        else:
            # طريقة بسيطة للبحث عن النصوص المشابهة بدون sklearn
            # معالجة نص الاستعلام
            processed_query = self.preprocess_text(query_text)
            query_words = set(processed_query.split())
            
            # حساب تشابه الكلمات مع كل نص
            similarities = []
            for i, text in enumerate(text_list):
                processed_text = self.preprocess_text(text)
                text_words = set(processed_text.split())
                
                # حساب معامل جاكارد للتشابه
                if not query_words or not text_words:
                    similarity = 0
                else:
                    similarity = len(query_words.intersection(text_words)) / len(query_words.union(text_words))
                
                similarities.append((i, similarity))
            
            # ترتيب النتائج حسب درجة التشابه
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities[:top_n]
    
    def cluster_texts(self, text_list, num_clusters=3):
        """
        تجميع النصوص في مجموعات
        
        المعلمات:
            text_list: قائمة النصوص للتجميع
            num_clusters: عدد المجموعات
            
        العائد:
            قائمة بمؤشرات المجموعة لكل نص
        """
        if not text_list or len(text_list) < num_clusters:
            return [0] * len(text_list)
        
        if SKLEARN_AVAILABLE:
            # استخدام TF-IDF و K-means لتجميع النصوص
            vectorizer = TfidfVectorizer(stop_words='english' if self.language == 'en' else None)
            try:
                # تحويل النصوص إلى متجهات TF-IDF
                tfidf_matrix = vectorizer.fit_transform(text_list)
                
                # تطبيق خوارزمية K-means
                kmeans = KMeans(n_clusters=num_clusters, random_state=42)
                kmeans.fit(tfidf_matrix)
                
                # إرجاع تصنيفات المجموعات
                return kmeans.labels_.tolist()
            except Exception as e:
                print(f"خطأ في تجميع النصوص: {e}")
                return [0] * len(text_list)
        else:
            # طريقة بسيطة لتجميع النصوص بدون sklearn
            # تقسيم النصوص بالتساوي إلى مجموعات
            clusters = [i % num_clusters for i in range(len(text_list))]
            return clusters
    
    def generate_recommendations(self, user_data, transaction_history, client_data=None):
        """
        توليد توصيات ذكية بناءً على بيانات المستخدم وسجل المعاملات
        
        المعلمات:
            user_data: بيانات المستخدم
            transaction_history: سجل المعاملات
            client_data: بيانات العملاء (اختياري)
            
        العائد:
            قائمة التوصيات
        """
        recommendations = []
        
        # تحليل سجل المعاملات
        if transaction_history:
            # تحويل سجل المعاملات إلى DataFrame
            try:
                df = pd.DataFrame(transaction_history)
                
                # التحقق من وجود معاملات متكررة
                if 'category' in df.columns and 'amount' in df.columns:
                    # تحليل المصروفات حسب الفئة
                    expense_by_category = df[df['type'] == 'expense'].groupby('category')['amount'].sum()
                    
                    # تحديد الفئات ذات أعلى مصروفات
                    if not expense_by_category.empty:
                        top_expense_category = expense_by_category.idxmax()
                        top_expense_amount = expense_by_category.max()
                        
                        recommendations.append({
                            'type': 'expense_analysis',
                            'message': f"لاحظنا أن أعلى فئة مصروفات هي '{top_expense_category}' بمبلغ {top_expense_amount}. قد ترغب في مراجعة هذه المصروفات لتحديد فرص التوفير.",
                            'priority': 'medium'
                        })
                
                # تحليل الاتجاهات الموسمية
                if 'date' in df.columns and 'amount' in df.columns and 'type' in df.columns:
                    # تحويل التاريخ إلى كائن datetime
                    df['date'] = pd.to_datetime(df['date'], errors='coerce')
                    
                    # استخراج الشهر
                    df['month'] = df['date'].dt.month
                    
                    # تحليل الدخل والمصروفات حسب الشهر
                    monthly_income = df[df['type'] == 'income'].groupby('month')['amount'].sum()
                    monthly_expense = df[df['type'] == 'expense'].groupby('month')['amount'].sum()
                    
                    # تحديد الشهر ذو أعلى دخل وأعلى مصروفات
                    if not monthly_income.empty and not monthly_expense.empty:
                        top_income_month = monthly_income.idxmax()
                        top_expense_month = monthly_expense.idxmax()
                        
                        month_names = {
                            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل', 5: 'مايو', 6: 'يونيو',
                            7: 'يوليو', 8: 'أغسطس', 9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
                        }
                        
                        recommendations.append({
                            'type': 'seasonal_trend',
                            'message': f"لاحظنا أن شهر {month_names.get(top_income_month, top_income_month)} هو الأعلى من حيث الدخل، وشهر {month_names.get(top_expense_month, top_expense_month)} هو الأعلى من حيث المصروفات. يمكنك التخطيط لميزانيتك وفقًا لذلك.",
                            'priority': 'medium'
                        })
                
                # تحليل الفواتير غير المدفوعة
                if 'status' in df.columns and 'amount' in df.columns:
                    unpaid_invoices = df[df['status'] == 'unpaid']
                    if not unpaid_invoices.empty:
                        total_unpaid = unpaid_invoices['amount'].sum()
                        num_unpaid = len(unpaid_invoices)
                        
                        recommendations.append({
                            'type': 'unpaid_invoices',
                            'message': f"لديك {num_unpaid} فواتير غير مدفوعة بإجمالي {total_unpaid}. ننصح بمتابعة هذه الفواتير لتحسين التدفق النقدي.",
                            'priority': 'high'
                        })
            except Exception as e:
                print(f"خطأ في تحليل سجل المعاملات: {e}")
        
        # تحليل بيانات العملاء
        if client_data:
            try:
                # تحديد العملاء غير النشطين
                inactive_clients = []
                current_date = datetime.now()
                
                for client in client_data:
                    if 'last_contact_date' in client and client['last_contact_date']:
                        last_contact = datetime.strptime(client['last_contact_date'], '%Y-%m-%d')
                        days_since_contact = (current_date - last_contact).days
                        
                        if days_since_contact > 90:  # أكثر من 3 أشهر
                            inactive_clients.append(client['name'])
                
                if inactive_clients:
                    recommendations.append({
                        'type': 'inactive_clients',
                        'message': f"لديك {len(inactive_clients)} عملاء غير نشطين لم تتواصل معهم منذ أكثر من 3 أشهر. ننصح بالتواصل معهم للحفاظ على العلاقة التجارية.",
                        'priority': 'medium'
                    })
            except Exception as e:
                print(f"خطأ في تحليل بيانات العملاء: {e}")
        
        # توصيات عامة
        recommendations.append({
            'type': 'general',
            'message': "ننصح بمراجعة تقارير الأرباح والخسائر بشكل دوري لتحديد فرص تحسين الأداء المالي.",
            'priority': 'low'
        })
        
        recommendations.append({
            'type': 'general',
            'message': "قم بتحديث معلومات العملاء والمشاريع بانتظام للحفاظ على دقة البيانات وتحسين التواصل.",
            'priority': 'low'
        })
        
        # ترتيب التوصيات حسب الأولوية
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        recommendations.sort(key=lambda x: priority_order.get(x['priority'], 3))
        
        return recommendations
    
    def analyze_invoice_text(self, invoice_text):
        """
        تحليل نص الفاتورة لاستخراج المعلومات المهمة
        
        المعلمات:
            invoice_text: نص الفاتورة
            
        العائد:
            قاموس يحتوي على المعلومات المستخرجة
        """
        if not invoice_text:
            return {}
        
        # استخراج رقم الفاتورة
        invoice_number_pattern = r'(?:Invoice|فاتورة|رقم الفاتورة)[\s#:]*([A-Za-z0-9-]+)'
        invoice_number_match = re.search(invoice_number_pattern, invoice_text, re.IGNORECASE)
        invoice_number = invoice_number_match.group(1) if invoice_number_match else None
        
        # استخراج التاريخ
        date_pattern = r'(?:Date|تاريخ)[\s:]*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})'
        date_match = re.search(date_pattern, invoice_text, re.IGNORECASE)
        date = date_match.group(1) if date_match else None
        
        # استخراج المبلغ الإجمالي
        total_pattern = r'(?:Total|الإجمالي|المجموع)[\s:]*(?:SAR|USD|AED|ر\.س\.|\$)?\s*([\d,\.]+)'
        total_match = re.search(total_pattern, invoice_text, re.IGNORECASE)
        total_amount = None
        if total_match:
            try:
                total_amount = float(total_match.group(1).replace(',', ''))
            except ValueError:
                pass
        
        # استخراج اسم البائع
        vendor_pattern = r'(?:Vendor|Supplier|From|البائع|المورد|من)[\s:]*([A-Za-z\s]+|[\u0600-\u06FF\s]+)'
        vendor_match = re.search(vendor_pattern, invoice_text, re.IGNORECASE)
        vendor = vendor_match.group(1) if vendor_match else None
        
        # استخراج العناصر
        items = []
        item_section_pattern = r'(?:Items|Description|العناصر|الوصف).*?(?:Total|الإجمالي|المجموع)'
        item_section_match = re.search(item_section_pattern, invoice_text, re.IGNORECASE | re.DOTALL)
        
        if item_section_match:
            item_section = item_section_match.group(0)
            lines = item_section.split('\n')
            
            for line in lines:
                # تجاهل الأسطر الفارغة والعناوين
                if not line.strip() or re.search(r'(?:Items|Description|العناصر|الوصف|Qty|Quantity|الكمية|Price|السعر|Amount|المبلغ)', line, re.IGNORECASE):
                    continue
                
                # محاولة استخراج الكمية والسعر والمبلغ
                qty_match = re.search(r'(\d+)', line)
                price_match = re.search(r'(\d+(?:\.\d+)?)', line)
                
                if qty_match and price_match:
                    # استخراج الوصف (النص المتبقي بعد إزالة الأرقام)
                    description = re.sub(r'\d+(?:\.\d+)?', '', line).strip()
                    
                    items.append({
                        'description': description,
                        'quantity': int(qty_match.group(1)) if qty_match else 1,
                        'price': float(price_match.group(1)) if price_match else 0
                    })
        
        # تجميع المعلومات المستخرجة
        invoice_data = {
            'invoice_number': invoice_number,
            'date': date,
            'total_amount': total_amount,
            'vendor': vendor,
            'items': items
        }
        
        return {k: v for k, v in invoice_data.items() if v is not None}