{% extends "base.html" %}

{% block title %}قائمة الفواتير{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">قائمة الفواتير</h1>
            <p class="text-muted">إدارة جميع الفواتير</p>
        </div>
        <div>
            <a href="{{ url_for('invoices.add_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة فاتورة جديدة
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الفواتير</h6>
                            <h4 class="mb-0">{{ invoices|length }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-invoice fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المبلغ</h6>
                            <h4 class="mb-0">{{ total_amount|format_currency }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المبلغ المدفوع</h6>
                            <h4 class="mb-0">{{ total_paid|format_currency }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المبلغ غير المدفوع</h6>
                            <h4 class="mb-0">{{ total_unpaid|format_currency }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>فلترة النتائج
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if not status_filter or status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                        <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوعة</option>
                        <option value="unpaid" {% if status_filter == 'unpaid' %}selected{% endif %}>غير مدفوعة</option>
                        <option value="partially_paid" {% if status_filter == 'partially_paid' %}selected{% endif %}>مدفوعة جزئياً</option>
                        <option value="overdue" {% if status_filter == 'overdue' %}selected{% endif %}>متأخرة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="client_id" class="form-label">العميل</label>
                    <select class="form-select" id="client_id" name="client_id">
                        <option value="">جميع العملاء</option>
                        {% for client in clients %}
                        <option value="{{ client.id }}" {% if client_filter == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="project_id" class="form-label">المشروع</label>
                    <select class="form-select" id="project_id" name="project_id">
                        <option value="">جميع المشاريع</option>
                        {% for project in projects %}
                        <option value="{{ project.id }}" {% if project_filter == project.id|string %}selected{% endif %}>{{ project.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>الفواتير ({{ invoices|length }})
            </h5>
        </div>
        <div class="card-body p-0">
            {% if invoices %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المشروع</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>
                                <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="text-decoration-none">
                                    <strong>#{{ invoice.invoice_number }}</strong>
                                </a>
                            </td>
                            <td>
                                {% set client = clients|selectattr('id', 'equalto', invoice.client_id)|first %}
                                {{ client.name if client else 'غير محدد' }}
                            </td>
                            <td>
                                {% if invoice.project_id %}
                                    {% set project = projects|selectattr('id', 'equalto', invoice.project_id)|first %}
                                    {{ project.name if project else 'غير محدد' }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ invoice.total_amount|format_currency }}</td>
                            <td>
                                {% if invoice.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                {% elif invoice.status == 'unpaid' %}
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                {% elif invoice.status == 'partially_paid' %}
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                {% elif invoice.status == 'overdue' %}
                                    <span class="badge bg-dark">متأخرة</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('invoices.edit_invoice', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-secondary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if invoice.status != 'paid' %}
                                    <a href="{{ url_for('invoices.add_payment', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-success" title="إضافة دفعة">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </a>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete({{ invoice.id }}, '{{ invoice.invoice_number }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير</h5>
                <p class="text-muted">لم يتم العثور على أي فواتير تطابق معايير البحث.</p>
                <a href="{{ url_for('invoices.add_invoice') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة فاتورة جديدة
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف الفاتورة رقم <strong id="invoiceNumber"></strong>؟
                <br><br>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: هذا الإجراء لا يمكن التراجع عنه.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(invoiceId, invoiceNumber) {
    document.getElementById('invoiceNumber').textContent = invoiceNumber;
    document.getElementById('deleteForm').action = `/invoices/${invoiceId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
