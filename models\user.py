#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
نموذج المستخدم
"""

from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.ext.hybrid import hybrid_property
from extensions import db

class User(UserMixin, db.Model):
    """
نموذج المستخدم - يخزن معلومات المستخدمين في النظام
    """
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True, nullable=False)
    email = db.Column(db.String(120), unique=True, index=True, nullable=False)
    _password_hash = db.Column(db.String(128), nullable=False)
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    phone = db.Column(db.String(20))
    company_name = db.Column(db.String(100))
    address = db.Column(db.String(200))
    city = db.Column(db.String(64))
    country = db.Column(db.String(64))
    profile_image = db.Column(db.String(200))
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    language_preference = db.Column(db.String(10), default='ar')
    api_key = db.Column(db.String(128), unique=True, index=True)  # مفتاح API للمستخدم
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # العلاقات
    projects = db.relationship('Project', backref='owner', lazy='dynamic')
    clients = db.relationship('Client', backref='created_by', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='created_by', lazy='dynamic')

    @hybrid_property
    def password(self):
        raise AttributeError('كلمة المرور ليست خاصية يمكن قراءتها')

    @password.setter
    def password(self, password):
        self._password_hash = generate_password_hash(password)

    def verify_password(self, password):
        """
التحقق من كلمة المرور
        """
        return check_password_hash(self._password_hash, password)

    def check_password(self, password):
        """
التحقق من كلمة المرور (alias for verify_password)
        """
        return self.verify_password(password)

    @property
    def full_name(self):
        """
الحصول على الاسم الكامل للمستخدم
        """
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    def get_full_name(self):
        """
        دالة للحصول على الاسم الكامل (alias for full_name property)
        """
        return self.full_name

    def update_last_login(self):
        """
تحديث وقت آخر تسجيل دخول
        """
        self.last_login = datetime.utcnow()
        db.session.commit()

    def to_dict(self):
        """
تحويل بيانات المستخدم إلى قاموس
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'company_name': self.company_name,
            'is_admin': self.is_admin,
            'language_preference': self.language_preference,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

    def __repr__(self):
        return f'<User {self.username}>'