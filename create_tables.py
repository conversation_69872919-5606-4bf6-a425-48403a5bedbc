#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إنشاء جداول قاعدة البيانات
Create database tables
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from extensions import db
from models.user import User
from models.client import Client
from models.invoice import Invoice, InvoiceItem
from models.transaction import Transaction
from models.payment_method import PaymentMethod

# إنشاء تطبيق Flask بسيط
from flask import Flask

def create_simple_app():
    """إنشاء تطبيق Flask بسيط"""
    app = Flask(__name__)
    
    # إعدادات قاعدة البيانات
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smartbiz.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'dev-secret-key'
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    return app

def create_tables():
    """إنشاء جميع الجداول"""
    
    app = create_simple_app()
    
    with app.app_context():
        try:
            print("🔄 إنشاء جميع الجداول...")
            
            # حذف الجداول الموجودة
            db.drop_all()
            print("🗑️ تم حذف الجداول القديمة")
            
            # إنشاء جداول جديدة
            db.create_all()
            print("✅ تم إنشاء جميع الجداول")
            
            # التحقق من الجداول
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📋 الجداول المنشأة: {tables}")
            
            # التحقق من جدول العملاء
            if 'clients' in tables:
                columns = inspector.get_columns('clients')
                print("\n📋 أعمدة جدول العملاء:")
                for col in columns:
                    nullable = "NULL" if col['nullable'] else "NOT NULL"
                    print(f"  - {col['name']}: {col['type']} ({nullable})")
            
            # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
            if not User.query.first():
                print("\n🔄 إنشاء مستخدم افتراضي...")
                admin_user = User(
                    email='<EMAIL>',
                    password='admin123',  # سيتم تشفيرها تلقائياً
                    first_name='مدير',
                    last_name='النظام',
                    is_active=True
                )
                db.session.add(admin_user)
                db.session.commit()
                print(f"✅ تم إنشاء المستخدم: {admin_user.email}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_client_creation():
    """اختبار إنشاء عميل"""
    
    app = create_simple_app()
    
    with app.app_context():
        try:
            print("\n🔄 اختبار إنشاء عميل...")
            
            # الحصول على المستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين")
                return False
            
            # إنشاء عميل تجريبي
            test_client = Client(
                user_id=user.id,
                name='عميل تجريبي',
                email='<EMAIL>',
                phone='+966501234567',
                company_name='شركة تجريبية',
                is_active=True
            )
            
            db.session.add(test_client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل بـ ID: {test_client.id}")
            
            # حذف العميل التجريبي
            db.session.delete(test_client)
            db.session.commit()
            
            print("✅ تم حذف العميل التجريبي")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار العميل: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🚀 بدء إنشاء قاعدة البيانات...")
    
    success1 = create_tables()
    success2 = test_client_creation() if success1 else False
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
        print("✅ جميع الجداول جاهزة")
        print("✅ اختبار إنشاء العميل نجح")
    else:
        print("❌ حدثت مشاكل في إنشاء قاعدة البيانات")
    
    input("\nاضغط Enter للخروج...")
