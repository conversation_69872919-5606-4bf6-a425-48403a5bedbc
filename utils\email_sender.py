#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
أداة إرسال البريد الإلكتروني
"""

import os
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from datetime import datetime
from flask import current_app, render_template_string

# إعداد التسجيل
logger = logging.getLogger(__name__)

class EmailSender:
    """
    فئة لإرسال رسائل البريد الإلكتروني
    """
    
    def __init__(self, app=None):
        """
        تهيئة مرسل البريد الإلكتروني
        
        المعلمات:
            app: تطبيق Flask (اختياري)
        """
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """
        تهيئة مرسل البريد الإلكتروني مع تطبيق Flask
        
        المعلمات:
            app: تطبيق Flask
        """
        self.app = app
        self.smtp_server = app.config.get('MAIL_SERVER')
        self.smtp_port = app.config.get('MAIL_PORT')
        self.smtp_username = app.config.get('MAIL_USERNAME')
        self.smtp_password = app.config.get('MAIL_PASSWORD')
        self.use_tls = app.config.get('MAIL_USE_TLS', False)
        self.use_ssl = app.config.get('MAIL_USE_SSL', False)
        self.default_sender = app.config.get('MAIL_DEFAULT_SENDER')
        self.debug = app.config.get('MAIL_DEBUG', app.debug)
    
    def send_email(self, to, subject, body, html=None, sender=None, cc=None, bcc=None, attachments=None):
        """
        إرسال بريد إلكتروني
        
        المعلمات:
            to: المستلم أو قائمة المستلمين
            subject: موضوع البريد الإلكتروني
            body: نص البريد الإلكتروني
            html: نص HTML للبريد الإلكتروني (اختياري)
            sender: المرسل (اختياري، يستخدم الافتراضي إذا لم يتم تحديده)
            cc: نسخة كربونية (اختياري)
            bcc: نسخة كربونية مخفية (اختياري)
            attachments: قائمة المرفقات (اختياري)
            
        العائد:
            bool: نجاح أو فشل الإرسال
        """
        if not self.smtp_server or not self.smtp_port:
            logger.error("SMTP server not configured")
            return False
        
        if not sender:
            sender = self.default_sender
        
        if not sender:
            logger.error("No sender specified and no default sender configured")
            return False
        
        # تحويل المستلم إلى قائمة إذا كان نصًا
        if isinstance(to, str):
            to = [to]
        
        # تحويل النسخة الكربونية إلى قائمة إذا كانت نصًا
        if cc and isinstance(cc, str):
            cc = [cc]
        
        # تحويل النسخة الكربونية المخفية إلى قائمة إذا كانت نصًا
        if bcc and isinstance(bcc, str):
            bcc = [bcc]
        
        try:
            # إنشاء رسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = sender
            msg['To'] = ', '.join(to)
            
            if cc:
                msg['Cc'] = ', '.join(cc)
                to.extend(cc)
            
            if bcc:
                to.extend(bcc)
            
            # إضافة نص الرسالة
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # إضافة نص HTML إذا كان متاحًا
            if html:
                msg.attach(MIMEText(html, 'html', 'utf-8'))
            
            # إضافة المرفقات
            if attachments:
                for attachment in attachments:
                    with open(attachment, 'rb') as file:
                        part = MIMEApplication(file.read(), Name=os.path.basename(attachment))
                    part['Content-Disposition'] = f'attachment; filename="{os.path.basename(attachment)}"'
                    msg.attach(part)
            
            # إنشاء اتصال SMTP
            if self.use_ssl:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.debug:
                server.set_debuglevel(1)
            
            if self.use_tls:
                server.starttls()
            
            # تسجيل الدخول إذا كان اسم المستخدم وكلمة المرور متاحين
            if self.smtp_username and self.smtp_password:
                server.login(self.smtp_username, self.smtp_password)
            
            # إرسال البريد الإلكتروني
            server.sendmail(sender, to, msg.as_string())
            server.quit()
            
            logger.info(f"Email sent to {', '.join(to)}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
    
    def send_invoice_reminder(self, invoice, client, user):
        """
        إرسال تذكير بالفاتورة
        
        المعلمات:
            invoice: كائن الفاتورة
            client: كائن العميل
            user: كائن المستخدم
            
        العائد:
            bool: نجاح أو فشل الإرسال
        """
        if not client.email:
            logger.error(f"Cannot send reminder: Client {client.id} has no email address")
            return False
        
        # إعداد قالب البريد الإلكتروني
        subject = f"تذكير: فاتورة مستحقة #{invoice.invoice_number}"
        
        # إنشاء نص البريد الإلكتروني
        body = f"""عزيزي {client.name}،

هذا تذكير بأن الفاتورة رقم {invoice.invoice_number} مستحقة الدفع.

تفاصيل الفاتورة:
- رقم الفاتورة: {invoice.invoice_number}
- تاريخ الإصدار: {invoice.issue_date.strftime('%Y-%m-%d')}
- تاريخ الاستحقاق: {invoice.due_date.strftime('%Y-%m-%d')}
- المبلغ المستحق: {invoice.amount_due} {invoice.currency}

يرجى سداد المبلغ في أقرب وقت ممكن.

مع خالص الشكر والتقدير،
{user.first_name} {user.last_name}
{user.company_name if hasattr(user, 'company_name') else ''}
"""
        
        # إنشاء نص HTML للبريد الإلكتروني
        html_template = """
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; margin-bottom: 20px; }
                .invoice-details { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .footer { margin-top: 30px; font-size: 12px; color: #777; }
                .amount { font-weight: bold; color: #e74c3c; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>تذكير بفاتورة مستحقة</h2>
                </div>
                
                <p>عزيزي {{ client_name }}،</p>
                
                <p>هذا تذكير بأن الفاتورة رقم {{ invoice_number }} مستحقة الدفع.</p>
                
                <div class="invoice-details">
                    <h3>تفاصيل الفاتورة:</h3>
                    <p>رقم الفاتورة: {{ invoice_number }}</p>
                    <p>تاريخ الإصدار: {{ issue_date }}</p>
                    <p>تاريخ الاستحقاق: {{ due_date }}</p>
                    <p>المبلغ المستحق: <span class="amount">{{ amount_due }} {{ currency }}</span></p>
                </div>
                
                <p>يرجى سداد المبلغ في أقرب وقت ممكن.</p>
                
                <p>مع خالص الشكر والتقدير،<br>
                {{ user_name }}<br>
                {{ company_name }}</p>
                
                <div class="footer">
                    <p>تم إرسال هذا البريد الإلكتروني تلقائيًا من نظام SmartBiz Accounting.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # تعبئة قالب HTML
        html = render_template_string(
            html_template,
            client_name=client.name,
            invoice_number=invoice.invoice_number,
            issue_date=invoice.issue_date.strftime('%Y-%m-%d'),
            due_date=invoice.due_date.strftime('%Y-%m-%d'),
            amount_due=f"{invoice.amount_due:.2f}",
            currency=invoice.currency,
            user_name=f"{user.first_name} {user.last_name}",
            company_name=user.company_name if hasattr(user, 'company_name') else ''
        )
        
        # إرسال البريد الإلكتروني
        return self.send_email(
            to=client.email,
            subject=subject,
            body=body,
            html=html,
            sender=user.email if hasattr(user, 'email') and user.email else None
        )
    
    def send_password_reset(self, user, reset_url):
        """
        إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
        
        المعلمات:
            user: كائن المستخدم
            reset_url: رابط إعادة تعيين كلمة المرور
            
        العائد:
            bool: نجاح أو فشل الإرسال
        """
        if not user.email:
            logger.error(f"Cannot send password reset: User {user.id} has no email address")
            return False
        
        # إعداد قالب البريد الإلكتروني
        subject = "إعادة تعيين كلمة المرور"
        
        # إنشاء نص البريد الإلكتروني
        body = f"""عزيزي {user.first_name} {user.last_name}،

لقد تلقينا طلبًا لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام SmartBiz Accounting.

لإعادة تعيين كلمة المرور، يرجى النقر على الرابط التالي:
{reset_url}

هذا الرابط صالح لمدة 24 ساعة فقط.

إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.

مع خالص التحية،
فريق SmartBiz Accounting
"""
        
        # إنشاء نص HTML للبريد الإلكتروني
        html_template = """
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; margin-bottom: 20px; }
                .reset-button { display: inline-block; background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { margin-top: 30px; font-size: 12px; color: #777; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>إعادة تعيين كلمة المرور</h2>
                </div>
                
                <p>عزيزي {{ user_name }}،</p>
                
                <p>لقد تلقينا طلبًا لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام SmartBiz Accounting.</p>
                
                <p>لإعادة تعيين كلمة المرور، يرجى النقر على الزر التالي:</p>
                
                <a href="{{ reset_url }}" class="reset-button">إعادة تعيين كلمة المرور</a>
                
                <p>أو يمكنك نسخ ولصق الرابط التالي في متصفحك:</p>
                <p>{{ reset_url }}</p>
                
                <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
                
                <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
                
                <p>مع خالص التحية،<br>
                فريق SmartBiz Accounting</p>
                
                <div class="footer">
                    <p>تم إرسال هذا البريد الإلكتروني تلقائيًا من نظام SmartBiz Accounting.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # تعبئة قالب HTML
        html = render_template_string(
            html_template,
            user_name=f"{user.first_name} {user.last_name}",
            reset_url=reset_url
        )
        
        # إرسال البريد الإلكتروني
        return self.send_email(
            to=user.email,
            subject=subject,
            body=body,
            html=html
        )