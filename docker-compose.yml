# SmartBiz Accounting - نظام المحاسبة الذكي
# Docker Compose Configuration

version: '3.8'

services:
  # تطبيق SmartBiz الرئيسي
  app:
    build: .
    container_name: smartbiz_app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***********************************************/smartbiz_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./static/uploads:/app/static/uploads
    depends_on:
      - db
      - redis
    networks:
      - smartbiz_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # قاعدة البيانات PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: smartbiz_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: smartbiz_db
      POSTGRES_USER: smartbiz
      POSTGRES_PASSWORD: smartbiz_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - smartbiz_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smartbiz -d smartbiz_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    container_name: smartbiz_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - smartbiz_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker للمهام الخلفية
  celery_worker:
    build: .
    container_name: smartbiz_celery_worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***********************************************/smartbiz_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - smartbiz_network

  # Celery Beat للمهام المجدولة
  celery_beat:
    build: .
    container_name: smartbiz_celery_beat
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***********************************************/smartbiz_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - smartbiz_network

  # Nginx كخادم ويب عكسي
  nginx:
    image: nginx:alpine
    container_name: smartbiz_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./static:/var/www/static:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - smartbiz_network

  # Flower لمراقبة Celery
  flower:
    build: .
    container_name: smartbiz_flower
    restart: unless-stopped
    command: celery -A app.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    networks:
      - smartbiz_network

  # Elasticsearch للبحث المتقدم (اختياري)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: smartbiz_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - smartbiz_network
    profiles:
      - search

  # Kibana لمراقبة Elasticsearch (اختياري)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: smartbiz_kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - smartbiz_network
    profiles:
      - search

  # Prometheus للمراقبة (اختياري)
  prometheus:
    image: prom/prometheus:latest
    container_name: smartbiz_prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - smartbiz_network
    profiles:
      - monitoring

  # Grafana للوحات المراقبة (اختياري)
  grafana:
    image: grafana/grafana:latest
    container_name: smartbiz_grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - smartbiz_network
    profiles:
      - monitoring

# الشبكات
networks:
  smartbiz_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# الأحجام المستمرة
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
