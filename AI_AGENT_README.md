# AI Agent المحسن - د<PERSON>ي<PERSON> الاستخدام

## 🤖 نظرة عامة

تم تحسين AI Agent في نظام SmartBiz ليصبح أكثر ذكاءً وقدرة على التعلم. يتضمن الآن:

- **تكامل مع Google Gemini AI** للردود الذكية والمتقدمة
- **نظام تعلم تدريجي** يتحسن مع الاستخدام
- **ذاكرة متقدمة** تتذكر تفضيلات المستخدم
- **معالجة لغة طبيعية محسنة** للعربية
- **تحليلات ذكية** وتوصيات مخصصة
- **اقتراحات مخصصة** حسب مستوى خبرة المستخدم

## 🔧 التثبيت والإعداد

### 1. تثبيت المكتبات المطلوبة

```bash
python install_requirements.py
```

أو يدوياً:

```bash
pip install google-generativeai python-dotenv numpy scikit-learn pandas
```

### 2. إعداد مفتاح Google Gemini AI API

مفتاح Google Gemini AI API يجب إضافته في ملف `.env`:

```env
GEMINI_API_KEY=your-api-key-here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=4000
GEMINI_TEMPERATURE=0.7
GEMINI_TOP_K=40
GEMINI_TOP_P=0.95
```

### 3. اختبار النظام

```bash
python test_gemini_integration.py
```

## 🚀 الميزات الجديدة

### 1. التحيات المخصصة
- يتعرف على نوع المستخدم (جديد، عائد، خبير)
- يخصص الردود بناءً على التفاعلات السابقة

### 2. الأسئلة المحاسبية المحسنة
- يجمع بين قاعدة المعرفة المحلية و OpenAI
- يقدم إجابات شاملة ومفصلة
- يتعلم من الأسئلة المتكررة

### 3. التحليل الذكي
- تحليل متقدم للبيانات المالية
- رؤى ذكية باستخدام AI
- توصيات مخصصة للتحسين

### 4. نظام التعلم التدريجي
- يتذكر تفضيلات المستخدم
- يحسن دقة الردود مع الوقت
- يكتشف مستوى خبرة المستخدم

### 5. الذاكرة المتقدمة
- ذاكرة قصيرة المدى للسياق الفوري
- ذاكرة طويلة المدى للمعلومات المهمة
- ذاكرة دلالية للمفاهيم والعلاقات

## 📝 أمثلة الاستخدام

### استخدام أساسي

```python
from ai_agent.agent import SmartBizAgent

# إنشاء مثيل من الوكيل
agent = SmartBizAgent()

# معالجة رسالة
result = agent.process_message("احسب إجمالي الأرباح", user_id=1)

print(f"الرد: {result['response']}")
print(f"النية: {result['intent']}")
print(f"الثقة: {result['confidence']}")
```

### أمثلة على الرسائل

```python
# تحيات
agent.process_message("مرحبا", user_id=1)

# أسئلة محاسبية
agent.process_message("ما هو مبدأ الاستحقاق؟", user_id=1)

# طلبات حسابية
agent.process_message("احسب صافي الربح", user_id=1)

# طلبات تحليل
agent.process_message("حلل اتجاه المبيعات", user_id=1)

# طلبات توصيات
agent.process_message("قدم توصيات للتحسين", user_id=1)

# طلبات توقعات
agent.process_message("توقع المبيعات القادمة", user_id=1)
```

## 🔍 التحليلات المتاحة

### 1. تحليل اتجاه المبيعات
```python
result = agent.analyze_sales_trend(user_id=1)
```

### 2. تحليل شرائح العملاء
```python
result = agent.analyze_client_segments(user_id=1)
```

### 3. التحليل المالي الشامل
```python
response = agent.process_message("حلل الوضع المالي", user_id=1)
```

## 🎯 التخصيص والتعلم

### مستويات الخبرة
- **مبتدئ**: ردود مبسطة وتعليمية
- **متوسط**: ردود متوازنة مع تفاصيل إضافية
- **خبير**: ردود متقدمة وتحليلات عميقة

### التعلم التدريجي
- يتعلم من كل تفاعل
- يحسن دقة كشف النوايا
- يخصص الاقتراحات للمستخدم

## 🛠️ التكوين المتقدم

### إعدادات التعلم
```python
from ai_agent.config import AIAgentConfig

# تحديث إعدادات التعلم
AIAgentConfig.update_config('advanced_learning', {
    'learning_algorithms': {
        'reinforcement_learning': {
            'enabled': True,
            'learning_rate': 0.01
        }
    }
})
```

### إعدادات الأداء
```python
# تحسين الأداء
AIAgentConfig.update_config('performance', {
    'caching': {
        'enabled': True,
        'ttl': 3600
    }
})
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

1. **مكتبة OpenAI غير مثبتة**
   ```bash
   pip install openai
   ```

2. **مفتاح API غير صحيح**
   - تحقق من ملف `.env`
   - تأكد من صحة المفتاح

3. **أخطاء في التعلم**
   - تحقق من السجلات في `app.log`
   - أعد تشغيل النظام

### السجلات
```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 📊 مراقبة الأداء

### إحصائيات الاستخدام
```python
# عرض إحصائيات الاستخدام
stats = agent.nlp.usage_stats
print(f"إجمالي التفاعلات: {stats['total_interactions']}")
print(f"توزيع النوايا: {dict(stats['intent_distribution'])}")
```

### تقييم الأداء
```python
# تقييم دقة النظام
accuracy = agent.evaluate_performance()
print(f"دقة النظام: {accuracy:.2%}")
```

## 🔮 المستقبل

### ميزات قادمة
- تكامل مع المزيد من نماذج AI
- تحليلات تنبؤية متقدمة
- واجهة صوتية
- تكامل مع أنظمة خارجية

### المساهمة
- أرسل تقارير الأخطاء
- اقترح ميزات جديدة
- شارك في التطوير

---

**ملاحظة**: هذا النظام في تطوير مستمر. تأكد من تحديث المكتبات بانتظام للحصول على أحدث الميزات.
