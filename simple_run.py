#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل بسيط لنظام SmartBiz
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        print("  - extensions...")
        from extensions import db, login_manager
        print("    ✅ extensions")
        
        print("  - models...")
        from models.user import User
        print("    ✅ models")
        
        print("  - blueprints...")
        from blueprints.auth import auth_bp
        print("    ✅ blueprints")
        
        print("  - app...")
        from app import create_app
        print("    ✅ app")
        
        return True
    except Exception as e:
        print(f"    ❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_app():
    """إنشاء تطبيق للاختبار"""
    print("\n🔧 إنشاء التطبيق...")
    
    try:
        from app import create_app
        app = create_app()
        print("  ✅ تم إنشاء التطبيق")
        return app
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_database(app):
    """اختبار قاعدة البيانات"""
    print("\n💾 اختبار قاعدة البيانات...")
    
    try:
        with app.app_context():
            from extensions import db
            db.create_all()
            print("  ✅ تم إنشاء الجداول")
            return True
    except Exception as e:
        print(f"  ❌ خطأ في قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_server(app):
    """تشغيل الخادم"""
    print("\n🚀 تشغيل الخادم...")
    
    try:
        print("=" * 50)
        print("🎉 نظام SmartBiz جاهز!")
        print("🌐 الروابط:")
        print("   - الصفحة الرئيسية: http://127.0.0.1:5000")
        print("   - لوحة التحكم: http://127.0.0.1:5000/dashboard")
        print("   - العملاء: http://127.0.0.1:5000/clients")
        print("   - الفواتير: http://127.0.0.1:5000/invoices")
        print("   - المشاريع: http://127.0.0.1:5000/projects")
        print("   - التقارير: http://127.0.0.1:5000/reports")
        print("\n🔑 بيانات الدخول:")
        print("   البريد: <EMAIL>")
        print("   كلمة المرور: admin123")
        print("\n⏹️ اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام SmartBiz...")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء التطبيق
    app = create_test_app()
    if not app:
        print("\n❌ فشل في إنشاء التطبيق")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار قاعدة البيانات
    if not test_database(app):
        print("\n❌ فشل في اختبار قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل الخادم
    run_server(app)

if __name__ == '__main__':
    main()
