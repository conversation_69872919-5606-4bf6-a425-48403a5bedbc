#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار نموذج إضافة العميل عبر HTTP
Test client form via HTTP
"""

import requests
import time

def test_client_form():
    """اختبار نموذج إضافة العميل"""
    
    try:
        print("🔄 اختبار نموذج إضافة العميل...")
        
        # التحقق من أن الخادم يعمل
        try:
            response = requests.get('http://localhost:5000/', timeout=5)
            print("✅ الخادم يعمل")
        except requests.exceptions.ConnectionError:
            print("❌ الخادم لا يعمل - يرجى تشغيل التطبيق أولاً")
            return False
        
        # بيانات العميل للاختبار
        client_data = {
            'name': 'عميل اختبار HTTP',
            'email': '<EMAIL>',
            'phone': '+966501111111',
            'address': 'عنوان تجريبي',
            'company_name': 'شركة الاختبار',
            'payment_method': 'cash',
            'notes': 'ملاحظات تجريبية',
            'is_active': 'on'
        }
        
        print(f"📊 بيانات العميل: {client_data}")
        
        # إرسال طلب POST
        response = requests.post(
            'http://localhost:5000/clients/add',
            data=client_data,
            allow_redirects=False,
            timeout=10
        )
        
        print(f"📈 رمز الاستجابة: {response.status_code}")
        print(f"📋 عناوين الاستجابة: {dict(response.headers)}")
        
        if response.status_code == 302:
            # إعادة توجيه - قد يعني نجاح العملية
            location = response.headers.get('Location', '')
            print(f"✅ تم إعادة التوجيه إلى: {location}")
            
            if '/clients/' in location and location.split('/')[-1].isdigit():
                print("🎉 يبدو أن العميل تم إنشاؤه بنجاح!")
                return True
            else:
                print("⚠️ إعادة توجيه غير متوقعة")
                return False
                
        elif response.status_code == 200:
            # عودة إلى نفس الصفحة - قد يعني وجود خطأ
            print("⚠️ تم إرجاع نفس الصفحة")
            
            # البحث عن رسائل الخطأ
            if 'alert-danger' in response.text:
                print("❌ تم العثور على رسالة خطأ في الصفحة")
                # محاولة استخراج رسالة الخطأ
                import re
                error_pattern = r'<div[^>]*alert-danger[^>]*>(.*?)</div>'
                errors = re.findall(error_pattern, response.text, re.DOTALL)
                for error in errors:
                    # تنظيف HTML
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    print(f"   📝 رسالة الخطأ: {clean_error}")
            else:
                print("✅ لا توجد رسائل خطأ واضحة")
            
            return False
            
        else:
            print(f"❌ رمز استجابة غير متوقع: {response.status_code}")
            print(f"📄 محتوى الاستجابة: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_with_session():
    """اختبار مع جلسة تسجيل دخول"""
    
    try:
        print("\n🔄 اختبار مع جلسة تسجيل دخول...")
        
        session = requests.Session()
        
        # محاولة الحصول على صفحة تسجيل الدخول
        login_response = session.get('http://localhost:5000/auth/login')
        
        if login_response.status_code == 200:
            print("✅ تم الوصول لصفحة تسجيل الدخول")
            
            # محاولة تسجيل الدخول (إذا كان هناك مستخدم افتراضي)
            login_data = {
                'email': '<EMAIL>',
                'password': 'admin123'
            }
            
            login_post = session.post(
                'http://localhost:5000/auth/login',
                data=login_data,
                allow_redirects=False
            )
            
            if login_post.status_code == 302:
                print("✅ تم تسجيل الدخول بنجاح")
                
                # الآن محاولة إضافة عميل
                client_data = {
                    'name': 'عميل مع جلسة',
                    'email': '<EMAIL>',
                    'phone': '+966502222222',
                    'company_name': 'شركة الجلسة',
                    'is_active': 'on'
                }
                
                add_response = session.post(
                    'http://localhost:5000/clients/add',
                    data=client_data,
                    allow_redirects=False
                )
                
                print(f"📈 رمز استجابة إضافة العميل: {add_response.status_code}")
                
                if add_response.status_code == 302:
                    location = add_response.headers.get('Location', '')
                    print(f"✅ تم إعادة التوجيه إلى: {location}")
                    return True
                else:
                    print("❌ فشل في إضافة العميل")
                    return False
            else:
                print("❌ فشل في تسجيل الدخول")
                return False
        else:
            print("❌ لا يمكن الوصول لصفحة تسجيل الدخول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الجلسة: {e}")
        return False

if __name__ == '__main__':
    print("🚀 بدء اختبار نموذج إضافة العميل...")
    
    # انتظار قليل للتأكد من تشغيل الخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(2)
    
    success1 = test_client_form()
    success2 = test_with_session()
    
    print("\n" + "="*50)
    if success1 or success2:
        print("🎉 نجح أحد الاختبارات على الأقل!")
        print("✅ النموذج يعمل بشكل صحيح")
    else:
        print("❌ فشلت جميع الاختبارات")
        print("⚠️ هناك مشكلة في النموذج أو الخادم")
    
    input("\nاضغط Enter للخروج...")
