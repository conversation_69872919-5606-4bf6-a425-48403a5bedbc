#!/usr/bin/env python3
"""
تشغيل نظام SmartBiz النظيف مع Google Gemini AI
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    print("🚀 تشغيل نظام SmartBiz النظيف...")
    print("=" * 60)
    
    try:
        # اختبار Google Gemini AI
        print("🤖 فحص Google Gemini AI...")
        try:
            # فحص مفتاح API
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.environ.get('GEMINI_API_KEY')
            if api_key:
                print("  ✅ مفتاح Google Gemini API متوفر")
                
                # اختبار سريع للاتصال
                import requests
                api_url = f"https://generativelanguage.googleapis.com/v1beta/models?key={api_key}"
                
                try:
                    response = requests.get(api_url, timeout=5)
                    if response.status_code == 200:
                        print("  ✅ Google Gemini AI متصل ويعمل")
                    else:
                        print("  ⚠️ Google Gemini API متاح (تم تخطي الاختبار)")
                except:
                    print("  ⚠️ تم تخطي اختبار الاتصال (سيعمل النظام البديل)")
            else:
                print("  ⚠️ مفتاح Google Gemini غير متوفر (سيعمل النظام البديل)")
                
        except Exception as e:
            print(f"  ⚠️ تحذير: {e}")
            print("  ✅ سيعمل النظام بالوضع البديل")
        
        # استيراد التطبيق
        print("\n📦 استيراد التطبيق...")
        from app import create_app
        
        # إنشاء التطبيق
        print("🔧 إنشاء التطبيق...")
        app = create_app()
        
        # إعدادات التشغيل
        host = '127.0.0.1'
        port = 5000
        debug = True
        
        print("\n" + "=" * 60)
        print("🎉 نظام SmartBiz جاهز!")
        print("🌐 الروابط:")
        print(f"   - الصفحة الرئيسية: http://{host}:{port}")
        print(f"   - المساعد الذكي: http://{host}:{port}/ai/chat")
        print(f"   - لوحة التحكم: http://{host}:{port}/dashboard")
        print(f"   - التقارير: http://{host}:{port}/reports")
        
        print("\n🤖 **المساعد الذكي:**")
        print("   - مدعوم بـ Google Gemini AI")
        print("   - نظام بديل ذكي متاح")
        print("   - إجابات دقيقة باللغة العربية")
        print("   - تحليلات مالية متقدمة")
        print("   - اقتراحات ذكية ومخصصة")
        
        print("\n📊 **الميزات المتاحة:**")
        print("   - إدارة العملاء والمشاريع")
        print("   - إنشاء الفواتير والتقارير")
        print("   - تتبع المعاملات المالية")
        print("   - تحليلات الأعمال الذكية")
        print("   - نظام المساعد الذكي")
        
        print("\n⚡ للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        print("شكراً لاستخدام نظام SmartBiz! 👋")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
