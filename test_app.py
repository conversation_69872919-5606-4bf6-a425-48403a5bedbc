#!/usr/bin/env python3
"""
اختبار تشغيل التطبيق
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل نظام المحاسبة الذكي...")
        
        # استيراد التطبيق
        from app import create_app, create_db
        
        # إنشاء التطبيق
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح!")
        
        # إنشاء قاعدة البيانات
        create_db(app)
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        
        print("🌐 الرابط: http://127.0.0.1:5000")
        print("🔧 للإيقاف: اضغط Ctrl+C")
        print("=" * 50)
        
        # تشغيل التطبيق
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
