#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار سريع لإضافة عميل
Quick client addition test
"""

from app import create_app
from extensions import db
from models.client import Client
from models.user import User

def quick_test():
    """اختبار سريع"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 اختبار سريع لإضافة عميل...")
            
            # التحقق من المستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين")
                return False
            
            print(f"✅ المستخدم: {user.email}")
            
            # إنشاء عميل بسيط
            client = Client(
                user_id=user.id,
                name='اختبار سريع',
                email='<EMAIL>'
            )
            
            db.session.add(client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل بـ ID: {client.id}")
            
            # حذف العميل
            db.session.delete(client)
            db.session.commit()
            
            print("✅ تم حذف العميل")
            return True
            
        except Exception as e:
            print(f"❌ خطأ: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    success = quick_test()
    if success:
        print("🎉 الاختبار نجح!")
    else:
        print("❌ الاختبار فشل!")
    
    input("اضغط Enter...")
