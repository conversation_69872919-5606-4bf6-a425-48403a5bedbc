#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إضافة طرق الدفع الافتراضية إلى قاعدة البيانات
Add default payment methods to database
"""

from app import create_app
from extensions import db
from models.payment_method import PaymentMethod

def add_default_payment_methods():
    """إضافة طرق الدفع الافتراضية"""
    
    # إنشاء التطبيق
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 إضافة طرق الدفع الافتراضية...")
            
            # طرق الدفع الافتراضية
            default_payment_methods = [
                {
                    'name': 'نقداً',
                    'code': 'CASH',
                    'description': 'الدفع نقداً',
                    'icon': 'fas fa-money-bill-wave',
                    'is_active': True
                },
                {
                    'name': 'تحويل بنكي',
                    'code': 'BANK_TRANSFER',
                    'description': 'تحويل بنكي مباشر',
                    'icon': 'fas fa-university',
                    'is_active': True
                },
                {
                    'name': 'بطاقة ائتمان',
                    'code': 'CREDIT_CARD',
                    'description': 'الدفع بالبطاقة الائتمانية',
                    'icon': 'fas fa-credit-card',
                    'is_active': True
                },
                {
                    'name': 'بطاقة خصم',
                    'code': 'DEBIT_CARD',
                    'description': 'الدفع ببطاقة الخصم',
                    'icon': 'fas fa-credit-card',
                    'is_active': True
                },
                {
                    'name': 'شيك',
                    'code': 'CHECK',
                    'description': 'الدفع بالشيك',
                    'icon': 'fas fa-file-invoice',
                    'is_active': True
                },
                {
                    'name': 'محفظة إلكترونية',
                    'code': 'E_WALLET',
                    'description': 'الدفع عبر المحفظة الإلكترونية',
                    'icon': 'fas fa-wallet',
                    'is_active': True
                },
                {
                    'name': 'PayPal',
                    'code': 'PAYPAL',
                    'description': 'الدفع عبر PayPal',
                    'icon': 'fab fa-paypal',
                    'is_active': True
                },
                {
                    'name': 'آجل',
                    'code': 'DEFERRED',
                    'description': 'دفع آجل (بالتقسيط)',
                    'icon': 'fas fa-calendar-alt',
                    'is_active': True
                }
            ]
            
            # التحقق من وجود طرق الدفع وإضافة الجديدة فقط
            added_count = 0
            for method_data in default_payment_methods:
                existing = PaymentMethod.query.filter_by(code=method_data['code']).first()
                if not existing:
                    payment_method = PaymentMethod(
                        name=method_data['name'],
                        code=method_data['code'],
                        description=method_data['description'],
                        icon=method_data['icon'],
                        is_active=method_data['is_active']
                    )
                    db.session.add(payment_method)
                    added_count += 1
                    print(f"✅ تمت إضافة طريقة الدفع: {method_data['name']}")
                else:
                    print(f"⚠️ طريقة الدفع موجودة مسبقاً: {method_data['name']}")
            
            # حفظ التغييرات
            if added_count > 0:
                db.session.commit()
                print(f"\n🎉 تم إضافة {added_count} طريقة دفع جديدة بنجاح!")
            else:
                print("\n📋 جميع طرق الدفع موجودة مسبقاً.")
            
            # عرض جميع طرق الدفع
            all_methods = PaymentMethod.query.all()
            print(f"\n📊 إجمالي طرق الدفع في النظام: {len(all_methods)}")
            print("=" * 50)
            for method in all_methods:
                status = "نشط" if method.is_active else "غير نشط"
                print(f"• {method.name} ({method.code}) - {status}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة طرق الدفع: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = add_default_payment_methods()
    if success:
        print("\n🚀 تم إعداد طرق الدفع بنجاح!")
    else:
        print("\n❌ فشل في إعداد طرق الدفع.")
    input("اضغط Enter للخروج...")
