#!/usr/bin/env python3
"""
نظام المحاسبة الذكي - SmartBiz Accounting
ملف التشغيل الرئيسي
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل نظام المحاسبة الذكي...")
        print("=" * 50)

        # استيراد وإنشاء التطبيق
        from app import create_app
        app = create_app()

        # إعدادات التشغيل
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5000))
        debug = os.environ.get('DEBUG', 'True').lower() == 'true'

        # معلومات التشغيل
        print("\n" + "=" * 50)
        print("🎉 نظام SmartBiz جاهز!")
        print("🌐 الروابط:")
        print(f"   - المحلي: http://127.0.0.1:{port}")
        print(f"   - الشبكة: http://{host}:{port}")
        print("\n💡 الميزات المتاحة:")
        print("   - 🤖 AI Chat (مساعد ذكي محسن)")
        print("   - 📊 لوحة التحكم")
        print("   - 👥 إدارة العملاء")
        print("   - 📋 إدارة المشاريع")
        print("   - 🧾 الفواتير")
        print("   - 📈 التقارير المالية")
        print("\n⚡ للإيقاف: اضغط Ctrl+C")
        print("=" * 50)

        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
