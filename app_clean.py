#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الأساسي
تطبيق نظيف بدون مساعد ذكي
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, redirect, url_for
from flask_login import current_user

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app():
    """إنشاء تطبيق Flask"""
    
    app = Flask(__name__)
    
    # إعدادات التطبيق
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///smartbiz.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['WTF_CSRF_ENABLED'] = True
    
    # تهيئة الإضافات
    from extensions import db, login_manager, migrate, bcrypt
    
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    bcrypt.init_app(app)
    
    # إعدادات تسجيل الدخول
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from models.user import User
        return User.query.get(int(user_id))
    
    # استيراد النماذج (مطلوب لإنشاء الجداول)
    from models.user import User
    from models.project import Project
    from models.client import Client
    from models.invoice import Invoice
    from models.transaction import Transaction
    from models.payment_method import PaymentMethod
    
    # تسجيل البلوبرنتات
    from blueprints.auth import auth_bp
    from blueprints.dashboard import dashboard_bp
    from blueprints.invoices import invoices_bp
    from blueprints.clients import clients_bp
    from blueprints.projects import projects_bp
    from blueprints.reports import reports_bp
    from blueprints.main import main_bp
    from blueprints.transactions import transactions_bp
    
    app.register_blueprint(auth_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(invoices_bp)
    app.register_blueprint(clients_bp)
    app.register_blueprint(projects_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(transactions_bp)
    
    # الصفحة الرئيسية
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('main.dashboard'))
        return render_template('index.html')
    
    # صفحات الأخطاء
    @app.errorhandler(404)
    def page_not_found(_):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(403)
    def forbidden(_):
        return render_template('errors/403.html'), 403
    
    @app.errorhandler(500)
    def internal_server_error(error):
        logger.error(f"Server error: {error}")
        return render_template('errors/500.html'), 500
    
    # إضافة context processor عام
    @app.context_processor
    def inject_global_vars():
        """إضافة متغيرات عامة لجميع القوالب"""
        notifications_count = 0
        if current_user.is_authenticated:
            try:
                # حساب الإشعارات (فواتير غير مدفوعة، مشاريع قريبة الانتهاء، إلخ)
                from models.invoice import Invoice
                from models.project import Project
                from datetime import datetime, timedelta
                
                # الفواتير غير المدفوعة
                unpaid_invoices = Invoice.query.filter_by(
                    user_id=current_user.id,
                    status='unpaid'
                ).count()
                
                # المشاريع القريبة من الانتهاء (خلال أسبوع)
                next_week = datetime.now() + timedelta(days=7)
                upcoming_projects = Project.query.filter(
                    Project.user_id == current_user.id,
                    Project.end_date <= next_week,
                    Project.status == 'active'
                ).count()
                
                notifications_count = unpaid_invoices + upcoming_projects
                
            except Exception as e:
                logger.error(f"Error calculating notifications: {str(e)}")
                notifications_count = 0
        
        return {
            'current_year': datetime.now().year,
            'notifications_count': notifications_count,
            'app_name': 'SmartBiz Accounting',
            'app_version': '1.0.0'
        }
    
    # إضافة فلاتر Jinja2 المخصصة
    @app.template_filter('format_currency')
    def format_currency(amount):
        """تنسيق العملة"""
        if amount is None:
            return "0.00 ر.س"
        try:
            return f"{float(amount):,.2f} ر.س"
        except (ValueError, TypeError):
            return "0.00 ر.س"
    
    @app.template_filter('format_date')
    def format_date(date):
        """تنسيق التاريخ"""
        if date is None:
            return ""
        try:
            if isinstance(date, str):
                date = datetime.strptime(date, '%Y-%m-%d')
            return date.strftime('%d/%m/%Y')
        except (ValueError, AttributeError):
            return ""
    
    @app.template_filter('format_datetime')
    def format_datetime(datetime_obj):
        """تنسيق التاريخ والوقت"""
        if datetime_obj is None:
            return ""
        try:
            if isinstance(datetime_obj, str):
                datetime_obj = datetime.strptime(datetime_obj, '%Y-%m-%d %H:%M:%S')
            return datetime_obj.strftime('%d/%m/%Y %H:%M')
        except (ValueError, AttributeError):
            return ""
    
    # إنشاء الجداول
    with app.app_context():
        try:
            db.create_all()
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
    
    logger.info("SmartBiz Accounting application created successfully")
    return app

if __name__ == '__main__':
    app = create_app()
    
    print("🚀 بدء تشغيل نظام SmartBiz الأساسي...")
    print("🌐 الروابط:")
    print("   - الصفحة الرئيسية: http://127.0.0.1:5000")
    print("   - لوحة التحكم: http://127.0.0.1:5000/dashboard")
    print("   - العملاء: http://127.0.0.1:5000/clients")
    print("   - الفواتير: http://127.0.0.1:5000/invoices")
    print("   - المشاريع: http://127.0.0.1:5000/projects")
    print("   - التقارير: http://127.0.0.1:5000/reports")
    print("\n🔑 بيانات الدخول الافتراضية:")
    print("   البريد: <EMAIL>")
    print("   كلمة المرور: admin123")
    print("\n⚡ بدء الخادم...")
    
    try:
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
