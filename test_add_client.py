#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار إضافة عميل جديد مباشرة
Test adding a new client directly
"""

import requests
import json

def test_add_client_via_form():
    """اختبار إضافة عميل عبر النموذج"""
    
    try:
        print("🔄 اختبار إضافة عميل عبر النموذج...")
        
        # بيانات العميل
        client_data = {
            'name': 'أحمد محمد علي',
            'email': '<EMAIL>',
            'phone': '+************',
            'address': 'الرياض، المملكة العربية السعودية',
            'company_name': 'شركة الأمل للتجارة',
            'payment_method': 'bank_transfer',
            'notes': 'عميل مهم - يفضل التواصل عبر الهاتف',
            'is_active': 'on'
        }
        
        # إرسال طلب POST
        response = requests.post(
            'http://localhost:5000/clients/add',
            data=client_data,
            allow_redirects=False
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        print(f"📍 العناوين: {dict(response.headers)}")
        
        if response.status_code == 302:
            print("✅ تم إعادة التوجيه - قد يكون العميل تم إضافته بنجاح")
            location = response.headers.get('Location', '')
            print(f"📍 موقع إعادة التوجيه: {location}")
        elif response.status_code == 200:
            print("⚠️ تم إرجاع الصفحة - قد تكون هناك أخطاء في النموذج")
            # البحث عن رسائل الخطأ في HTML
            if 'خطأ' in response.text or 'error' in response.text.lower():
                print("❌ تم العثور على رسائل خطأ في الاستجابة")
            else:
                print("✅ لا توجد رسائل خطأ واضحة")
        else:
            print(f"❌ رمز استجابة غير متوقع: {response.status_code}")
            print(f"📄 محتوى الاستجابة: {response.text[:500]}...")
        
        return response.status_code in [200, 302]
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_add_client_via_api():
    """اختبار إضافة عميل عبر API"""
    
    try:
        print("\n🔄 اختبار إضافة عميل عبر API...")
        
        # بيانات العميل
        client_data = {
            'name': 'سارة أحمد محمد',
            'email': '<EMAIL>',
            'phone': '+966507654321',
            'address': 'جدة، المملكة العربية السعودية',
            'company': 'شركة النور للتقنية',
            'payment_method': 'credit_card',
            'notes': 'عميلة جديدة - تفضل التواصل عبر البريد الإلكتروني'
        }
        
        # إرسال طلب POST إلى API
        response = requests.post(
            'http://localhost:5000/clients/api/add',
            json=client_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 الاستجابة: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('status') == 'success':
                print("✅ تم إضافة العميل بنجاح عبر API")
                return True
            else:
                print(f"❌ فشل في إضافة العميل: {result.get('message', 'خطأ غير معروف')}")
                return False
        else:
            print(f"❌ رمز استجابة غير متوقع: {response.status_code}")
            print(f"📄 محتوى الاستجابة: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

def check_server_status():
    """التحقق من حالة الخادم"""
    
    try:
        print("🔄 التحقق من حالة الخادم...")
        
        response = requests.get('http://localhost:5000/', timeout=5)
        
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"⚠️ الخادم يعمل لكن رمز الاستجابة: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم - تأكد من تشغيل التطبيق")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحقق من الخادم: {e}")
        return False

if __name__ == '__main__':
    print("🚀 بدء اختبار إضافة العملاء...")
    
    # التحقق من حالة الخادم أولاً
    if not check_server_status():
        print("\n❌ يرجى تشغيل الخادم أولاً باستخدام: python app.py")
        input("اضغط Enter للخروج...")
        exit(1)
    
    # اختبار إضافة عميل عبر النموذج
    success1 = test_add_client_via_form()
    
    # اختبار إضافة عميل عبر API
    success2 = test_add_client_via_api()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ وظيفة إضافة العملاء تعمل بشكل صحيح")
    elif success1 or success2:
        print("⚠️ بعض الاختبارات نجحت والأخرى فشلت")
        print("✅ النموذج:" if success1 else "❌ النموذج:", "يعمل" if success1 else "لا يعمل")
        print("✅ API:" if success2 else "❌ API:", "يعمل" if success2 else "لا يعمل")
    else:
        print("❌ جميع الاختبارات فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
