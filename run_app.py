#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل تطبيق SmartBiz الأساسي
Run basic SmartBiz application
"""

import os
import sys

def run_app():
    """تشغيل التطبيق"""
    
    print("🚀 بدء تشغيل نظام SmartBiz الأساسي...")
    
    try:
        # استيراد التطبيق
        from app import create_app
        
        # إنشاء التطبيق
        app = create_app()
        
        # إعدادات التشغيل
        host = '127.0.0.1'
        port = 5000
        debug = True
        
        print("\n" + "=" * 50)
        print("🎉 نظام SmartBiz جاهز!")
        print("🌐 الروابط:")
        print(f"   - الصفحة الرئيسية: http://{host}:{port}")
        print(f"   - لوحة التحكم: http://{host}:{port}/dashboard")
        print(f"   - العملاء: http://{host}:{port}/clients")
        print(f"   - الفواتير: http://{host}:{port}/invoices")
        print(f"   - المشاريع: http://{host}:{port}/projects")
        print(f"   - التقارير: http://{host}:{port}/reports")
        
        print("\n📋 الميزات المتاحة:")
        print("   ✅ إدارة العملاء")
        print("   ✅ إدارة الفواتير")
        print("   ✅ إدارة المشاريع")
        print("   ✅ التقارير المالية")
        print("   ✅ إدارة المعاملات")
        
        print("\n🔑 بيانات الدخول الافتراضية:")
        print("   البريد: <EMAIL>")
        print("   كلمة المرور: admin123")
        
        print("\n" + "=" * 50)
        print("⚡ بدء الخادم...")
        
        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    run_app()
