#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار تكامل Google Gemini AI مع نظام SmartBiz

هذا الملف يختبر تكامل Google Gemini AI مع نظام SmartBiz للتأكد من أن المساعد الذكي
يعمل بشكل صحيح ويمكنه الاستجابة للاستفسارات باللغة العربية.
"""

import os
import sys
import time
import json
import logging
from dotenv import load_dotenv

# محاولة استيراد مكتبة google-generativeai
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("⚠️ مكتبة google-generativeai غير متوفرة. جاري استخدام طريقة الاتصال المباشر...")
    import requests

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("gemini_test")

# تحميل متغيرات البيئة
load_dotenv()

# الحصول على مفتاح API والإعدادات من متغيرات البيئة
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-pro")
GEMINI_MAX_TOKENS = int(os.getenv("GEMINI_MAX_TOKENS", "4000"))
GEMINI_TEMPERATURE = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
GEMINI_TOP_K = int(os.getenv("GEMINI_TOP_K", "40"))
GEMINI_TOP_P = float(os.getenv("GEMINI_TOP_P", "0.95"))

# أسئلة الاختبار باللغة العربية
TEST_QUESTIONS = [
    "ما هو مبدأ الاستحقاق في المحاسبة؟",
    "كيف يمكنني إنشاء فاتورة جديدة في النظام؟",
    "ما هي أفضل الممارسات لإدارة التدفق النقدي؟",
    "اشرح لي كيفية حساب ضريبة القيمة المضافة.",
    "ما هي الفروق بين المحاسبة على أساس الاستحقاق والمحاسبة النقدية؟"
]

def test_with_official_library():
    """اختبار باستخدام المكتبة الرسمية لـ Google Generative AI"""
    if not GEMINI_AVAILABLE:
        logger.warning("المكتبة الرسمية غير متوفرة. تخطي هذا الاختبار.")
        return False
    
    try:
        # تكوين مكتبة Google Generative AI
        genai.configure(api_key=GEMINI_API_KEY)
        
        # إنشاء نموذج
        generation_config = {
            "temperature": GEMINI_TEMPERATURE,
            "max_output_tokens": GEMINI_MAX_TOKENS,
            "top_k": GEMINI_TOP_K,
            "top_p": GEMINI_TOP_P,
        }
        
        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        ]
        
        model = genai.GenerativeModel(
            model_name=GEMINI_MODEL,
            generation_config=generation_config,
            safety_settings=safety_settings
        )
        
        # اختبار الأسئلة
        for i, question in enumerate(TEST_QUESTIONS):
            logger.info(f"\n\n--- اختبار السؤال {i+1} (المكتبة الرسمية) ---")
            logger.info(f"السؤال: {question}")
            
            # إنشاء المطالبة
            prompt = f"""أنت مساعد محاسبة ذكي لنظام SmartBiz. أجب على السؤال التالي باللغة العربية بشكل مفصل ومفيد:

{question}

قدم إجابة عملية ومفيدة. استخدم تنسيق Markdown للإجابة."""
            
            # الحصول على الاستجابة
            start_time = time.time()
            response = model.generate_content(prompt)
            end_time = time.time()
            
            # عرض الاستجابة
            logger.info(f"الإجابة: {response.text}")
            logger.info(f"وقت الاستجابة: {end_time - start_time:.2f} ثانية")
            
            # إضافة تأخير قصير بين الطلبات
            time.sleep(1)
        
        return True
    
    except Exception as e:
        logger.error(f"خطأ في اختبار المكتبة الرسمية: {str(e)}")
        return False

def test_with_direct_api():
    """اختبار باستخدام طلبات API المباشرة"""
    try:
        # إعداد عنوان URL للطلب
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
        
        # اختبار الأسئلة
        for i, question in enumerate(TEST_QUESTIONS):
            logger.info(f"\n\n--- اختبار السؤال {i+1} (API المباشر) ---")
            logger.info(f"السؤال: {question}")
            
            # إنشاء المطالبة
            prompt = f"""أنت مساعد محاسبة ذكي لنظام SmartBiz. أجب على السؤال التالي باللغة العربية بشكل مفصل ومفيد:

{question}

قدم إجابة عملية ومفيدة. استخدم تنسيق Markdown للإجابة."""
            
            # إعداد بيانات الطلب
            payload = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": GEMINI_TEMPERATURE,
                    "maxOutputTokens": GEMINI_MAX_TOKENS,
                    "topK": GEMINI_TOP_K,
                    "topP": GEMINI_TOP_P
                },
                "safetySettings": [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}
                ]
            }
            
            # إرسال الطلب
            start_time = time.time()
            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            end_time = time.time()
            
            # معالجة الاستجابة
            if response.status_code == 200:
                response_json = response.json()
                if "candidates" in response_json and len(response_json["candidates"]) > 0:
                    content = response_json["candidates"][0]["content"]
                    if "parts" in content and len(content["parts"]) > 0:
                        text = content["parts"][0]["text"]
                        logger.info(f"الإجابة: {text}")
                    else:
                        logger.error("لم يتم العثور على نص في الاستجابة")
                else:
                    logger.error("لم يتم العثور على مرشحين في الاستجابة")
            else:
                logger.error(f"فشل الطلب: {response.status_code} - {response.text}")
            
            logger.info(f"وقت الاستجابة: {end_time - start_time:.2f} ثانية")
            
            # إضافة تأخير قصير بين الطلبات
            time.sleep(1)
        
        return True
    
    except Exception as e:
        logger.error(f"خطأ في اختبار API المباشر: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("=== بدء اختبار تكامل Google Gemini AI ===\n")
    
    # التحقق من وجود مفتاح API
    if not GEMINI_API_KEY:
        logger.error("مفتاح Google Gemini API غير موجود. يرجى إضافته إلى ملف .env")
        return
    
    logger.info(f"استخدام النموذج: {GEMINI_MODEL}")
    logger.info(f"الحد الأقصى للرموز: {GEMINI_MAX_TOKENS}")
    logger.info(f"درجة الحرارة: {GEMINI_TEMPERATURE}")
    logger.info(f"Top-K: {GEMINI_TOP_K}")
    logger.info(f"Top-P: {GEMINI_TOP_P}\n")
    
    # اختبار باستخدام المكتبة الرسمية
    if GEMINI_AVAILABLE:
        logger.info("اختبار باستخدام المكتبة الرسمية لـ Google Generative AI...")
        official_success = test_with_official_library()
    else:
        official_success = False
    
    # اختبار باستخدام طلبات API المباشرة
    logger.info("\n\nاختبار باستخدام طلبات API المباشرة...")
    direct_success = test_with_direct_api()
    
    # تلخيص النتائج
    logger.info("\n\n=== نتائج الاختبار ===")
    if GEMINI_AVAILABLE:
        logger.info(f"المكتبة الرسمية: {'✅ نجاح' if official_success else '❌ فشل'}")
    else:
        logger.info("المكتبة الرسمية: ⚠️ غير متوفرة")
    
    logger.info(f"API المباشر: {'✅ نجاح' if direct_success else '❌ فشل'}")
    
    if (GEMINI_AVAILABLE and official_success) or direct_success:
        logger.info("\n✅ تم اختبار تكامل Google Gemini AI بنجاح!")
    else:
        logger.info("\n❌ فشل اختبار تكامل Google Gemini AI. يرجى التحقق من الأخطاء أعلاه.")

if __name__ == "__main__":
    main()