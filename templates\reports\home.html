{% extends "base.html" %}

{% block title %}التقارير والتحليلات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">التقارير والتحليلات</h1>
            <p class="text-muted">تحليل شامل لأداء أعمالك</p>
        </div>
        <div>
            <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>تصدير التقارير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ date_from or '' }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ date_to or '' }}">
                </div>
                <div class="col-md-3">
                    <label for="report_type" class="form-label">نوع التقرير</label>
                    <select class="form-select" id="report_type" name="report_type">
                        <option value="all" {% if report_type == 'all' %}selected{% endif %}>جميع التقارير</option>
                        <option value="sales" {% if report_type == 'sales' %}selected{% endif %}>تقرير المبيعات</option>
                        <option value="clients" {% if report_type == 'clients' %}selected{% endif %}>تقرير العملاء</option>
                        <option value="projects" {% if report_type == 'projects' %}selected{% endif %}>تقرير المشاريع</option>
                        <option value="financial" {% if report_type == 'financial' %}selected{% endif %}>التقرير المالي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المبيعات</h6>
                            <h4 class="mb-0">{{ total_sales|format_currency }}</h4>
                            <small>
                                {% if sales_growth >= 0 %}
                                <i class="fas fa-arrow-up"></i> +{{ sales_growth }}%
                                {% else %}
                                <i class="fas fa-arrow-down"></i> {{ sales_growth }}%
                                {% endif %}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الفواتير المدفوعة</h6>
                            <h4 class="mb-0">{{ paid_invoices_count or 0 }}</h4>
                            <small>{{ paid_amount|format_currency }}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الفواتير المعلقة</h6>
                            <h4 class="mb-0">{{ pending_invoices_count or 0 }}</h4>
                            <small>{{ pending_amount|format_currency }}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">العملاء النشطون</h6>
                            <h4 class="mb-0">{{ active_clients_count or 0 }}</h4>
                            <small>من أصل {{ total_clients_count or 0 }}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Sales Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>تطور المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Invoice Status Chart -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>حالة الفواتير
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="invoiceStatusChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="row">
        <!-- Top Clients -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>أفضل العملاء
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if top_clients %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>العميل</th>
                                    <th>عدد الفواتير</th>
                                    <th>إجمالي المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in top_clients %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                 style="width: 32px; height: 32px;">
                                                <span class="text-white fw-bold">{{ client.name[0].upper() }}</span>
                                            </div>
                                            <span>{{ client.name }}</span>
                                        </div>
                                    </td>
                                    <td>{{ client.invoices_count }}</td>
                                    <td>{{ client.total_amount|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-2x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات عملاء</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="text-decoration-none">
                                            #{{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.client_name or 'غير محدد' }}</td>
                                    <td>{{ invoice.total_amount|format_currency }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% elif invoice.status == 'unpaid' %}
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        {% elif invoice.status == 'partially_paid' %}
                                            <span class="badge bg-warning">مدفوعة جزئياً</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد فواتير</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Performance -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>الأداء الشهري
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyPerformanceChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: {{ sales_labels|safe if sales_labels else "[]" }},
        datasets: [{
            label: 'المبيعات',
            data: {{ sales_data|safe if sales_data else "[]" }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString('ar-SA') + ' ر.س';
                    }
                }
            }
        }
    }
});

// Invoice Status Chart
const statusCtx = document.getElementById('invoiceStatusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['مدفوعة', 'غير مدفوعة', 'مدفوعة جزئياً', 'متأخرة'],
        datasets: [{
            data: [
                {{ paid_invoices_count or 0 }},
                {{ unpaid_invoices_count or 0 }},
                {{ partially_paid_invoices_count or 0 }},
                {{ overdue_invoices_count or 0 }}
            ],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Monthly Performance Chart
const monthlyCtx = document.getElementById('monthlyPerformanceChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: {{ monthly_labels|safe if monthly_labels else "[]" }},
        datasets: [{
            label: 'الإيرادات',
            data: {{ monthly_revenue|safe if monthly_revenue else "[]" }},
            backgroundColor: '#007bff',
            borderColor: '#0056b3',
            borderWidth: 1
        }, {
            label: 'المصروفات',
            data: {{ monthly_expenses|safe if monthly_expenses else "[]" }},
            backgroundColor: '#dc3545',
            borderColor: '#c82333',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString('ar-SA') + ' ر.س';
                    }
                }
            }
        }
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    const url = `/reports/export?${params.toString()}`;
    window.open(url, '_blank');
}

// Auto-refresh charts when date filter changes
document.addEventListener('DOMContentLoaded', function() {
    const dateInputs = document.querySelectorAll('#date_from, #date_to, #report_type');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Auto-submit form after a short delay
            setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    });
});
</script>
{% endblock %}
