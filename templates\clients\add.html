{% extends "base.html" %}

{% block title %}{% if client %}تعديل {{ client.name }}{% else %}إضافة عميل جديد{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% if client %}تعديل {{ client.name }}{% else %}إضافة عميل جديد{% endif %}</h1>
            <p class="text-muted">{% if client %}تحديث بيانات العميل{% else %}إضافة عميل جديد إلى قاعدة البيانات{% endif
                %}</p>
        </div>
        <div>
            {% if client %}
            <a href="{{ url_for('clients.view_client', client_id=client.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى العميل
            </a>
            {% else %}
            <a href="{{ url_for('clients.list_clients') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
            </a>
            {% endif %}
        </div>
    </div>

    <form method="POST" enctype="multipart/form-data">
        <div class="row">
            <!-- Main Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name"
                                    value="{{ client.name if client else '' }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email"
                                    value="{{ client.email if client else '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                    value="{{ client.phone if client else '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name"
                                    value="{{ client.company_name if client else '' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>معلومات العنوان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                    placeholder="العنوان التفصيلي">{{ client.address if client else '' }}</textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city"
                                    value="{{ client.city if client else '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">الدولة</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="">اختر الدولة</option>
                                    <option value="SA" selected>المملكة العربية السعودية</option>
                                    <option value="AE">الإمارات العربية المتحدة</option>
                                    <option value="KW">الكويت</option>
                                    <option value="QA">قطر</option>
                                    <option value="BH">البحرين</option>
                                    <option value="OM">عمان</option>
                                    <option value="JO">الأردن</option>
                                    <option value="LB">لبنان</option>
                                    <option value="EG">مصر</option>
                                    <option value="MA">المغرب</option>
                                    <option value="TN">تونس</option>
                                    <option value="DZ">الجزائر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>طريقة الدفع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع المفضلة</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقدي</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="digital_wallet">محفظة رقمية</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                    placeholder="ملاحظات إضافية عن العميل"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Profile Image -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-image me-2"></i>صورة العميل
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div id="imagePreview" class="mb-3">
                                <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center"
                                    style="width: 120px; height: 120px;">
                                    <i class="fas fa-user fa-3x text-muted"></i>
                                </div>
                            </div>
                            <input type="file" class="form-control" id="profile_image" name="profile_image"
                                accept="image/*" onchange="previewImage(this)">
                            <small class="form-text text-muted">اختر صورة للعميل (اختياري)</small>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-toggle-on me-2"></i>الحالة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                عميل نشط
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            العملاء النشطون يظهرون في قوائم الاختيار عند إنشاء الفواتير
                        </small>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ العميل
                            </button>
                            <a href="{{ url_for('clients.list_clients') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" class="rounded-circle" 
                     style="width: 120px; height: 120px; object-fit: cover;" 
                     alt="معاينة الصورة">
            `;
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = `
            <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                 style="width: 120px; height: 120px;">
                <i class="fas fa-user fa-3x text-muted"></i>
            </div>
        `;
    }
}

// Helper functions for error handling
function showError(input, message) {
    let errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        input.parentNode.appendChild(errorDiv);
    }
    errorDiv.textContent = message;
}

function hideError(input) {
    const errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;

        // Validate name (required)
        if (!nameInput.value.trim()) {
            isValid = false;
            nameInput.classList.add('is-invalid');
            showError(nameInput, 'اسم العميل مطلوب');
        } else {
            nameInput.classList.remove('is-invalid');
            hideError(nameInput);
        }

        // Validate email (optional but must be valid if provided)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailInput.value.trim() && !emailRegex.test(emailInput.value)) {
            isValid = false;
            emailInput.classList.add('is-invalid');
            showError(emailInput, 'يرجى إدخال بريد إلكتروني صحيح');
        } else {
            emailInput.classList.remove('is-invalid');
            hideError(emailInput);
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء في النموذج');
        }
    });
    
    // Real-time validation
    nameInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('is-invalid');
        }
    });
    
    emailInput.addEventListener('input', function() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!this.value.trim() || emailRegex.test(this.value)) {
            this.classList.remove('is-invalid');
            hideError(this);
        }
    });
});
</script>
{% endblock %}