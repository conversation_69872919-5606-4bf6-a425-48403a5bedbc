#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إصلاح شامل وتشغيل نظام SmartBiz
"""

import os
import sys
import subprocess

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_requirements():
    """التحقق من المتطلبات"""
    print("📦 التحقق من المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'flask-login',
        'flask-migrate',
        'flask-cors',
        'flask-mail',
        'flask-bcrypt',
        'flask-wtf'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_packages)}")
        print("تثبيت المكتبات المفقودة...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ تم تثبيت المكتبات المفقودة")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المكتبات")
            return False
    
    return True

def create_minimal_app():
    """إنشاء تطبيق مبسط"""
    print("\n🔧 إنشاء تطبيق مبسط...")
    
    try:
        from flask import Flask, render_template, redirect, url_for, flash, request
        from flask_sqlalchemy import SQLAlchemy
        from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
        from werkzeug.security import generate_password_hash, check_password_hash
        from datetime import datetime
        
        # إنشاء التطبيق
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'dev-secret-key'
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smartbiz_simple.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        # إنشاء قاعدة البيانات
        db = SQLAlchemy(app)
        
        # إعداد تسجيل الدخول
        login_manager = LoginManager()
        login_manager.init_app(app)
        login_manager.login_view = 'login'
        login_manager.login_message = 'يرجى تسجيل الدخول'
        
        # نموذج المستخدم البسيط
        class User(UserMixin, db.Model):
            id = db.Column(db.Integer, primary_key=True)
            username = db.Column(db.String(80), unique=True, nullable=False)
            email = db.Column(db.String(120), unique=True, nullable=False)
            password_hash = db.Column(db.String(120), nullable=False)
            created_at = db.Column(db.DateTime, default=datetime.utcnow)
            
            def set_password(self, password):
                self.password_hash = generate_password_hash(password)
            
            def check_password(self, password):
                return check_password_hash(self.password_hash, password)
        
        # نموذج العميل البسيط
        class Client(db.Model):
            id = db.Column(db.Integer, primary_key=True)
            name = db.Column(db.String(100), nullable=False)
            email = db.Column(db.String(120))
            phone = db.Column(db.String(20))
            created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        @login_manager.user_loader
        def load_user(user_id):
            return User.query.get(int(user_id))
        
        # الصفحة الرئيسية
        @app.route('/')
        def index():
            if current_user.is_authenticated:
                return redirect(url_for('dashboard'))
            return '''
            <h1>مرحباً بك في نظام SmartBiz</h1>
            <a href="/login">تسجيل الدخول</a> | 
            <a href="/register">إنشاء حساب</a>
            '''
        
        # تسجيل الدخول
        @app.route('/login', methods=['GET', 'POST'])
        def login():
            if request.method == 'POST':
                username = request.form['username']
                password = request.form['password']
                user = User.query.filter_by(username=username).first()
                
                if user and user.check_password(password):
                    login_user(user)
                    return redirect(url_for('dashboard'))
                else:
                    flash('اسم المستخدم أو كلمة المرور غير صحيحة')
            
            return '''
            <form method="post">
                <h2>تسجيل الدخول</h2>
                <p>اسم المستخدم: <input type="text" name="username" required></p>
                <p>كلمة المرور: <input type="password" name="password" required></p>
                <p><input type="submit" value="دخول"></p>
            </form>
            <a href="/register">إنشاء حساب جديد</a>
            '''
        
        # إنشاء حساب
        @app.route('/register', methods=['GET', 'POST'])
        def register():
            if request.method == 'POST':
                username = request.form['username']
                email = request.form['email']
                password = request.form['password']
                
                if User.query.filter_by(username=username).first():
                    flash('اسم المستخدم موجود مسبقاً')
                elif User.query.filter_by(email=email).first():
                    flash('البريد الإلكتروني موجود مسبقاً')
                else:
                    user = User(username=username, email=email)
                    user.set_password(password)
                    db.session.add(user)
                    db.session.commit()
                    login_user(user)
                    return redirect(url_for('dashboard'))
            
            return '''
            <form method="post">
                <h2>إنشاء حساب جديد</h2>
                <p>اسم المستخدم: <input type="text" name="username" required></p>
                <p>البريد الإلكتروني: <input type="email" name="email" required></p>
                <p>كلمة المرور: <input type="password" name="password" required></p>
                <p><input type="submit" value="إنشاء حساب"></p>
            </form>
            <a href="/login">تسجيل الدخول</a>
            '''
        
        # لوحة التحكم
        @app.route('/dashboard')
        @login_required
        def dashboard():
            clients_count = Client.query.count()
            return f'''
            <h1>مرحباً {current_user.username}</h1>
            <h2>لوحة التحكم</h2>
            <p>عدد العملاء: {clients_count}</p>
            <ul>
                <li><a href="/clients">إدارة العملاء</a></li>
                <li><a href="/logout">تسجيل الخروج</a></li>
            </ul>
            '''
        
        # إدارة العملاء
        @app.route('/clients')
        @login_required
        def clients():
            clients = Client.query.all()
            clients_html = '<ul>'
            for client in clients:
                clients_html += f'<li>{client.name} - {client.email}</li>'
            clients_html += '</ul>'
            
            return f'''
            <h1>إدارة العملاء</h1>
            {clients_html}
            <h3>إضافة عميل جديد</h3>
            <form method="post" action="/add_client">
                <p>الاسم: <input type="text" name="name" required></p>
                <p>البريد: <input type="email" name="email"></p>
                <p>الهاتف: <input type="text" name="phone"></p>
                <p><input type="submit" value="إضافة"></p>
            </form>
            <a href="/dashboard">العودة للوحة التحكم</a>
            '''
        
        # إضافة عميل
        @app.route('/add_client', methods=['POST'])
        @login_required
        def add_client():
            name = request.form['name']
            email = request.form['email']
            phone = request.form['phone']
            
            client = Client(name=name, email=email, phone=phone)
            db.session.add(client)
            db.session.commit()
            
            return redirect(url_for('clients'))
        
        # تسجيل الخروج
        @app.route('/logout')
        @login_required
        def logout():
            logout_user()
            return redirect(url_for('index'))
        
        # إنشاء الجداول
        with app.app_context():
            db.create_all()
            
            # إنشاء مستخدم افتراضي
            if not User.query.filter_by(username='admin').first():
                admin = User(username='admin', email='<EMAIL>')
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم افتراضي: admin / admin123")
        
        print("✅ تم إنشاء التطبيق المبسط بنجاح")
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 إصلاح وتشغيل نظام SmartBiz...")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء التطبيق المبسط
    app = create_minimal_app()
    if not app:
        print("❌ فشل في إنشاء التطبيق")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل الخادم
    try:
        print("\n" + "=" * 50)
        print("🎉 نظام SmartBiz جاهز!")
        print("🌐 الروابط:")
        print("   - الصفحة الرئيسية: http://127.0.0.1:5000")
        print("   - لوحة التحكم: http://127.0.0.1:5000/dashboard")
        print("   - العملاء: http://127.0.0.1:5000/clients")
        print("\n🔑 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n⏹️ اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(host='127.0.0.1', port=5000, debug=True)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
