# SmartBiz Accounting - Utility Modules
# نظام المحاسبة الذكي - وحدات المساعدة

This directory contains utility modules that provide various helper functions for the SmartBiz Accounting system.

## Available Modules

### PDF Generator (`pdf_generator.py`)
Provides functionality for generating PDF documents, particularly invoices and reports.
- Supports Arabic text rendering
- Includes company logo and styling
- Creates professional invoice PDFs
- Generates report PDFs with charts and tables

### Email Sender (`email_sender.py`)
Handles all email communication from the system.
- Sends invoice reminders
- Delivers password reset emails
- Supports HTML email templates
- Handles attachments

### Export Helper (`export_helper.py`)
Facilitates data export to various formats.
- Exports to CSV and Excel formats
- Handles Arabic text in exports
- Formats data appropriately for different report types

### Analytics (`analytics.py`)
Provides data analysis and visualization capabilities.
- Generates various charts (financial, invoice, client, project)
- Calculates financial metrics
- Creates time series visualizations

### OCR Processor (`ocr_processor.py`)
Handles optical character recognition for document processing.
- Extracts text from images and PDFs
- Supports Arabic and English text recognition
- Extracts structured data from invoices and receipts
- Detects document types

### NLP Processor (`nlp_processor.py`)
Provides natural language processing capabilities.
- Analyzes text sentiment
- Extracts keywords and entities
- Categorizes text
- Generates smart recommendations
- Analyzes invoice text

### Voice Processor (`voice_processor.py`)
Handles speech recognition and text-to-speech functionality.
- Recognizes speech from microphone or audio files
- Converts text to speech
- Creates voice notes
- Processes voice commands

## Usage

All modules are imported in the `__init__.py` file and can be accessed through the `utils` package:

```python
from utils import PDFGenerator, EmailSender, ExportHelper

# Create a PDF
pdf_gen = PDFGenerator()
pdf_file = pdf_gen.create_invoice_pdf(invoice_data)

# Send an email
email_sender = EmailSender()
email_sender.send_invoice_reminder(invoice, client)

# Export data
ExportHelper.export_to_excel(data, 'report.xlsx')
```

## Dependencies

These utility modules depend on various Python libraries listed in the project's `requirements.txt` file, including:

- PyPDF2 (PDF processing)
- openpyxl, xlrd (Excel handling)
- pandas (Data manipulation)
- plotly, matplotlib (Data visualization)
- easyocr, pytesseract (OCR)
- spacy, scikit-learn, transformers (NLP)
- SpeechRecognition, pyttsx3 (Voice processing)

## Error Handling

All modules implement graceful error handling with appropriate fallbacks when dependencies are missing or operations fail.