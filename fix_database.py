#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إصلاح قاعدة البيانات وإضافة الحقول المفقودة
Fix database and add missing fields
"""

from app import create_app
from extensions import db
import sqlite3
import os

def fix_clients_table():
    """إصلاح جدول العملاء"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 إصلاح جدول العملاء...")
            
            # الحصول على مسار قاعدة البيانات
            db_path = app.config.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///smartbiz.db')
            if db_path.startswith('sqlite:///'):
                db_path = db_path.replace('sqlite:///', '')
            
            print(f"📍 مسار قاعدة البيانات: {db_path}")
            
            # الاتصال المباشر بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من بنية الجدول الحالية
            cursor.execute("PRAGMA table_info(clients)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            print("📋 الأعمدة الحالية:")
            for col in columns:
                print(f"  - {col[1]}: {col[2]}")
            
            # إضافة الأعمدة المفقودة
            missing_columns = []
            
            if 'company_name' not in column_names:
                missing_columns.append(('company_name', 'VARCHAR(100)'))
            
            if 'last_contact' not in column_names:
                missing_columns.append(('last_contact', 'DATETIME'))
            
            # إضافة الأعمدة المفقودة
            for col_name, col_type in missing_columns:
                try:
                    cursor.execute(f"ALTER TABLE clients ADD COLUMN {col_name} {col_type}")
                    print(f"✅ تمت إضافة العمود: {col_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" in str(e):
                        print(f"⚠️ العمود {col_name} موجود مسبقاً")
                    else:
                        print(f"❌ خطأ في إضافة العمود {col_name}: {e}")
            
            # تحديث نوع عمود payment_method إذا لزم الأمر
            try:
                # إنشاء جدول مؤقت بالبنية الجديدة
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS clients_new (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(120),
                    phone VARCHAR(20),
                    address TEXT,
                    company_name VARCHAR(100),
                    tax_number VARCHAR(50),
                    contact_person VARCHAR(100),
                    payment_method VARCHAR(50),
                    payment_method_id INTEGER,
                    notes TEXT,
                    created_at DATETIME,
                    updated_at DATETIME,
                    last_contact DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    user_id INTEGER NOT NULL,
                    FOREIGN KEY (payment_method_id) REFERENCES payment_methods (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
                """)
                
                # نسخ البيانات من الجدول القديم
                cursor.execute("""
                INSERT INTO clients_new (
                    id, name, email, phone, address, company_name, tax_number, 
                    contact_person, payment_method, payment_method_id, notes, 
                    created_at, updated_at, last_contact, is_active, user_id
                )
                SELECT 
                    id, name, email, phone, address, 
                    COALESCE(company_name, '') as company_name,
                    tax_number, contact_person, payment_method, payment_method_id, 
                    notes, created_at, updated_at, 
                    COALESCE(last_contact, created_at) as last_contact,
                    COALESCE(is_active, 1) as is_active,
                    user_id
                FROM clients
                """)
                
                # حذف الجدول القديم وإعادة تسمية الجديد
                cursor.execute("DROP TABLE clients")
                cursor.execute("ALTER TABLE clients_new RENAME TO clients")
                
                print("✅ تم تحديث بنية جدول العملاء بنجاح")
                
            except Exception as e:
                print(f"⚠️ تحذير في تحديث البنية: {e}")
            
            # حفظ التغييرات
            conn.commit()
            conn.close()
            
            print("✅ تم إصلاح جدول العملاء بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح الجدول: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_client_creation_after_fix():
    """اختبار إنشاء عميل بعد الإصلاح"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔄 اختبار إنشاء عميل جديد...")
            
            from models.client import Client
            from models.user import User
            
            # التحقق من وجود مستخدم
            user = User.query.first()
            if not user:
                print("❌ لا يوجد مستخدمين في النظام")
                return False
            
            # إنشاء عميل تجريبي
            test_client = Client(
                user_id=user.id,
                name='عميل تجريبي',
                email='<EMAIL>',
                phone='+966501234567',
                company_name='شركة تجريبية',
                payment_method='cash',
                is_active=True
            )
            
            db.session.add(test_client)
            db.session.commit()
            
            print(f"✅ تم إنشاء العميل التجريبي بنجاح! ID: {test_client.id}")
            
            # حذف العميل التجريبي
            db.session.delete(test_client)
            db.session.commit()
            
            print("✅ تم حذف العميل التجريبي")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار إنشاء العميل: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 بدء إصلاح قاعدة البيانات...")
    
    success1 = fix_clients_table()
    success2 = test_client_creation_after_fix()
    
    if success1 and success2:
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("✅ يمكن الآن إضافة العملاء بدون مشاكل")
    else:
        print("\n❌ حدثت مشاكل أثناء الإصلاح")
    
    input("\nاضغط Enter للخروج...")
