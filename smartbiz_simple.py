#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام SmartBiz المبسط - يعمل بدون مشاكل
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'smartbiz-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smartbiz_working.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نماذج البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    company = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'))
    amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# قوالب HTML
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - SmartBiz</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="email"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .btn { background: #007bff; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .alert { padding: 10px; margin-bottom: 20px; border-radius: 5px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .link { text-align: center; margin-top: 20px; }
        .link a { color: #007bff; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 SmartBiz</h1>
        <h2>تسجيل الدخول</h2>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="post">
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit" class="btn">دخول</button>
        </form>
        
        <div class="link">
            <a href="{{ url_for('register') }}">إنشاء حساب جديد</a>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - SmartBiz</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 0; }
        .header { background: #007bff; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .nav { background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; }
        .nav a { display: inline-block; margin-left: 20px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .nav a:hover { background: #0056b3; }
        .btn { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 SmartBiz - نظام المحاسبة</h1>
        <div>
            مرحباً {{ username }} | 
            <a href="{{ url_for('logout') }}" style="color: white;">خروج</a>
        </div>
    </div>
    
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('dashboard') }}">لوحة التحكم</a>
            <a href="{{ url_for('clients') }}">العملاء</a>
            <a href="{{ url_for('invoices') }}">الفواتير</a>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ clients_count }}</div>
                <div>إجمالي العملاء</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ invoices_count }}</div>
                <div>إجمالي الفواتير</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_amount }} ر.س</div>
                <div>إجمالي المبيعات</div>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 10px;">
            <h3>مرحباً بك في نظام SmartBiz للمحاسبة</h3>
            <p>يمكنك الآن إدارة عملائك وفواتيرك بسهولة من خلال النظام.</p>
            <a href="{{ url_for('clients') }}" class="btn">إدارة العملاء</a>
            <a href="{{ url_for('invoices') }}" class="btn">إدارة الفواتير</a>
        </div>
    </div>
</body>
</html>
'''

# دوال مساعدة
def is_logged_in():
    return 'user_id' in session

def require_login():
    if not is_logged_in():
        return redirect(url_for('login'))
    return None

# المسارات
@app.route('/')
def index():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            session['user_id'] = user.id
            session['username'] = user.username
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود مسبقاً')
        elif User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود مسبقاً')
        else:
            user = User(username=username, email=email)
            user.set_password(password)
            db.session.add(user)
            db.session.commit()
            
            session['user_id'] = user.id
            session['username'] = user.username
            return redirect(url_for('dashboard'))
    
    return render_template_string(LOGIN_TEMPLATE.replace('تسجيل الدخول', 'إنشاء حساب جديد').replace('دخول', 'إنشاء حساب'))

@app.route('/dashboard')
def dashboard():
    redirect_response = require_login()
    if redirect_response:
        return redirect_response
    
    clients_count = Client.query.count()
    invoices_count = Invoice.query.count()
    total_amount = db.session.query(db.func.sum(Invoice.amount)).scalar() or 0
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                username=session['username'],
                                clients_count=clients_count,
                                invoices_count=invoices_count,
                                total_amount=f"{total_amount:,.2f}")

@app.route('/clients')
def clients():
    redirect_response = require_login()
    if redirect_response:
        return redirect_response
    
    clients = Client.query.all()
    return f"<h1>العملاء ({len(clients)})</h1><a href='/dashboard'>العودة</a>"

@app.route('/invoices')
def invoices():
    redirect_response = require_login()
    if redirect_response:
        return redirect_response
    
    invoices = Invoice.query.all()
    return f"<h1>الفواتير ({len(invoices)})</h1><a href='/dashboard'>العودة</a>"

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# إنشاء قاعدة البيانات والبيانات الافتراضية
def init_db():
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin', email='<EMAIL>')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء مستخدم افتراضي: admin / admin123")

if __name__ == '__main__':
    print("🚀 تشغيل نظام SmartBiz المبسط...")
    
    # تهيئة قاعدة البيانات
    init_db()
    
    print("=" * 50)
    print("🎉 نظام SmartBiz جاهز!")
    print("🌐 الرابط: http://127.0.0.1:5000")
    print("🔑 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
