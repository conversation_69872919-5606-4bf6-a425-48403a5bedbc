#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي
نموذج المعاملة المالية
"""

from datetime import datetime
from app import db

class Transaction(db.Model):
    """
نموذج المعاملة المالية - يخزن معلومات المعاملات المالية (الدخل والمصروفات)
    """
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    amount = db.Column(db.Float, nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # income, expense
    category = db.Column(db.String(50))  # فئة المعاملة (مبيعات، رواتب، إيجار، إلخ)
    description = db.Column(db.Text)
    payment_method = db.Column(db.String(50))  # طريقة الدفع (نقدًا، بطاقة ائتمان، تحويل بنكي، إلخ)
    reference_number = db.Column(db.String(50))  # رقم مرجعي (رقم الشيك، رقم التحويل، إلخ)
    receipt_image = db.Column(db.String(200))  # مسار صورة الإيصال
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    def update_invoice_status(self):
        """
تحديث حالة الفاتورة المرتبطة بالمعاملة
        """
        if self.invoice_id and self.transaction_type == 'income':
            from models.invoice import Invoice
            invoice = Invoice.query.get(self.invoice_id)
            if invoice:
                invoice.update_status()

    def to_dict(self):
        """
تحويل بيانات المعاملة إلى قاموس
        """
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'amount': self.amount,
            'transaction_type': self.transaction_type,
            'category': self.category,
            'description': self.description,
            'payment_method': self.payment_method,
            'reference_number': self.reference_number,
            'receipt_image': self.receipt_image,
            'notes': self.notes,
            'client_id': self.client_id,
            'project_id': self.project_id,
            'invoice_id': self.invoice_id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Transaction {self.id}: {self.amount} ({self.transaction_type})>'