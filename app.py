#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SmartBiz Accounting - نظام المحاسبة الذكي

تطبيق ويب للمحاسبة الذكية مع وكيل ذكاء اصطناعي متكامل
"""

from flask import Flask, render_template, redirect, url_for
from flask_login import current_user
from dotenv import load_dotenv
import logging

# استيراد الإضافات
from extensions import db, init_extensions
from config.config import get_config

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.FileHandler("app.log"),
                              logging.StreamHandler()])
logger = logging.getLogger(__name__)

def create_app():
    """
    إنشاء وتكوين تطبيق Flask
    """
    # إنشاء تطبيق Flask
    app = Flask(__name__,
                static_folder='static',
                template_folder='templates')

    # تحميل الإعدادات
    config = get_config()
    app.config.from_object(config)

    # تهيئة الإضافات
    init_extensions(app)

    # استيراد النماذج (بعد إنشاء db) - مطلوب لتهيئة قاعدة البيانات
    from models.user import User
    from models.project import Project
    from models.client import Client
    from models.invoice import Invoice
    from models.transaction import Transaction
    from models.payment_method import PaymentMethod

    # تسجيل البلوبرنتات
    from blueprints.auth import auth_bp
    from blueprints.dashboard import dashboard_bp
    from blueprints.invoices import invoices_bp
    from blueprints.clients import clients_bp
    from blueprints.projects import projects_bp
    from blueprints.reports import reports_bp
    from blueprints.main import main_bp
    from blueprints.transactions import transactions_bp
    from blueprints.ai_agent import ai_agent_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(invoices_bp)
    app.register_blueprint(clients_bp)
    app.register_blueprint(projects_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(transactions_bp)
    app.register_blueprint(ai_agent_bp)

    # الصفحة الرئيسية
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('main.dashboard'))
        return render_template('index.html')

    # صفحة الخطأ 404
    @app.errorhandler(404)
    def page_not_found(_):
        return render_template('errors/404.html'), 404

    # صفحة الخطأ 403
    @app.errorhandler(403)
    def forbidden(_):
        return render_template('errors/403.html'), 403

    # صفحة الخطأ 500
    @app.errorhandler(500)
    def internal_server_error(error):
        logger.error(f"Server error: {error}")
        return render_template('errors/500.html'), 500

    # إضافة context processor عام
    @app.context_processor
    def inject_global_vars():
        """إضافة متغيرات عامة لجميع القوالب"""
        notifications_count = 0
        if current_user.is_authenticated:
            try:
                from datetime import datetime, timedelta
                from models.invoice import Invoice
                from sqlalchemy import and_

                today = datetime.now().date()
                upcoming_due_date = today + timedelta(days=7)

                # حساب الفواتير المستحقة قريباً
                upcoming_invoices_count = Invoice.query.filter(
                    and_(
                        Invoice.user_id == current_user.id,
                        Invoice.status == 'unpaid',  # type: ignore
                        Invoice.due_date <= upcoming_due_date,
                        Invoice.due_date >= today
                    )
                ).count()

                # حساب الفواتير المتأخرة
                overdue_invoices_count = Invoice.query.filter(
                    and_(
                        Invoice.user_id == current_user.id,
                        Invoice.status == 'unpaid',  # type: ignore
                        Invoice.due_date < today
                    )
                ).count()

                notifications_count = upcoming_invoices_count + overdue_invoices_count
            except Exception:
                notifications_count = 0

        return dict(notifications_count=notifications_count)

    # إضافة فلاتر Jinja2 المخصصة
    @app.template_filter('format_currency')
    def format_currency(amount):
        """تنسيق العملة"""
        if amount is None:
            return "0.00 ر.س"
        try:
            return f"{float(amount):,.2f} ر.س"
        except (ValueError, TypeError):
            return "0.00 ر.س"

    @app.template_filter('format_date')
    def format_date(date):
        """تنسيق التاريخ"""
        if date is None:
            return ""
        try:
            if hasattr(date, 'strftime'):
                return date.strftime('%Y-%m-%d')
            return str(date)
        except:
            return ""

    @app.template_filter('format_datetime')
    def format_datetime(datetime_obj):
        """تنسيق التاريخ والوقت"""
        if datetime_obj is None:
            return ""
        try:
            if hasattr(datetime_obj, 'strftime'):
                return datetime_obj.strftime('%Y-%m-%d %H:%M')
            return str(datetime_obj)
        except:
            return ""

    return app

# إنشاء قاعدة البيانات وتشغيل التطبيق
def create_db(app):
    """إنشاء جداول قاعدة البيانات"""
    with app.app_context():
        try:
            db.create_all()
            logger.info("Database tables created successfully.")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise

# تشغيل التطبيق
if __name__ == '__main__':
    try:
        print("🚀 تشغيل نظام SmartBiz...")
        app = create_app()
        create_db(app)
        print("✅ التطبيق جاهز!")
        print("🌐 الرابط: http://127.0.0.1:5000")
        print("🔧 للإيقاف: اضغط Ctrl+C")
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()