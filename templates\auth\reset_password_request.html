{% extends "base.html" %}

{% block title %}طلب إعادة تعيين كلمة المرور - نظام المحاسبة الذكي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-warning text-white text-center py-3">
                    <h4 class="mb-0">إعادة تعيين كلمة المرور</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="SmartBiz Accounting" height="80">
                        <p class="text-muted mt-2">أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور</p>
                    </div>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST" action="{{ url_for('auth.reset_password_request') }}">
                        {{ form.hidden_tag() }}
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-warning btn-lg") }}
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            <a href="{{ url_for('auth.login') }}" class="text-primary">
                                <i class="fas fa-arrow-left me-1"></i> العودة لتسجيل الدخول
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني المسجل.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const emailInput = document.getElementById('email');
    
    form.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();
        
        if (!email) {
            e.preventDefault();
            alert('يرجى إدخال البريد الإلكتروني');
            emailInput.focus();
            return;
        }
        
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            emailInput.focus();
            return;
        }
    });
    
    // Auto-focus on email input
    emailInput.focus();
});
</script>
{% endblock %}
